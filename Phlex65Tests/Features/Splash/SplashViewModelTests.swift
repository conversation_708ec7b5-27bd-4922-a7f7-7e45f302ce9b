//
//  SplashViewModelTests.swift
//  Phlex65Tests
//
//  Created by TKxel on 27/05/2025.
//

import XCTest
@testable import Phlex65

@MainActor
final class SplashViewModelTests: XCTestCase {
    
    var viewModel: SplashViewModel!
    var completionCalled: Bool = false
    
    override func setUp() {
        super.setUp()
        completionCalled = false
    }
    
    override func tearDown() {
        viewModel = nil
        super.tearDown()
    }
    
    func testInitialState() {
        // Given
        let config = SplashConfiguration.default
        
        // When
        viewModel = SplashViewModel(configuration: config) {
            self.completionCalled = true
        }
        
        // Then
        XCTAssertEqual(viewModel.logoScale, config.initialScale)
        XCTAssertEqual(viewModel.logoOpacity, config.initialOpacity)
        XCTAssertFalse(viewModel.isAnimationComplete)
        XCTAssertFalse(completionCalled)
    }
    
    func testSkipAnimation() {
        // Given
        viewModel = SplashViewModel(configuration: .default) {
            self.completionCalled = true
        }
        
        // When
        viewModel.skipAnimation()
        
        // Then
        XCTAssertTrue(viewModel.isAnimationComplete)
        XCTAssertTrue(completionCalled)
    }
    
    func testConfigurationProperties() {
        // Given
        let config = SplashConfiguration.fast
        
        // When
        viewModel = SplashViewModel(configuration: config) {
            self.completionCalled = true
        }
        
        // Then
        XCTAssertEqual(viewModel.backgroundColor, config.backgroundColor)
        XCTAssertEqual(viewModel.maxLogoWidth, config.maxLogoWidth)
    }
}
