// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		96E873B12DE5D00000F5D013 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 96E873B02DE5D00000F5D013 /* Alamofire */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		96E873942DE5C8BB00F5D013 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 96E8737D2DE5C8B700F5D013 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 96E873842DE5C8B700F5D013;
			remoteInfo = Phlex65;
		};
		96E8739E2DE5C8BB00F5D013 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 96E8737D2DE5C8B700F5D013 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 96E873842DE5C8B700F5D013;
			remoteInfo = Phlex65;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		96E873852DE5C8B700F5D013 /* Phlex65.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Phlex65.app; sourceTree = BUILT_PRODUCTS_DIR; };
		96E873932DE5C8BB00F5D013 /* Phlex65Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Phlex65Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		96E8739D2DE5C8BB00F5D013 /* Phlex65UITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Phlex65UITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		96E873872DE5C8B700F5D013 /* Phlex65 */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Phlex65;
			sourceTree = "<group>";
		};
		96E873962DE5C8BB00F5D013 /* Phlex65Tests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Phlex65Tests;
			sourceTree = "<group>";
		};
		96E873A02DE5C8BB00F5D013 /* Phlex65UITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Phlex65UITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		96E873822DE5C8B700F5D013 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96E873B12DE5D00000F5D013 /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E873902DE5C8BB00F5D013 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E8739A2DE5C8BB00F5D013 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		96E8737C2DE5C8B700F5D013 = {
			isa = PBXGroup;
			children = (
				96E873872DE5C8B700F5D013 /* Phlex65 */,
				96E873962DE5C8BB00F5D013 /* Phlex65Tests */,
				96E873A02DE5C8BB00F5D013 /* Phlex65UITests */,
				96E873862DE5C8B700F5D013 /* Products */,
			);
			sourceTree = "<group>";
		};
		96E873862DE5C8B700F5D013 /* Products */ = {
			isa = PBXGroup;
			children = (
				96E873852DE5C8B700F5D013 /* Phlex65.app */,
				96E873932DE5C8BB00F5D013 /* Phlex65Tests.xctest */,
				96E8739D2DE5C8BB00F5D013 /* Phlex65UITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		96E873842DE5C8B700F5D013 /* Phlex65 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96E873A72DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65" */;
			buildPhases = (
				96E873812DE5C8B700F5D013 /* Sources */,
				96E873822DE5C8B700F5D013 /* Frameworks */,
				96E873832DE5C8B700F5D013 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				96E873872DE5C8B700F5D013 /* Phlex65 */,
			);
			name = Phlex65;
			packageProductDependencies = (
				96E873B02DE5D00000F5D013 /* Alamofire */,
			);
			productName = Phlex65;
			productReference = 96E873852DE5C8B700F5D013 /* Phlex65.app */;
			productType = "com.apple.product-type.application";
		};
		96E873922DE5C8BB00F5D013 /* Phlex65Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96E873AA2DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65Tests" */;
			buildPhases = (
				96E8738F2DE5C8BB00F5D013 /* Sources */,
				96E873902DE5C8BB00F5D013 /* Frameworks */,
				96E873912DE5C8BB00F5D013 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				96E873952DE5C8BB00F5D013 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				96E873962DE5C8BB00F5D013 /* Phlex65Tests */,
			);
			name = Phlex65Tests;
			packageProductDependencies = (
			);
			productName = Phlex65Tests;
			productReference = 96E873932DE5C8BB00F5D013 /* Phlex65Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		96E8739C2DE5C8BB00F5D013 /* Phlex65UITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 96E873AD2DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65UITests" */;
			buildPhases = (
				96E873992DE5C8BB00F5D013 /* Sources */,
				96E8739A2DE5C8BB00F5D013 /* Frameworks */,
				96E8739B2DE5C8BB00F5D013 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				96E8739F2DE5C8BB00F5D013 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				96E873A02DE5C8BB00F5D013 /* Phlex65UITests */,
			);
			name = Phlex65UITests;
			packageProductDependencies = (
			);
			productName = Phlex65UITests;
			productReference = 96E8739D2DE5C8BB00F5D013 /* Phlex65UITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		96E8737D2DE5C8B700F5D013 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					96E873842DE5C8B700F5D013 = {
						CreatedOnToolsVersion = 16.4;
					};
					96E873922DE5C8BB00F5D013 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 96E873842DE5C8B700F5D013;
					};
					96E8739C2DE5C8BB00F5D013 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 96E873842DE5C8B700F5D013;
					};
				};
			};
			buildConfigurationList = 96E873802DE5C8B700F5D013 /* Build configuration list for PBXProject "Phlex65" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 96E8737C2DE5C8B700F5D013;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				96E873AF2DE5D00000F5D013 /* XCRemoteSwiftPackageReference "Alamofire" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 96E873862DE5C8B700F5D013 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				96E873842DE5C8B700F5D013 /* Phlex65 */,
				96E873922DE5C8BB00F5D013 /* Phlex65Tests */,
				96E8739C2DE5C8BB00F5D013 /* Phlex65UITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		96E873832DE5C8B700F5D013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E873912DE5C8BB00F5D013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E8739B2DE5C8BB00F5D013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		96E873812DE5C8B700F5D013 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E8738F2DE5C8BB00F5D013 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		96E873992DE5C8BB00F5D013 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		96E873952DE5C8BB00F5D013 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 96E873842DE5C8B700F5D013 /* Phlex65 */;
			targetProxy = 96E873942DE5C8BB00F5D013 /* PBXContainerItemProxy */;
		};
		96E8739F2DE5C8BB00F5D013 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 96E873842DE5C8B700F5D013 /* Phlex65 */;
			targetProxy = 96E8739E2DE5C8BB00F5D013 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		96E873A52DE5C8BB00F5D013 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		96E873A62DE5C8BB00F5D013 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		96E873A82DE5C8BB00F5D013 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Phlex65/Core/Configuration/Phlex65.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs access to camera to take profile pictures.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app needs access to save photos to your photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs access to photo library to select profile pictures.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchStoryboardName[sdk=iphoneos*]" = LaunchScreen;
				"INFOPLIST_KEY_UILaunchStoryboardName[sdk=iphonesimulator*]" = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		96E873A92DE5C8BB00F5D013 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Phlex65/Core/Configuration/Phlex65.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs access to camera to take profile pictures.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "This app needs access to save photos to your photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs access to photo library to select profile pictures.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchStoryboardName[sdk=iphoneos*]" = LaunchScreen;
				"INFOPLIST_KEY_UILaunchStoryboardName[sdk=iphonesimulator*]" = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		96E873AB2DE5C8BB00F5D013 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Phlex65.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Phlex65";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		96E873AC2DE5C8BB00F5D013 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65Tests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Phlex65.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Phlex65";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		96E873AE2DE5C8BB00F5D013 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Phlex65;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		96E873AF2DE5C8BB00F5D013 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.phlex65.caregiver.Phlex65UITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Phlex65;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		96E873802DE5C8B700F5D013 /* Build configuration list for PBXProject "Phlex65" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96E873A52DE5C8BB00F5D013 /* Debug */,
				96E873A62DE5C8BB00F5D013 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96E873A72DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96E873A82DE5C8BB00F5D013 /* Debug */,
				96E873A92DE5C8BB00F5D013 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96E873AA2DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96E873AB2DE5C8BB00F5D013 /* Debug */,
				96E873AC2DE5C8BB00F5D013 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		96E873AD2DE5C8BB00F5D013 /* Build configuration list for PBXNativeTarget "Phlex65UITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				96E873AE2DE5C8BB00F5D013 /* Debug */,
				96E873AF2DE5C8BB00F5D013 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		96E873AF2DE5D00000F5D013 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.8.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		96E873B02DE5D00000F5D013 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 96E873AF2DE5D00000F5D013 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 96E8737D2DE5C8B700F5D013 /* Project object */;
}
