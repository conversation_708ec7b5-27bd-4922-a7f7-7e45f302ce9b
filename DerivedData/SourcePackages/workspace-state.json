{"object": {"artifacts": [], "dependencies": [{"basedOn": null, "packageRef": {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "name": "Alamofire"}, "state": {"checkoutState": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}, "name": "sourceControlCheckout"}, "subpath": "Alamofire"}], "prebuilts": []}, "version": 7}