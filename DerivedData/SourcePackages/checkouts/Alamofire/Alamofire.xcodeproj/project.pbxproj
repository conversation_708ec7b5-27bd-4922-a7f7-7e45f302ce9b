// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		3106FB6123F8C53A007FAB43 /* ProtectedTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */; };
		3106FB6223F8C53A007FAB43 /* ProtectedTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */; };
		3106FB6323F8C53A007FAB43 /* ProtectedTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */; };
		3106FB6523F8D9E0007FAB43 /* DataStreamTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */; };
		3106FB6623F8D9E0007FAB43 /* DataStreamTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */; };
		3106FB6723F8D9E0007FAB43 /* DataStreamTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */; };
		3107EA3520A11AE100445260 /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */; };
		3107EA3620A11AE100445260 /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */; };
		3107EA3720A11AE200445260 /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */; };
		3107EA3820A11F9600445260 /* ResponseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */; };
		3107EA3920A11F9600445260 /* ResponseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */; };
		3107EA3A20A11F9700445260 /* ResponseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */; };
		3107EA3F20A1267C00445260 /* SessionDelegateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */; };
		3107EA4020A1267C00445260 /* SessionDelegateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */; };
		3107EA4120A1267D00445260 /* SessionDelegateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */; };
		3111CE8420A7636E008315E2 /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8D1C6F419D52968002E74FE /* SessionTests.swift */; };
		3111CE8520A7636F008315E2 /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8D1C6F419D52968002E74FE /* SessionTests.swift */; };
		3111CE8620A76370008315E2 /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8D1C6F419D52968002E74FE /* SessionTests.swift */; };
		3111CE8820A77843008315E2 /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3111CE8720A77843008315E2 /* EventMonitor.swift */; };
		3111CE8920A77944008315E2 /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3111CE8720A77843008315E2 /* EventMonitor.swift */; };
		3111CE8A20A77945008315E2 /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3111CE8720A77843008315E2 /* EventMonitor.swift */; };
		3111CE8B20A77945008315E2 /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3111CE8720A77843008315E2 /* EventMonitor.swift */; };
		3111CE8C20A7EBE6008315E2 /* MultipartFormDataTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */; };
		3111CE8D20A7EBE7008315E2 /* MultipartFormDataTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */; };
		3111CE8E20A7EBE7008315E2 /* MultipartFormDataTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */; };
		3111CE8F20A7EC26008315E2 /* NetworkReachabilityManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */; };
		3111CE9020A7EC27008315E2 /* NetworkReachabilityManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */; };
		3111CE9120A7EC27008315E2 /* NetworkReachabilityManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */; };
		3111CE9220A7EC30008315E2 /* ResponseSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */; };
		3111CE9320A7EC31008315E2 /* ResponseSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */; };
		3111CE9420A7EC32008315E2 /* ResponseSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */; };
		3111CE9520A7EC39008315E2 /* ServerTrustEvaluatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */; };
		3111CE9620A7EC3A008315E2 /* ServerTrustEvaluatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */; };
		3111CE9720A7EC3A008315E2 /* ServerTrustEvaluatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */; };
		3111CE9B20A7EC57008315E2 /* URLProtocolTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */; };
		3111CE9C20A7EC58008315E2 /* URLProtocolTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */; };
		3111CE9D20A7EC58008315E2 /* URLProtocolTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */; };
		3113D46B21878227001CCD21 /* HTTPHeadersTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */; };
		3113D46C21878227001CCD21 /* HTTPHeadersTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */; };
		3113D46D21878227001CCD21 /* HTTPHeadersTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */; };
		31181E122794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */; };
		31181E132794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */; };
		31181E142794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */; };
		31181E152794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */; };
		31181E302794FF9600E88600 /* certPEM.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E172794FF9600E88600 /* certPEM.cer */; };
		31181E312794FF9600E88600 /* certPEM.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E172794FF9600E88600 /* certPEM.cer */; };
		31181E322794FF9600E88600 /* certPEM.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E172794FF9600E88600 /* certPEM.cer */; };
		31181E332794FF9600E88600 /* certPEM.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E172794FF9600E88600 /* certPEM.cer */; };
		31181E342794FF9600E88600 /* randomGibberish.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E182794FF9600E88600 /* randomGibberish.crt */; };
		31181E352794FF9600E88600 /* randomGibberish.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E182794FF9600E88600 /* randomGibberish.crt */; };
		31181E362794FF9600E88600 /* randomGibberish.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E182794FF9600E88600 /* randomGibberish.crt */; };
		31181E372794FF9600E88600 /* randomGibberish.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E182794FF9600E88600 /* randomGibberish.crt */; };
		31181E382794FF9600E88600 /* certDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E192794FF9600E88600 /* certDER.der */; };
		31181E392794FF9600E88600 /* certDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E192794FF9600E88600 /* certDER.der */; };
		31181E3A2794FF9600E88600 /* certDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E192794FF9600E88600 /* certDER.der */; };
		31181E3B2794FF9600E88600 /* certDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E192794FF9600E88600 /* certDER.der */; };
		31181E3C2794FF9600E88600 /* keyDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1A2794FF9600E88600 /* keyDER.der */; };
		31181E3D2794FF9600E88600 /* keyDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1A2794FF9600E88600 /* keyDER.der */; };
		31181E3E2794FF9600E88600 /* keyDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1A2794FF9600E88600 /* keyDER.der */; };
		31181E3F2794FF9600E88600 /* keyDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1A2794FF9600E88600 /* keyDER.der */; };
		31181E402794FF9600E88600 /* certDER.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1B2794FF9600E88600 /* certDER.cer */; };
		31181E412794FF9600E88600 /* certDER.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1B2794FF9600E88600 /* certDER.cer */; };
		31181E422794FF9600E88600 /* certDER.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1B2794FF9600E88600 /* certDER.cer */; };
		31181E432794FF9600E88600 /* certDER.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1B2794FF9600E88600 /* certDER.cer */; };
		31181E442794FF9600E88600 /* certPEM.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1C2794FF9600E88600 /* certPEM.crt */; };
		31181E452794FF9600E88600 /* certPEM.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1C2794FF9600E88600 /* certPEM.crt */; };
		31181E462794FF9600E88600 /* certPEM.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1C2794FF9600E88600 /* certPEM.crt */; };
		31181E472794FF9600E88600 /* certPEM.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1C2794FF9600E88600 /* certPEM.crt */; };
		31181E482794FF9600E88600 /* certDER.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1D2794FF9600E88600 /* certDER.crt */; };
		31181E492794FF9600E88600 /* certDER.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1D2794FF9600E88600 /* certDER.crt */; };
		31181E4A2794FF9600E88600 /* certDER.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1D2794FF9600E88600 /* certDER.crt */; };
		31181E4B2794FF9600E88600 /* certDER.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1D2794FF9600E88600 /* certDER.crt */; };
		31181E4C2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */; };
		31181E4D2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */; };
		31181E4E2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */; };
		31181E4F2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */; };
		31181E502794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */; };
		31181E512794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */; };
		31181E522794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */; };
		31181E532794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */; };
		31181E542794FF9600E88600 /* signed-by-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E212794FF9600E88600 /* signed-by-ca2.cer */; };
		31181E552794FF9600E88600 /* signed-by-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E212794FF9600E88600 /* signed-by-ca2.cer */; };
		31181E562794FF9600E88600 /* signed-by-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E212794FF9600E88600 /* signed-by-ca2.cer */; };
		31181E572794FF9600E88600 /* signed-by-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E212794FF9600E88600 /* signed-by-ca2.cer */; };
		31181E582794FF9600E88600 /* signed-by-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E222794FF9600E88600 /* signed-by-ca1.cer */; };
		31181E592794FF9600E88600 /* signed-by-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E222794FF9600E88600 /* signed-by-ca1.cer */; };
		31181E5A2794FF9600E88600 /* signed-by-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E222794FF9600E88600 /* signed-by-ca1.cer */; };
		31181E5B2794FF9600E88600 /* signed-by-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E222794FF9600E88600 /* signed-by-ca1.cer */; };
		31181E5C2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */; };
		31181E5D2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */; };
		31181E5E2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */; };
		31181E5F2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */; };
		31181E602794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */; };
		31181E612794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */; };
		31181E622794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */; };
		31181E632794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */; };
		31181E642794FF9600E88600 /* test.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E252794FF9600E88600 /* test.alamofire.org.cer */; };
		31181E652794FF9600E88600 /* test.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E252794FF9600E88600 /* test.alamofire.org.cer */; };
		31181E662794FF9600E88600 /* test.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E252794FF9600E88600 /* test.alamofire.org.cer */; };
		31181E672794FF9600E88600 /* test.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E252794FF9600E88600 /* test.alamofire.org.cer */; };
		31181E682794FF9600E88600 /* alamofire-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E262794FF9600E88600 /* alamofire-root-ca.cer */; };
		31181E692794FF9600E88600 /* alamofire-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E262794FF9600E88600 /* alamofire-root-ca.cer */; };
		31181E6A2794FF9600E88600 /* alamofire-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E262794FF9600E88600 /* alamofire-root-ca.cer */; };
		31181E6B2794FF9600E88600 /* alamofire-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E262794FF9600E88600 /* alamofire-root-ca.cer */; };
		31181E6C2794FF9600E88600 /* valid-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E272794FF9600E88600 /* valid-uri.cer */; };
		31181E6D2794FF9600E88600 /* valid-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E272794FF9600E88600 /* valid-uri.cer */; };
		31181E6E2794FF9600E88600 /* valid-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E272794FF9600E88600 /* valid-uri.cer */; };
		31181E6F2794FF9600E88600 /* valid-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E272794FF9600E88600 /* valid-uri.cer */; };
		31181E702794FF9600E88600 /* multiple-dns-names.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E282794FF9600E88600 /* multiple-dns-names.cer */; };
		31181E712794FF9600E88600 /* multiple-dns-names.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E282794FF9600E88600 /* multiple-dns-names.cer */; };
		31181E722794FF9600E88600 /* multiple-dns-names.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E282794FF9600E88600 /* multiple-dns-names.cer */; };
		31181E732794FF9600E88600 /* multiple-dns-names.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E282794FF9600E88600 /* multiple-dns-names.cer */; };
		31181E742794FF9600E88600 /* expired.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E292794FF9600E88600 /* expired.cer */; };
		31181E752794FF9600E88600 /* expired.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E292794FF9600E88600 /* expired.cer */; };
		31181E762794FF9600E88600 /* expired.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E292794FF9600E88600 /* expired.cer */; };
		31181E772794FF9600E88600 /* expired.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E292794FF9600E88600 /* expired.cer */; };
		31181E782794FF9600E88600 /* valid-dns-name.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2A2794FF9600E88600 /* valid-dns-name.cer */; };
		31181E792794FF9600E88600 /* valid-dns-name.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2A2794FF9600E88600 /* valid-dns-name.cer */; };
		31181E7A2794FF9600E88600 /* valid-dns-name.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2A2794FF9600E88600 /* valid-dns-name.cer */; };
		31181E7B2794FF9600E88600 /* valid-dns-name.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2A2794FF9600E88600 /* valid-dns-name.cer */; };
		31181E7C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */; };
		31181E7D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */; };
		31181E7E2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */; };
		31181E7F2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */; };
		31181E802794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */; };
		31181E812794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */; };
		31181E822794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */; };
		31181E832794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */; };
		31181E842794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */; };
		31181E852794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */; };
		31181E862794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */; };
		31181E872794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */; };
		31181E882794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */; };
		31181E892794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */; };
		31181E8A2794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */; };
		31181E8B2794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */; };
		311A89BF23185BBF003BB714 /* CachedResponseHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */; };
		311A89C023185BBF003BB714 /* CachedResponseHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */; };
		311A89C123185BC0003BB714 /* CachedResponseHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */; };
		311B199020B0D3B40036823B /* MultipartUpload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 311B198F20B0D3B40036823B /* MultipartUpload.swift */; };
		311B199120B0E3470036823B /* MultipartUpload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 311B198F20B0D3B40036823B /* MultipartUpload.swift */; };
		311B199220B0E3480036823B /* MultipartUpload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 311B198F20B0D3B40036823B /* MultipartUpload.swift */; };
		311B199320B0E3480036823B /* MultipartUpload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 311B198F20B0D3B40036823B /* MultipartUpload.swift */; };
		311B199420B0ED980036823B /* UploadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5F19A9674D0040E7D1 /* UploadTests.swift */; };
		311B199520B0ED980036823B /* UploadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5F19A9674D0040E7D1 /* UploadTests.swift */; };
		311B199620B0ED990036823B /* UploadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5F19A9674D0040E7D1 /* UploadTests.swift */; };
		3129306A263E17D600473CEA /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E4202FE01B667AA100C997FB /* Alamofire.framework */; };
		31293070263E183500473CEA /* BaseTestCase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A501B096C2C0065714F /* BaseTestCase.swift */; };
		31293071263E183800473CEA /* LeaksTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31762DC9247738FA0025C704 /* LeaksTests.swift */; };
		31293072263E183800473CEA /* TestHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727421218BB9A50039FFCC /* TestHelpers.swift */; };
		31293073263E183800473CEA /* NSLoggingEventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */; };
		31293074263E183C00473CEA /* RequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5D19A9674D0040E7D1 /* RequestTests.swift */; };
		31293075263E183C00473CEA /* SessionDelegateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */; };
		31293076263E183C00473CEA /* DownloadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */; };
		31293077263E183C00473CEA /* ResponseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */; };
		31293078263E183C00473CEA /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8D1C6F419D52968002E74FE /* SessionTests.swift */; };
		31293079263E183C00473CEA /* InternalRequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31425AC0241F098000EE3CCC /* InternalRequestTests.swift */; };
		3129307A263E183C00473CEA /* UploadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5F19A9674D0040E7D1 /* UploadTests.swift */; };
		3129307B263E183C00473CEA /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */; };
		3129307C263E183C00473CEA /* ParameterEncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31501E872196962A005829F2 /* ParameterEncoderTests.swift */; };
		3129307D263E183C00473CEA /* DataStreamTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */; };
		3129307E263E183C00473CEA /* RequestModifierTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B51E8B2434FECB005356DB /* RequestModifierTests.swift */; };
		3129307F263E183C00473CEA /* ParameterEncodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */; };
		31293080263E184000473CEA /* AFError+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */; };
		31293081263E184000473CEA /* FileManager+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */; };
		31293082263E184500473CEA /* ProtectedTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */; };
		31293083263E184500473CEA /* NetworkReachabilityManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */; };
		31293084263E184500473CEA /* RetryPolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */; };
		31293085263E184500473CEA /* HTTPHeadersTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */; };
		31293086263E184500473CEA /* CombineTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */; };
		31293087263E184500473CEA /* RedirectHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */; };
		31293088263E184500473CEA /* RequestInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */; };
		31293089263E184500473CEA /* ServerTrustEvaluatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */; };
		3129308A263E184500473CEA /* AuthenticationInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */; };
		3129308B263E184500473CEA /* CacheTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C341BB91B1A865A00C1B34D /* CacheTests.swift */; };
		3129308C263E184500473CEA /* ResponseSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */; };
		3129308D263E184500473CEA /* ValidationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */; };
		3129308E263E184500473CEA /* CachedResponseHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */; };
		3129308F263E184500473CEA /* MultipartFormDataTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */; };
		31293090263E184500473CEA /* TLSEvaluationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */; };
		31293091263E184500473CEA /* URLProtocolTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */; };
		31293092263E184900473CEA /* Result+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */; };
		312930AA263E187200473CEA /* unicorn.png in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1241B5207DB00873DFF /* unicorn.png */; };
		312930AB263E187200473CEA /* rainbow.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1231B5207DB00873DFF /* rainbow.jpg */; };
		312930AC263E187500473CEA /* empty_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EA1D7D2FA20056F249 /* empty_data.json */; };
		312930AD263E187500473CEA /* valid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EC1D7D2FA20056F249 /* valid_data.json */; };
		312930AE263E187500473CEA /* invalid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EB1D7D2FA20056F249 /* invalid_data.json */; };
		312930AF263E187800473CEA /* empty_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F21D7D2FA20056F249 /* empty_string.txt */; };
		312930B0263E187800473CEA /* utf8_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F41D7D2FA20056F249 /* utf8_string.txt */; };
		312930B1263E187800473CEA /* utf32_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F31D7D2FA20056F249 /* utf32_string.txt */; };
		312FC4FF2CB079E800E48EAB /* InternalHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */; };
		312FC5002CB079E800E48EAB /* InternalHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */; };
		312FC5012CB079E800E48EAB /* InternalHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */; };
		312FC5022CB079E800E48EAB /* InternalHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */; };
		312FC5032CB079E800E48EAB /* InternalHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */; };
		31425AC1241F098000EE3CCC /* InternalRequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31425AC0241F098000EE3CCC /* InternalRequestTests.swift */; };
		31425AC2241F098000EE3CCC /* InternalRequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31425AC0241F098000EE3CCC /* InternalRequestTests.swift */; };
		31425AC3241F098000EE3CCC /* InternalRequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31425AC0241F098000EE3CCC /* InternalRequestTests.swift */; };
		3145E0EA2797DA4200949557 /* ConcurrencyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */; };
		314998EA27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 314998E927A6560600ABB856 /* Request+AlamofireTests.swift */; };
		314998EB27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 314998E927A6560600ABB856 /* Request+AlamofireTests.swift */; };
		314998EC27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 314998E927A6560600ABB856 /* Request+AlamofireTests.swift */; };
		314998ED27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 314998E927A6560600ABB856 /* Request+AlamofireTests.swift */; };
		31501E882196962A005829F2 /* ParameterEncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31501E872196962A005829F2 /* ParameterEncoderTests.swift */; };
		31501E892196962A005829F2 /* ParameterEncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31501E872196962A005829F2 /* ParameterEncoderTests.swift */; };
		31501E8A2196962A005829F2 /* ParameterEncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31501E872196962A005829F2 /* ParameterEncoderTests.swift */; };
		315A4C56241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */; };
		315A4C57241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */; };
		315A4C58241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */; };
		315A4C59241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */; };
		3165407329AEBC0400C9BE08 /* RequestCompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3165407229AEBC0400C9BE08 /* RequestCompression.swift */; };
		3165407429AEBC0400C9BE08 /* RequestCompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3165407229AEBC0400C9BE08 /* RequestCompression.swift */; };
		3165407529AEBC0400C9BE08 /* RequestCompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3165407229AEBC0400C9BE08 /* RequestCompression.swift */; };
		3165407629AEBC0400C9BE08 /* RequestCompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3165407229AEBC0400C9BE08 /* RequestCompression.swift */; };
		31727418218BAEC90039FFCC /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727417218BAEC90039FFCC /* HTTPMethod.swift */; };
		31727419218BAEC90039FFCC /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727417218BAEC90039FFCC /* HTTPMethod.swift */; };
		3172741A218BAEC90039FFCC /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727417218BAEC90039FFCC /* HTTPMethod.swift */; };
		3172741B218BAEC90039FFCC /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727417218BAEC90039FFCC /* HTTPMethod.swift */; };
		3172741D218BB1790039FFCC /* ParameterEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3172741C218BB1790039FFCC /* ParameterEncoder.swift */; };
		3172741E218BB1790039FFCC /* ParameterEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3172741C218BB1790039FFCC /* ParameterEncoder.swift */; };
		3172741F218BB1790039FFCC /* ParameterEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3172741C218BB1790039FFCC /* ParameterEncoder.swift */; };
		31727420218BB1790039FFCC /* ParameterEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3172741C218BB1790039FFCC /* ParameterEncoder.swift */; };
		31727422218BB9A50039FFCC /* TestHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727421218BB9A50039FFCC /* TestHelpers.swift */; };
		31727423218BB9A50039FFCC /* TestHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727421218BB9A50039FFCC /* TestHelpers.swift */; };
		31727424218BB9A50039FFCC /* TestHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727421218BB9A50039FFCC /* TestHelpers.swift */; };
		317338CB2A43A51100D4EA0A /* Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = F897FF4019AA800700AB5182 /* Alamofire.swift */; };
		317338CC2A43A51100D4EA0A /* AFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C1DC8531B68908E00476DE3 /* AFError.swift */; };
		317338CD2A43A51100D4EA0A /* HTTPHeaders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A9209CDCB000103A19 /* HTTPHeaders.swift */; };
		317338CE2A43A51100D4EA0A /* HTTPMethod.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727417218BAEC90039FFCC /* HTTPMethod.swift */; };
		317338CF2A43A51100D4EA0A /* Notifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB928281C66BFBC00CE5F08 /* Notifications.swift */; };
		317338D02A43A51100D4EA0A /* ParameterEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3172741C218BB1790039FFCC /* ParameterEncoder.swift */; };
		317338D12A43A51100D4EA0A /* ParameterEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */; };
		317338D22A43A51100D4EA0A /* Protected.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3191B5741F5F53A6003960A8 /* Protected.swift */; };
		317338D32A43A51100D4EA0A /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991790209CDA7F00103A19 /* Request.swift */; };
		317338D42A43A51100D4EA0A /* RequestTaskMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A4209CDAC400103A19 /* RequestTaskMap.swift */; };
		317338D52A43A51100D4EA0A /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991791209CDA7F00103A19 /* Response.swift */; };
		317338D62A43A51100D4EA0A /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991792209CDA7F00103A19 /* Session.swift */; };
		317338D72A43A51100D4EA0A /* SessionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991793209CDA7F00103A19 /* SessionDelegate.swift */; };
		317338D82A43A51100D4EA0A /* URLConvertible+URLRequestConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */; };
		317338D92A43A51100D4EA0A /* DispatchQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */; };
		317338DA2A43A51100D4EA0A /* OperationQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */; };
		317338DB2A43A51100D4EA0A /* Result+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */; };
		317338DC2A43A51100D4EA0A /* StringEncoding+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */; };
		317338DD2A43A51100D4EA0A /* URLRequest+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */; };
		317338DE2A43A51100D4EA0A /* URLSessionConfiguration+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */; };
		317338DF2A43A51100D4EA0A /* AlamofireExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DADDFA224811ED0051390F /* AlamofireExtended.swift */; };
		317338E02A43A51100D4EA0A /* AuthenticationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */; };
		317338E12A43A51100D4EA0A /* CachedResponseHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */; };
		317338E22A43A51100D4EA0A /* Combine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318DD40E2439780500963291 /* Combine.swift */; };
		317338E32A43A51100D4EA0A /* Concurrency.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE3A25C11CEA00760641 /* Concurrency.swift */; };
		317338E42A43A51100D4EA0A /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3111CE8720A77843008315E2 /* EventMonitor.swift */; };
		317338E52A43A51100D4EA0A /* MultipartFormData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */; };
		317338E62A43A51100D4EA0A /* MultipartUpload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 311B198F20B0D3B40036823B /* MultipartUpload.swift */; };
		317338E72A43A51100D4EA0A /* NetworkReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */; };
		317338E82A43A51100D4EA0A /* RedirectHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB645220CA8A400604EDC /* RedirectHandler.swift */; };
		317338E92A43A51100D4EA0A /* RequestCompression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3165407229AEBC0400C9BE08 /* RequestCompression.swift */; };
		317338EA2A43A51100D4EA0A /* RequestInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */; };
		317338EB2A43A51100D4EA0A /* ResponseSerialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */; };
		317338EC2A43A51100D4EA0A /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */; };
		317338ED2A43A51100D4EA0A /* ServerTrustEvaluation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */; };
		317338EE2A43A51100D4EA0A /* URLEncodedFormEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */; };
		317338EF2A43A51100D4EA0A /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C421AF89F0900BABAE5 /* Validation.swift */; };
		317338F82A43BE5F00D4EA0A /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 317338C42A43A4FA00D4EA0A /* Alamofire.framework */; };
		317338FE2A43BE9000D4EA0A /* BaseTestCase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A501B096C2C0065714F /* BaseTestCase.swift */; };
		317338FF2A43BE9000D4EA0A /* LeaksTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31762DC9247738FA0025C704 /* LeaksTests.swift */; };
		317339002A43BE9000D4EA0A /* NSLoggingEventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */; };
		317339012A43BE9000D4EA0A /* TestHelpers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31727421218BB9A50039FFCC /* TestHelpers.swift */; };
		317339022A43BE9000D4EA0A /* AuthenticationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */; };
		317339032A43BE9000D4EA0A /* DataStreamTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */; };
		317339042A43BE9000D4EA0A /* DownloadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */; };
		317339052A43BE9000D4EA0A /* InternalRequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31425AC0241F098000EE3CCC /* InternalRequestTests.swift */; };
		317339062A43BE9000D4EA0A /* ParameterEncoderTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31501E872196962A005829F2 /* ParameterEncoderTests.swift */; };
		317339072A43BE9000D4EA0A /* ParameterEncodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */; };
		317339082A43BE9000D4EA0A /* RequestModifierTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B51E8B2434FECB005356DB /* RequestModifierTests.swift */; };
		317339092A43BE9000D4EA0A /* RequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5D19A9674D0040E7D1 /* RequestTests.swift */; };
		3173390A2A43BE9000D4EA0A /* ResponseTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */; };
		3173390B2A43BE9000D4EA0A /* SessionDelegateTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */; };
		3173390C2A43BE9000D4EA0A /* SessionTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8D1C6F419D52968002E74FE /* SessionTests.swift */; };
		3173390D2A43BE9000D4EA0A /* UploadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5F19A9674D0040E7D1 /* UploadTests.swift */; };
		3173390E2A43BE9000D4EA0A /* AFError+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */; };
		3173390F2A43BE9000D4EA0A /* Bundle+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */; };
		317339102A43BE9000D4EA0A /* FileManager+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */; };
		317339112A43BE9000D4EA0A /* Request+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 314998E927A6560600ABB856 /* Request+AlamofireTests.swift */; };
		317339122A43BE9000D4EA0A /* Result+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */; };
		317339132A43BE9000D4EA0A /* AuthenticationInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */; };
		317339142A43BE9000D4EA0A /* CachedResponseHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */; };
		317339152A43BE9000D4EA0A /* CacheTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C341BB91B1A865A00C1B34D /* CacheTests.swift */; };
		317339162A43BE9000D4EA0A /* CombineTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */; };
		317339172A43BE9000D4EA0A /* ConcurrencyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */; };
		317339182A43BE9000D4EA0A /* HTTPHeadersTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */; };
		317339192A43BE9000D4EA0A /* MultipartFormDataTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */; };
		3173391A2A43BE9000D4EA0A /* NetworkReachabilityManagerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */; };
		3173391B2A43BE9000D4EA0A /* ProtectedTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */; };
		3173391C2A43BE9000D4EA0A /* RedirectHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */; };
		3173391D2A43BE9000D4EA0A /* RequestInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */; };
		3173391E2A43BE9000D4EA0A /* ResponseSerializationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */; };
		3173391F2A43BE9000D4EA0A /* RetryPolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */; };
		317339202A43BE9000D4EA0A /* ServerTrustEvaluatorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */; };
		317339212A43BE9000D4EA0A /* TLSEvaluationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */; };
		317339222A43BE9000D4EA0A /* URLProtocolTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */; };
		317339232A43BE9000D4EA0A /* ValidationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */; };
		317339242A43BEAC00D4EA0A /* alamofire-signing-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */; };
		317339252A43BEAC00D4EA0A /* alamofire-signing-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */; };
		317339262A43BEAC00D4EA0A /* signed-by-ca2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E212794FF9600E88600 /* signed-by-ca2.cer */; };
		317339272A43BEAC00D4EA0A /* signed-by-ca1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E222794FF9600E88600 /* signed-by-ca1.cer */; };
		317339282A43BEAC00D4EA0A /* missing-dns-name-and-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */; };
		317339292A43BEAC00D4EA0A /* wildcard.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */; };
		3173392A2A43BEAC00D4EA0A /* test.alamofire.org.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E252794FF9600E88600 /* test.alamofire.org.cer */; };
		3173392B2A43BEAC00D4EA0A /* alamofire-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E262794FF9600E88600 /* alamofire-root-ca.cer */; };
		3173392C2A43BEAC00D4EA0A /* valid-uri.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E272794FF9600E88600 /* valid-uri.cer */; };
		3173392D2A43BEAC00D4EA0A /* multiple-dns-names.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E282794FF9600E88600 /* multiple-dns-names.cer */; };
		3173392E2A43BEAC00D4EA0A /* expired.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E292794FF9600E88600 /* expired.cer */; };
		3173392F2A43BEAC00D4EA0A /* valid-dns-name.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2A2794FF9600E88600 /* valid-dns-name.cer */; };
		317339302A43BEAC00D4EA0A /* expired.badssl.com-intermediate-ca-1.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */; };
		317339312A43BEAC00D4EA0A /* expired.badssl.com-intermediate-ca-2.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */; };
		317339322A43BEAC00D4EA0A /* expired.badssl.com-root-ca.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */; };
		317339332A43BEAC00D4EA0A /* expired.badssl.com-leaf.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */; };
		317339342A43BEAC00D4EA0A /* certPEM.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E172794FF9600E88600 /* certPEM.cer */; };
		317339352A43BEAC00D4EA0A /* randomGibberish.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E182794FF9600E88600 /* randomGibberish.crt */; };
		317339362A43BEAC00D4EA0A /* certDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E192794FF9600E88600 /* certDER.der */; };
		317339372A43BEAC00D4EA0A /* keyDER.der in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1A2794FF9600E88600 /* keyDER.der */; };
		317339382A43BEAC00D4EA0A /* certDER.cer in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1B2794FF9600E88600 /* certDER.cer */; };
		317339392A43BEAC00D4EA0A /* certPEM.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1C2794FF9600E88600 /* certPEM.crt */; };
		3173393A2A43BEAC00D4EA0A /* certDER.crt in Resources */ = {isa = PBXBuildFile; fileRef = 31181E1D2794FF9600E88600 /* certDER.crt */; };
		3173393B2A43BEAC00D4EA0A /* rainbow.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1231B5207DB00873DFF /* rainbow.jpg */; };
		3173393C2A43BEAC00D4EA0A /* unicorn.png in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1241B5207DB00873DFF /* unicorn.png */; };
		3173393D2A43BEAC00D4EA0A /* empty_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EA1D7D2FA20056F249 /* empty_data.json */; };
		3173393E2A43BEAC00D4EA0A /* invalid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EB1D7D2FA20056F249 /* invalid_data.json */; };
		3173393F2A43BEAC00D4EA0A /* valid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EC1D7D2FA20056F249 /* valid_data.json */; };
		317339402A43BEAC00D4EA0A /* empty_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F21D7D2FA20056F249 /* empty_string.txt */; };
		317339412A43BEAC00D4EA0A /* utf8_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F41D7D2FA20056F249 /* utf8_string.txt */; };
		317339422A43BEAC00D4EA0A /* utf32_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F31D7D2FA20056F249 /* utf32_string.txt */; };
		31762DCA247738FA0025C704 /* LeaksTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31762DC9247738FA0025C704 /* LeaksTests.swift */; };
		31762DCB247738FA0025C704 /* LeaksTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31762DC9247738FA0025C704 /* LeaksTests.swift */; };
		31762DCC247738FA0025C704 /* LeaksTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31762DC9247738FA0025C704 /* LeaksTests.swift */; };
		317A6A7620B2207F00A9FEC5 /* DownloadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */; };
		317A6A7720B2208000A9FEC5 /* DownloadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */; };
		317A6A7820B2208000A9FEC5 /* DownloadTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */; };
		318702EC2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */; };
		318702ED2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */; };
		318702EE2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */; };
		318702EF2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */; };
		318702F02B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */; };
		318702F22B0AEE3700C10A8C /* DataRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F12B0AEE3700C10A8C /* DataRequest.swift */; };
		318702F32B0AEE3700C10A8C /* DataRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F12B0AEE3700C10A8C /* DataRequest.swift */; };
		318702F42B0AEE3700C10A8C /* DataRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F12B0AEE3700C10A8C /* DataRequest.swift */; };
		318702F52B0AEE3700C10A8C /* DataRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F12B0AEE3700C10A8C /* DataRequest.swift */; };
		318702F62B0AEE3700C10A8C /* DataRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F12B0AEE3700C10A8C /* DataRequest.swift */; };
		318702F82B0AEEE400C10A8C /* UploadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F72B0AEEE400C10A8C /* UploadRequest.swift */; };
		318702F92B0AEEE400C10A8C /* UploadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F72B0AEEE400C10A8C /* UploadRequest.swift */; };
		318702FA2B0AEEE400C10A8C /* UploadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F72B0AEEE400C10A8C /* UploadRequest.swift */; };
		318702FB2B0AEEE400C10A8C /* UploadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F72B0AEEE400C10A8C /* UploadRequest.swift */; };
		318702FC2B0AEEE400C10A8C /* UploadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702F72B0AEEE400C10A8C /* UploadRequest.swift */; };
		318702FE2B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */; };
		318702FF2B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */; };
		318703002B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */; };
		318703012B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */; };
		318703022B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */; };
		318703042B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318703032B0AEF4B00C10A8C /* DownloadRequest.swift */; };
		318703052B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318703032B0AEF4B00C10A8C /* DownloadRequest.swift */; };
		318703062B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318703032B0AEF4B00C10A8C /* DownloadRequest.swift */; };
		318703072B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318703032B0AEF4B00C10A8C /* DownloadRequest.swift */; };
		318703082B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318703032B0AEF4B00C10A8C /* DownloadRequest.swift */; };
		318DD40F2439780500963291 /* Combine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318DD40E2439780500963291 /* Combine.swift */; };
		318DD4102439780500963291 /* Combine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318DD40E2439780500963291 /* Combine.swift */; };
		318DD4112439780500963291 /* Combine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318DD40E2439780500963291 /* Combine.swift */; };
		318DD4122439780500963291 /* Combine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 318DD40E2439780500963291 /* Combine.swift */; };
		3191B5751F5F53A6003960A8 /* Protected.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3191B5741F5F53A6003960A8 /* Protected.swift */; };
		3191B5761F5F53A6003960A8 /* Protected.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3191B5741F5F53A6003960A8 /* Protected.swift */; };
		3191B5771F5F53A6003960A8 /* Protected.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3191B5741F5F53A6003960A8 /* Protected.swift */; };
		3191B5781F5F53A6003960A8 /* Protected.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3191B5741F5F53A6003960A8 /* Protected.swift */; };
		31991794209CDA7F00103A19 /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991790209CDA7F00103A19 /* Request.swift */; };
		31991795209CDA7F00103A19 /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991790209CDA7F00103A19 /* Request.swift */; };
		31991796209CDA7F00103A19 /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991790209CDA7F00103A19 /* Request.swift */; };
		31991797209CDA7F00103A19 /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991790209CDA7F00103A19 /* Request.swift */; };
		31991798209CDA7F00103A19 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991791209CDA7F00103A19 /* Response.swift */; };
		31991799209CDA7F00103A19 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991791209CDA7F00103A19 /* Response.swift */; };
		3199179A209CDA7F00103A19 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991791209CDA7F00103A19 /* Response.swift */; };
		3199179B209CDA7F00103A19 /* Response.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991791209CDA7F00103A19 /* Response.swift */; };
		3199179C209CDA7F00103A19 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991792209CDA7F00103A19 /* Session.swift */; };
		3199179D209CDA7F00103A19 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991792209CDA7F00103A19 /* Session.swift */; };
		3199179E209CDA7F00103A19 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991792209CDA7F00103A19 /* Session.swift */; };
		3199179F209CDA7F00103A19 /* Session.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991792209CDA7F00103A19 /* Session.swift */; };
		319917A0209CDA7F00103A19 /* SessionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991793209CDA7F00103A19 /* SessionDelegate.swift */; };
		319917A1209CDA7F00103A19 /* SessionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991793209CDA7F00103A19 /* SessionDelegate.swift */; };
		319917A2209CDA7F00103A19 /* SessionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991793209CDA7F00103A19 /* SessionDelegate.swift */; };
		319917A3209CDA7F00103A19 /* SessionDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31991793209CDA7F00103A19 /* SessionDelegate.swift */; };
		319917A5209CDAC400103A19 /* RequestTaskMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A4209CDAC400103A19 /* RequestTaskMap.swift */; };
		319917A6209CDAC400103A19 /* RequestTaskMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A4209CDAC400103A19 /* RequestTaskMap.swift */; };
		319917A7209CDAC400103A19 /* RequestTaskMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A4209CDAC400103A19 /* RequestTaskMap.swift */; };
		319917A8209CDAC400103A19 /* RequestTaskMap.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A4209CDAC400103A19 /* RequestTaskMap.swift */; };
		319917AA209CDCB000103A19 /* HTTPHeaders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A9209CDCB000103A19 /* HTTPHeaders.swift */; };
		319917AB209CDCB000103A19 /* HTTPHeaders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A9209CDCB000103A19 /* HTTPHeaders.swift */; };
		319917AC209CDCB000103A19 /* HTTPHeaders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A9209CDCB000103A19 /* HTTPHeaders.swift */; };
		319917AD209CDCB000103A19 /* HTTPHeaders.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917A9209CDCB000103A19 /* HTTPHeaders.swift */; };
		319917B9209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */; };
		319917BA209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */; };
		319917BB209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */; };
		319917BC209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */; };
		31B3DE3B25C11CEA00760641 /* Concurrency.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE3A25C11CEA00760641 /* Concurrency.swift */; };
		31B3DE3C25C11CEA00760641 /* Concurrency.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE3A25C11CEA00760641 /* Concurrency.swift */; };
		31B3DE3D25C11CEA00760641 /* Concurrency.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE3A25C11CEA00760641 /* Concurrency.swift */; };
		31B3DE3E25C11CEA00760641 /* Concurrency.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE3A25C11CEA00760641 /* Concurrency.swift */; };
		31B3DE4F25C120D800760641 /* ConcurrencyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */; };
		31B3DE5025C120D800760641 /* ConcurrencyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */; };
		31B3DE5125C120D800760641 /* ConcurrencyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */; };
		31B51E8C2434FECB005356DB /* RequestModifierTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B51E8B2434FECB005356DB /* RequestModifierTests.swift */; };
		31B51E8D2434FECB005356DB /* RequestModifierTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B51E8B2434FECB005356DB /* RequestModifierTests.swift */; };
		31B51E8E2434FECB005356DB /* RequestModifierTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31B51E8B2434FECB005356DB /* RequestModifierTests.swift */; };
		31BADE4E2439A8D1007D2AB9 /* CombineTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */; };
		31BADE4F2439A8D1007D2AB9 /* CombineTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */; };
		31BADE502439A8D1007D2AB9 /* CombineTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */; };
		31BC5E7B2B7E9D520069BDEF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */; };
		31BC5E7C2B7E9D5F0069BDEF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */; };
		31BC5E7D2B7E9D660069BDEF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */; };
		31BC5E7E2B7E9D6D0069BDEF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */; };
		31BC5E7F2B7E9D730069BDEF /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */; };
		31C2B0EA20B271040089BA7C /* CacheTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C341BB91B1A865A00C1B34D /* CacheTests.swift */; };
		31C2B0EB20B271050089BA7C /* CacheTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C341BB91B1A865A00C1B34D /* CacheTests.swift */; };
		31C2B0EC20B271060089BA7C /* CacheTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C341BB91B1A865A00C1B34D /* CacheTests.swift */; };
		31C2B0F020B271370089BA7C /* TLSEvaluationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */; };
		31C2B0F120B271370089BA7C /* TLSEvaluationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */; };
		31C2B0F220B271380089BA7C /* TLSEvaluationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */; };
		31D83FCE20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */; };
		31D83FCF20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */; };
		31D83FD020D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */; };
		31D83FD120D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */; };
		31DADDFB224811ED0051390F /* AlamofireExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DADDFA224811ED0051390F /* AlamofireExtended.swift */; };
		31DADDFC224811ED0051390F /* AlamofireExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DADDFA224811ED0051390F /* AlamofireExtended.swift */; };
		31DADDFD224811ED0051390F /* AlamofireExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DADDFA224811ED0051390F /* AlamofireExtended.swift */; };
		31DADDFE224811ED0051390F /* AlamofireExtended.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31DADDFA224811ED0051390F /* AlamofireExtended.swift */; };
		31E382E126477307004533B3 /* WebSocketTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FEC68B26225A54009D17DB /* WebSocketTests.swift */; };
		31EBD9C120D1D89C00D1FF34 /* ValidationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */; };
		31EBD9C220D1D89C00D1FF34 /* ValidationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */; };
		31EBD9C320D1D89D00D1FF34 /* ValidationTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */; };
		31ED52E81D73891B00199085 /* AFError+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */; };
		31ED52E91D73891C00199085 /* AFError+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */; };
		31ED52EA1D73891C00199085 /* AFError+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */; };
		31F032542ABB9C0900D68FB2 /* WebSocketTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FEC68B26225A54009D17DB /* WebSocketTests.swift */; };
		31F5085D20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */; };
		31F5085E20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */; };
		31F5085F20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */; };
		31F5086020B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */; };
		31F9683C20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */; };
		31F9683D20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */; };
		31F9683E20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */; };
		31FB2F8722C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */; };
		31FB2F8822C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */; };
		31FB2F8922C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */; };
		31FB2F8A22C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */; };
		31FEC68C26225A54009D17DB /* WebSocketTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FEC68B26225A54009D17DB /* WebSocketTests.swift */; };
		31FEC68D26225A54009D17DB /* WebSocketTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FEC68B26225A54009D17DB /* WebSocketTests.swift */; };
		31FEC68E26225A54009D17DB /* WebSocketTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31FEC68B26225A54009D17DB /* WebSocketTests.swift */; };
		4196936222FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */; };
		4196936322FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */; };
		4196936422FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */; };
		4196936522FA1EAD001EA5D5 /* Result+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */; };
		4C0CB631220BC70300604EDC /* RequestInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */; };
		4C0CB632220BC70300604EDC /* RequestInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */; };
		4C0CB633220BC70300604EDC /* RequestInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */; };
		4C0CB641220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */; };
		4C0CB642220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */; };
		4C0CB643220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */; };
		4C0CB644220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */; };
		4C0CB646220CA8A400604EDC /* RedirectHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB645220CA8A400604EDC /* RedirectHandler.swift */; };
		4C0CB647220CA8A400604EDC /* RedirectHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB645220CA8A400604EDC /* RedirectHandler.swift */; };
		4C0CB648220CA8A400604EDC /* RedirectHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB645220CA8A400604EDC /* RedirectHandler.swift */; };
		4C0CB649220CA8A400604EDC /* RedirectHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB645220CA8A400604EDC /* RedirectHandler.swift */; };
		4C0CB64B220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */; };
		4C0CB64C220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */; };
		4C0CB64D220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */; };
		4C1DC8541B68908E00476DE3 /* AFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C1DC8531B68908E00476DE3 /* AFError.swift */; };
		4C1DC8551B68908E00476DE3 /* AFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C1DC8531B68908E00476DE3 /* AFError.swift */; };
		4C23EB431B327C5B0090E0BC /* MultipartFormData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */; };
		4C23EB441B327C5B0090E0BC /* MultipartFormData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */; };
		4C256A0621EEB69000AD5D87 /* RequestInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */; };
		4C256A0721EEB69000AD5D87 /* RequestInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */; };
		4C256A0821EEB69000AD5D87 /* RequestInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */; };
		4C256A0921EEB69000AD5D87 /* RequestInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */; };
		4C256A1A21F1449C00AD5D87 /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */; };
		4C256A1B21F1449C00AD5D87 /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */; };
		4C256A1C21F1449C00AD5D87 /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */; };
		4C256A1D21F1449C00AD5D87 /* RetryPolicy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */; };
		4C256A531B096C770065714F /* BaseTestCase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A501B096C2C0065714F /* BaseTestCase.swift */; };
		4C256A541B096C770065714F /* BaseTestCase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A501B096C2C0065714F /* BaseTestCase.swift */; };
		4C33A1391B5207DB00873DFF /* rainbow.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1231B5207DB00873DFF /* rainbow.jpg */; };
		4C33A13A1B5207DB00873DFF /* rainbow.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1231B5207DB00873DFF /* rainbow.jpg */; };
		4C33A13B1B5207DB00873DFF /* unicorn.png in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1241B5207DB00873DFF /* unicorn.png */; };
		4C33A13C1B5207DB00873DFF /* unicorn.png in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1241B5207DB00873DFF /* unicorn.png */; };
		4C3D00541C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */; };
		4C3D00551C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */; };
		4C3D00561C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */; };
		4C43669B1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */; };
		4C43669C1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */; };
		4C43669D1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */; };
		4C43669E1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */; };
		4C4466EB21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */; };
		4C4466EC21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */; };
		4C4466ED21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */; };
		4C4466EE21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */; };
		4C67D1362454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */; };
		4C67D1372454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */; };
		4C67D1382454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */; };
		4C67D1392454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */; };
		4C7DD7EB224C627300249836 /* Result+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */; };
		4C7DD7EC224C627300249836 /* Result+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */; };
		4C7DD7ED224C627300249836 /* Result+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */; };
		4C811F8D1B51856D00E0F59A /* ServerTrustEvaluation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */; };
		4C811F8E1B51856D00E0F59A /* ServerTrustEvaluation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */; };
		4CB0080D2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */; };
		4CB0080E2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */; };
		4CB0080F2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */; };
		4CB928291C66BFBC00CE5F08 /* Notifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB928281C66BFBC00CE5F08 /* Notifications.swift */; };
		4CB9282A1C66BFBC00CE5F08 /* Notifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB928281C66BFBC00CE5F08 /* Notifications.swift */; };
		4CB9282B1C66BFBC00CE5F08 /* Notifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB928281C66BFBC00CE5F08 /* Notifications.swift */; };
		4CB9282C1C66BFBC00CE5F08 /* Notifications.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CB928281C66BFBC00CE5F08 /* Notifications.swift */; };
		4CBD2180220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */; };
		4CBD2181220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */; };
		4CBD2182220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */; };
		4CDE2C431AF89F0900BABAE5 /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C421AF89F0900BABAE5 /* Validation.swift */; };
		4CDE2C441AF89F0900BABAE5 /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C421AF89F0900BABAE5 /* Validation.swift */; };
		4CDE2C461AF89FF300BABAE5 /* ResponseSerialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */; };
		4CDE2C471AF89FF300BABAE5 /* ResponseSerialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */; };
		4CE2724F1AF88FB500F1D59A /* ParameterEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */; };
		4CE272501AF88FB500F1D59A /* ParameterEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */; };
		4CEC605A1B745C9100E684F4 /* AFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C1DC8531B68908E00476DE3 /* AFError.swift */; };
		4CEE82AD1C6813CF00E9C9F0 /* NetworkReachabilityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */; };
		4CF626F91BA7CB3E0011A099 /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4CF626EF1BA7CB3E0011A099 /* Alamofire.framework */; };
		4CF627071BA7CBF60011A099 /* Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = F897FF4019AA800700AB5182 /* Alamofire.swift */; };
		4CF627081BA7CBF60011A099 /* AFError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C1DC8531B68908E00476DE3 /* AFError.swift */; };
		4CF6270A1BA7CBF60011A099 /* ParameterEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */; };
		4CF6270E1BA7CBF60011A099 /* MultipartFormData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */; };
		4CF6270F1BA7CBF60011A099 /* ResponseSerialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */; };
		4CF627101BA7CBF60011A099 /* ServerTrustEvaluation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */; };
		4CF627131BA7CBF60011A099 /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C421AF89F0900BABAE5 /* Validation.swift */; };
		4CF627141BA7CC240011A099 /* BaseTestCase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C256A501B096C2C0065714F /* BaseTestCase.swift */; };
		4CF627171BA7CC240011A099 /* ParameterEncodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */; };
		4CF627181BA7CC240011A099 /* RequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5D19A9674D0040E7D1 /* RequestTests.swift */; };
		4CF627341BA7CC300011A099 /* rainbow.jpg in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1231B5207DB00873DFF /* rainbow.jpg */; };
		4CF627351BA7CC300011A099 /* unicorn.png in Resources */ = {isa = PBXBuildFile; fileRef = 4C33A1241B5207DB00873DFF /* unicorn.png */; };
		4CFB02901D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */; };
		4CFB02911D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */; };
		4CFB02921D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */; };
		4CFB02F51D7D2FA20056F249 /* empty_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EA1D7D2FA20056F249 /* empty_data.json */; };
		4CFB02F61D7D2FA20056F249 /* empty_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EA1D7D2FA20056F249 /* empty_data.json */; };
		4CFB02F71D7D2FA20056F249 /* empty_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EA1D7D2FA20056F249 /* empty_data.json */; };
		4CFB02F81D7D2FA20056F249 /* invalid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EB1D7D2FA20056F249 /* invalid_data.json */; };
		4CFB02F91D7D2FA20056F249 /* invalid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EB1D7D2FA20056F249 /* invalid_data.json */; };
		4CFB02FA1D7D2FA20056F249 /* invalid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EB1D7D2FA20056F249 /* invalid_data.json */; };
		4CFB02FB1D7D2FA20056F249 /* valid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EC1D7D2FA20056F249 /* valid_data.json */; };
		4CFB02FC1D7D2FA20056F249 /* valid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EC1D7D2FA20056F249 /* valid_data.json */; };
		4CFB02FD1D7D2FA20056F249 /* valid_data.json in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02EC1D7D2FA20056F249 /* valid_data.json */; };
		4CFB03071D7D2FA20056F249 /* empty_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F21D7D2FA20056F249 /* empty_string.txt */; };
		4CFB03081D7D2FA20056F249 /* empty_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F21D7D2FA20056F249 /* empty_string.txt */; };
		4CFB03091D7D2FA20056F249 /* empty_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F21D7D2FA20056F249 /* empty_string.txt */; };
		4CFB030A1D7D2FA20056F249 /* utf32_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F31D7D2FA20056F249 /* utf32_string.txt */; };
		4CFB030B1D7D2FA20056F249 /* utf32_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F31D7D2FA20056F249 /* utf32_string.txt */; };
		4CFB030C1D7D2FA20056F249 /* utf32_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F31D7D2FA20056F249 /* utf32_string.txt */; };
		4CFB030D1D7D2FA20056F249 /* utf8_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F41D7D2FA20056F249 /* utf8_string.txt */; };
		4CFB030E1D7D2FA20056F249 /* utf8_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F41D7D2FA20056F249 /* utf8_string.txt */; };
		4CFB030F1D7D2FA20056F249 /* utf8_string.txt in Resources */ = {isa = PBXBuildFile; fileRef = 4CFB02F41D7D2FA20056F249 /* utf8_string.txt */; };
		4DD67C251A5C590000ED2280 /* Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = F897FF4019AA800700AB5182 /* Alamofire.swift */; };
		8035DB621BAB492500466CB3 /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F8111E3319A95C8B0040E7D1 /* Alamofire.framework */; };
		E4202FD01B667AA100C997FB /* ParameterEncoding.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */; };
		E4202FD21B667AA100C997FB /* ResponseSerialization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */; };
		E4202FD41B667AA100C997FB /* Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = F897FF4019AA800700AB5182 /* Alamofire.swift */; };
		E4202FD51B667AA100C997FB /* MultipartFormData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */; };
		E4202FD61B667AA100C997FB /* ServerTrustEvaluation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */; };
		E4202FD81B667AA100C997FB /* Validation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4CDE2C421AF89F0900BABAE5 /* Validation.swift */; };
		F8111E6119A9674D0040E7D1 /* ParameterEncodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */; };
		F829C6B81A7A94F100A2CD59 /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4DD67C0B1A5C55C900ED2280 /* Alamofire.framework */; };
		F829C6BE1A7A950600A2CD59 /* ParameterEncodingTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */; };
		F829C6BF1A7A950600A2CD59 /* RequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5D19A9674D0040E7D1 /* RequestTests.swift */; };
		F8858DDD19A96B4300F55F93 /* RequestTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F8111E5D19A9674D0040E7D1 /* RequestTests.swift */; };
		F897FF4119AA800700AB5182 /* Alamofire.swift in Sources */ = {isa = PBXBuildFile; fileRef = F897FF4019AA800700AB5182 /* Alamofire.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		3129306B263E17D600473CEA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8111E2A19A95C8B0040E7D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4202FCD1B667AA100C997FB;
			remoteInfo = "Alamofire watchOS";
		};
		317338F92A43BE5F00D4EA0A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8111E2A19A95C8B0040E7D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 317338C32A43A4FA00D4EA0A;
			remoteInfo = "Alamofire visionOS";
		};
		4CF626FA1BA7CB3E0011A099 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8111E2A19A95C8B0040E7D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4CF626EE1BA7CB3E0011A099;
			remoteInfo = "Alamofire tvOS";
		};
		F8111E6519A967880040E7D1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8111E2A19A95C8B0040E7D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F8111E3219A95C8B0040E7D1;
			remoteInfo = Alamofire;
		};
		F829C6B91A7A94F100A2CD59 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8111E2A19A95C8B0040E7D1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 4DD67C0A1A5C55C900ED2280;
			remoteInfo = "Alamofire OSX";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProtectedTests.swift; sourceTree = "<group>"; };
		3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataStreamTests.swift; sourceTree = "<group>"; };
		3111CE8720A77843008315E2 /* EventMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EventMonitor.swift; sourceTree = "<group>"; };
		3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTTPHeadersTests.swift; sourceTree = "<group>"; };
		31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Bundle+AlamofireTests.swift"; sourceTree = "<group>"; };
		31181E172794FF9600E88600 /* certPEM.cer */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = certPEM.cer; sourceTree = "<group>"; };
		31181E182794FF9600E88600 /* randomGibberish.crt */ = {isa = PBXFileReference; lastKnownFileType = file; path = randomGibberish.crt; sourceTree = "<group>"; };
		31181E192794FF9600E88600 /* certDER.der */ = {isa = PBXFileReference; lastKnownFileType = file; path = certDER.der; sourceTree = "<group>"; };
		31181E1A2794FF9600E88600 /* keyDER.der */ = {isa = PBXFileReference; lastKnownFileType = file; path = keyDER.der; sourceTree = "<group>"; };
		31181E1B2794FF9600E88600 /* certDER.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = certDER.cer; sourceTree = "<group>"; };
		31181E1C2794FF9600E88600 /* certPEM.crt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = certPEM.crt; sourceTree = "<group>"; };
		31181E1D2794FF9600E88600 /* certDER.crt */ = {isa = PBXFileReference; lastKnownFileType = file; path = certDER.crt; sourceTree = "<group>"; };
		31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "alamofire-signing-ca2.cer"; sourceTree = "<group>"; };
		31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "alamofire-signing-ca1.cer"; sourceTree = "<group>"; };
		31181E212794FF9600E88600 /* signed-by-ca2.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "signed-by-ca2.cer"; sourceTree = "<group>"; };
		31181E222794FF9600E88600 /* signed-by-ca1.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "signed-by-ca1.cer"; sourceTree = "<group>"; };
		31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "missing-dns-name-and-uri.cer"; sourceTree = "<group>"; };
		31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = wildcard.alamofire.org.cer; sourceTree = "<group>"; };
		31181E252794FF9600E88600 /* test.alamofire.org.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = test.alamofire.org.cer; sourceTree = "<group>"; };
		31181E262794FF9600E88600 /* alamofire-root-ca.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "alamofire-root-ca.cer"; sourceTree = "<group>"; };
		31181E272794FF9600E88600 /* valid-uri.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "valid-uri.cer"; sourceTree = "<group>"; };
		31181E282794FF9600E88600 /* multiple-dns-names.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "multiple-dns-names.cer"; sourceTree = "<group>"; };
		31181E292794FF9600E88600 /* expired.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = expired.cer; sourceTree = "<group>"; };
		31181E2A2794FF9600E88600 /* valid-dns-name.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "valid-dns-name.cer"; sourceTree = "<group>"; };
		31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "expired.badssl.com-intermediate-ca-1.cer"; sourceTree = "<group>"; };
		31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "expired.badssl.com-intermediate-ca-2.cer"; sourceTree = "<group>"; };
		31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "expired.badssl.com-root-ca.cer"; sourceTree = "<group>"; };
		31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = "expired.badssl.com-leaf.cer"; sourceTree = "<group>"; };
		311B198F20B0D3B40036823B /* MultipartUpload.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultipartUpload.swift; sourceTree = "<group>"; };
		31293065263E17D600473CEA /* Alamofire watchOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Alamofire watchOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		312D1E0B1FC2551400E51FF1 /* Usage.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = Usage.md; path = Documentation/Usage.md; sourceTree = "<group>"; };
		312D1E0C1FC2551400E51FF1 /* AdvancedUsage.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = AdvancedUsage.md; path = Documentation/AdvancedUsage.md; sourceTree = "<group>"; };
		312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InternalHelpers.swift; sourceTree = "<group>"; };
		31425AC0241F098000EE3CCC /* InternalRequestTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InternalRequestTests.swift; sourceTree = "<group>"; };
		3145E0E227977AA300949557 /* iOS-NoTS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "iOS-NoTS.xctestplan"; sourceTree = "<group>"; };
		3145E0E32797A8EF00949557 /* tvOS-NoTS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "tvOS-NoTS.xctestplan"; sourceTree = "<group>"; };
		3145E0E42797A8EF00949557 /* tvOS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = tvOS.xctestplan; sourceTree = "<group>"; };
		3145E0E52797A8EF00949557 /* tvOS-Old.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "tvOS-Old.xctestplan"; sourceTree = "<group>"; };
		3145E0E62797D91600949557 /* watchOS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = watchOS.xctestplan; sourceTree = "<group>"; };
		3145E0E72797D94200949557 /* watchOS-NoTS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "watchOS-NoTS.xctestplan"; sourceTree = "<group>"; };
		3145E0E82797D9E700949557 /* macOS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = macOS.xctestplan; sourceTree = "<group>"; };
		3145E0E92797D9E900949557 /* macOS-NoTS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "macOS-NoTS.xctestplan"; sourceTree = "<group>"; };
		314998E927A6560600ABB856 /* Request+AlamofireTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Request+AlamofireTests.swift"; sourceTree = "<group>"; };
		31501E872196962A005829F2 /* ParameterEncoderTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ParameterEncoderTests.swift; sourceTree = "<group>"; };
		31577E0A2676E72D001C7532 /* FUNDING.yml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = FUNDING.yml; sourceTree = "<group>"; };
		315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "StringEncoding+Alamofire.swift"; sourceTree = "<group>"; };
		3165407229AEBC0400C9BE08 /* RequestCompression.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RequestCompression.swift; sourceTree = "<group>"; };
		31727417218BAEC90039FFCC /* HTTPMethod.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTTPMethod.swift; sourceTree = "<group>"; };
		3172741C218BB1790039FFCC /* ParameterEncoder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ParameterEncoder.swift; sourceTree = "<group>"; };
		31727421218BB9A50039FFCC /* TestHelpers.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TestHelpers.swift; sourceTree = "<group>"; };
		317338C42A43A4FA00D4EA0A /* Alamofire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Alamofire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		317338F42A43BE5F00D4EA0A /* Alamofire visionOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Alamofire visionOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		317339432A43BF9E00D4EA0A /* visionOS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = visionOS.xctestplan; sourceTree = "<group>"; };
		31762DC9247738FA0025C704 /* LeaksTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeaksTests.swift; sourceTree = "<group>"; };
		318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebSocketRequest.swift; sourceTree = "<group>"; };
		318702F12B0AEE3700C10A8C /* DataRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataRequest.swift; sourceTree = "<group>"; };
		318702F72B0AEEE400C10A8C /* UploadRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UploadRequest.swift; sourceTree = "<group>"; };
		318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataStreamRequest.swift; sourceTree = "<group>"; };
		318703032B0AEF4B00C10A8C /* DownloadRequest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DownloadRequest.swift; sourceTree = "<group>"; };
		318DD40E2439780500963291 /* Combine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Combine.swift; sourceTree = "<group>"; };
		3191B5741F5F53A6003960A8 /* Protected.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Protected.swift; sourceTree = "<group>"; };
		31991790209CDA7F00103A19 /* Request.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Request.swift; sourceTree = "<group>"; };
		31991791209CDA7F00103A19 /* Response.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Response.swift; sourceTree = "<group>"; };
		31991792209CDA7F00103A19 /* Session.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Session.swift; sourceTree = "<group>"; };
		31991793209CDA7F00103A19 /* SessionDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SessionDelegate.swift; sourceTree = "<group>"; };
		319917A4209CDAC400103A19 /* RequestTaskMap.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RequestTaskMap.swift; sourceTree = "<group>"; };
		319917A9209CDCB000103A19 /* HTTPHeaders.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTTPHeaders.swift; sourceTree = "<group>"; };
		319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "OperationQueue+Alamofire.swift"; sourceTree = "<group>"; };
		319ECEA025EC96E8001C38CA /* ci.yml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = ci.yml; sourceTree = "<group>"; };
		319ECEA125EC96E8001C38CA /* PULL_REQUEST_TEMPLATE.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = PULL_REQUEST_TEMPLATE.md; sourceTree = "<group>"; };
		319ECEA225EC96E8001C38CA /* BUG_REPORT.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = BUG_REPORT.md; sourceTree = "<group>"; };
		319ECEA425EC96E8001C38CA /* config.yml */ = {isa = PBXFileReference; lastKnownFileType = text.yaml; path = config.yml; sourceTree = "<group>"; };
		319ECEA525EC9710001C38CA /* FEATURE_REQUEST.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = FEATURE_REQUEST.md; sourceTree = "<group>"; };
		31B2CA9521AA25CD005B371A /* Package.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Package.swift; sourceTree = "<group>"; };
		31B3DE3A25C11CEA00760641 /* Concurrency.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Concurrency.swift; sourceTree = "<group>"; };
		31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConcurrencyTests.swift; sourceTree = "<group>"; };
		31B51E8B2434FECB005356DB /* RequestModifierTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RequestModifierTests.swift; sourceTree = "<group>"; };
		31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CombineTests.swift; sourceTree = "<group>"; };
		31BC5E792B7E75770069BDEF /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "<EMAIL>"; sourceTree = "<group>"; };
		31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "URLConvertible+URLRequestConvertible.swift"; sourceTree = "<group>"; };
		31DADDFA224811ED0051390F /* AlamofireExtended.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlamofireExtended.swift; sourceTree = "<group>"; };
		31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "AFError+AlamofireTests.swift"; sourceTree = "<group>"; };
		31EF4BF5279646450048A19D /* iOS.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = iOS.xctestplan; sourceTree = "<group>"; };
		31EF4BF627964B520048A19D /* iOS-Old.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = "iOS-Old.xctestplan"; sourceTree = "<group>"; };
		31F1AA4123F75AEE00C2BB80 /* Alamofire 5.0 Migration Guide.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = "Alamofire 5.0 Migration Guide.md"; path = "Documentation/Alamofire 5.0 Migration Guide.md"; sourceTree = "<group>"; };
		31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "URLSessionConfiguration+Alamofire.swift"; sourceTree = "<group>"; };
		31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSLoggingEventMonitor.swift; sourceTree = "<group>"; };
		31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = URLEncodedFormEncoder.swift; sourceTree = "<group>"; };
		31FEC68B26225A54009D17DB /* WebSocketTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WebSocketTests.swift; sourceTree = "<group>"; };
		4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Result+Alamofire.swift"; sourceTree = "<group>"; };
		4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ResponseSerializationTests.swift; sourceTree = "<group>"; };
		4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RequestInterceptorTests.swift; sourceTree = "<group>"; };
		4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "URLRequest+Alamofire.swift"; sourceTree = "<group>"; };
		4C0CB645220CA8A400604EDC /* RedirectHandler.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RedirectHandler.swift; sourceTree = "<group>"; };
		4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RedirectHandlerTests.swift; sourceTree = "<group>"; };
		4C1DC8531B68908E00476DE3 /* AFError.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AFError.swift; sourceTree = "<group>"; };
		4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MultipartFormData.swift; sourceTree = "<group>"; };
		4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RequestInterceptor.swift; sourceTree = "<group>"; };
		4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RetryPolicy.swift; sourceTree = "<group>"; };
		4C256A501B096C2C0065714F /* BaseTestCase.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseTestCase.swift; sourceTree = "<group>"; };
		4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MultipartFormDataTests.swift; sourceTree = "<group>"; };
		4C33A1231B5207DB00873DFF /* rainbow.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = rainbow.jpg; sourceTree = "<group>"; };
		4C33A1241B5207DB00873DFF /* unicorn.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = unicorn.png; sourceTree = "<group>"; };
		4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ServerTrustEvaluatorTests.swift; sourceTree = "<group>"; };
		4C341BB91B1A865A00C1B34D /* CacheTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CacheTests.swift; sourceTree = "<group>"; };
		4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkReachabilityManager.swift; sourceTree = "<group>"; };
		4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NetworkReachabilityManagerTests.swift; sourceTree = "<group>"; };
		4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "DispatchQueue+Alamofire.swift"; sourceTree = "<group>"; };
		4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CachedResponseHandler.swift; sourceTree = "<group>"; };
		4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationInterceptor.swift; sourceTree = "<group>"; };
		4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Result+AlamofireTests.swift"; sourceTree = "<group>"; };
		4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ServerTrustEvaluation.swift; sourceTree = "<group>"; };
		4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SessionDelegateTests.swift; sourceTree = "<group>"; };
		4C9E88371F5FB3B0000BEC61 /* Alamofire 2.0 Migration Guide.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = "Alamofire 2.0 Migration Guide.md"; path = "Documentation/Alamofire 2.0 Migration Guide.md"; sourceTree = "<group>"; };
		4C9E88381F5FB3B0000BEC61 /* Alamofire 3.0 Migration Guide.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = "Alamofire 3.0 Migration Guide.md"; path = "Documentation/Alamofire 3.0 Migration Guide.md"; sourceTree = "<group>"; };
		4C9E88391F5FB3B0000BEC61 /* Alamofire 4.0 Migration Guide.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; name = "Alamofire 4.0 Migration Guide.md"; path = "Documentation/Alamofire 4.0 Migration Guide.md"; sourceTree = "<group>"; };
		4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationInterceptorTests.swift; sourceTree = "<group>"; };
		4CB928281C66BFBC00CE5F08 /* Notifications.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Notifications.swift; sourceTree = "<group>"; };
		4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RetryPolicyTests.swift; sourceTree = "<group>"; };
		4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = URLProtocolTests.swift; sourceTree = "<group>"; };
		4CDE2C421AF89F0900BABAE5 /* Validation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Validation.swift; sourceTree = "<group>"; };
		4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ResponseSerialization.swift; sourceTree = "<group>"; };
		4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ParameterEncoding.swift; sourceTree = "<group>"; };
		4CE292311EF4A393008DA555 /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		4CE292321EF4A393008DA555 /* CHANGELOG.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = CHANGELOG.md; sourceTree = "<group>"; };
		4CE292331EF4A393008DA555 /* CONTRIBUTING.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = CONTRIBUTING.md; sourceTree = "<group>"; };
		4CE292391EF4B12B008DA555 /* Alamofire.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; path = Alamofire.podspec; sourceTree = "<group>"; };
		4CF3B4281F5FC7900075BE59 /* LICENSE */ = {isa = PBXFileReference; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		4CF626EF1BA7CB3E0011A099 /* Alamofire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Alamofire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		4CF626F81BA7CB3E0011A099 /* Alamofire tvOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Alamofire tvOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "FileManager+AlamofireTests.swift"; sourceTree = "<group>"; };
		4CFB02EA1D7D2FA20056F249 /* empty_data.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = empty_data.json; sourceTree = "<group>"; };
		4CFB02EB1D7D2FA20056F249 /* invalid_data.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = invalid_data.json; sourceTree = "<group>"; };
		4CFB02EC1D7D2FA20056F249 /* valid_data.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = valid_data.json; sourceTree = "<group>"; };
		4CFB02F21D7D2FA20056F249 /* empty_string.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = empty_string.txt; sourceTree = "<group>"; };
		4CFB02F31D7D2FA20056F249 /* utf32_string.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = utf32_string.txt; sourceTree = "<group>"; };
		4CFB02F41D7D2FA20056F249 /* utf8_string.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = utf8_string.txt; sourceTree = "<group>"; };
		4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CachedResponseHandlerTests.swift; sourceTree = "<group>"; };
		4DD67C0B1A5C55C900ED2280 /* Alamofire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Alamofire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E4202FE01B667AA100C997FB /* Alamofire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Alamofire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F8111E3319A95C8B0040E7D1 /* Alamofire.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Alamofire.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		F8111E3719A95C8B0040E7D1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F8111E3E19A95C8B0040E7D1 /* Alamofire iOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Alamofire iOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		F8111E4119A95C8B0040E7D1 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DownloadTests.swift; sourceTree = "<group>"; };
		F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ParameterEncodingTests.swift; sourceTree = "<group>"; };
		F8111E5D19A9674D0040E7D1 /* RequestTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RequestTests.swift; sourceTree = "<group>"; };
		F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ResponseTests.swift; sourceTree = "<group>"; };
		F8111E5F19A9674D0040E7D1 /* UploadTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UploadTests.swift; sourceTree = "<group>"; };
		F829C6B21A7A94F100A2CD59 /* Alamofire macOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Alamofire macOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TLSEvaluationTests.swift; sourceTree = "<group>"; };
		F897FF4019AA800700AB5182 /* Alamofire.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Alamofire.swift; sourceTree = "<group>"; };
		F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ValidationTests.swift; sourceTree = "<group>"; };
		F8D1C6F419D52968002E74FE /* SessionTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SessionTests.swift; sourceTree = "<group>"; };
		F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AuthenticationTests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		31293062263E17D600473CEA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3129306A263E17D600473CEA /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338C12A43A4FA00D4EA0A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338F12A43BE5F00D4EA0A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				317338F82A43BE5F00D4EA0A /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626EB1BA7CB3E0011A099 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626F51BA7CB3E0011A099 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4CF626F91BA7CB3E0011A099 /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DD67C071A5C55C900ED2280 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4202FD91B667AA100C997FB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E2F19A95C8B0040E7D1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E3B19A95C8B0040E7D1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8035DB621BAB492500466CB3 /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F829C6AF1A7A94F100A2CD59 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F829C6B81A7A94F100A2CD59 /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		31181E162794FF9600E88600 /* selfSignedAndMalformedCerts */ = {
			isa = PBXGroup;
			children = (
				31181E172794FF9600E88600 /* certPEM.cer */,
				31181E182794FF9600E88600 /* randomGibberish.crt */,
				31181E192794FF9600E88600 /* certDER.der */,
				31181E1A2794FF9600E88600 /* keyDER.der */,
				31181E1B2794FF9600E88600 /* certDER.cer */,
				31181E1C2794FF9600E88600 /* certPEM.crt */,
				31181E1D2794FF9600E88600 /* certDER.crt */,
			);
			path = selfSignedAndMalformedCerts;
			sourceTree = "<group>";
		};
		31181E1E2794FF9600E88600 /* alamofire-org */ = {
			isa = PBXGroup;
			children = (
				31181E1F2794FF9600E88600 /* alamofire-signing-ca2.cer */,
				31181E202794FF9600E88600 /* alamofire-signing-ca1.cer */,
				31181E212794FF9600E88600 /* signed-by-ca2.cer */,
				31181E222794FF9600E88600 /* signed-by-ca1.cer */,
				31181E232794FF9600E88600 /* missing-dns-name-and-uri.cer */,
				31181E242794FF9600E88600 /* wildcard.alamofire.org.cer */,
				31181E252794FF9600E88600 /* test.alamofire.org.cer */,
				31181E262794FF9600E88600 /* alamofire-root-ca.cer */,
				31181E272794FF9600E88600 /* valid-uri.cer */,
				31181E282794FF9600E88600 /* multiple-dns-names.cer */,
				31181E292794FF9600E88600 /* expired.cer */,
				31181E2A2794FF9600E88600 /* valid-dns-name.cer */,
			);
			path = "alamofire-org";
			sourceTree = "<group>";
		};
		31181E2B2794FF9600E88600 /* expired-badssl-com */ = {
			isa = PBXGroup;
			children = (
				31181E2C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer */,
				31181E2D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer */,
				31181E2E2794FF9600E88600 /* expired.badssl.com-root-ca.cer */,
				31181E2F2794FF9600E88600 /* expired.badssl.com-leaf.cer */,
			);
			path = "expired-badssl-com";
			sourceTree = "<group>";
		};
		319ECE9E25EC96E8001C38CA /* .github */ = {
			isa = PBXGroup;
			children = (
				319ECEA125EC96E8001C38CA /* PULL_REQUEST_TEMPLATE.md */,
				31577E0A2676E72D001C7532 /* FUNDING.yml */,
				319ECEA325EC96E8001C38CA /* ISSUE_TEMPLATE */,
				319ECE9F25EC96E8001C38CA /* workflows */,
			);
			path = .github;
			sourceTree = "<group>";
		};
		319ECE9F25EC96E8001C38CA /* workflows */ = {
			isa = PBXGroup;
			children = (
				319ECEA025EC96E8001C38CA /* ci.yml */,
			);
			path = workflows;
			sourceTree = "<group>";
		};
		319ECEA325EC96E8001C38CA /* ISSUE_TEMPLATE */ = {
			isa = PBXGroup;
			children = (
				319ECEA225EC96E8001C38CA /* BUG_REPORT.md */,
				319ECEA525EC9710001C38CA /* FEATURE_REQUEST.md */,
				319ECEA425EC96E8001C38CA /* config.yml */,
			);
			path = ISSUE_TEMPLATE;
			sourceTree = "<group>";
		};
		31B6FE482B65812D003673E3 /* Core */ = {
			isa = PBXGroup;
			children = (
				4C1DC8531B68908E00476DE3 /* AFError.swift */,
				318702F12B0AEE3700C10A8C /* DataRequest.swift */,
				318702FD2B0AEF1D00C10A8C /* DataStreamRequest.swift */,
				318703032B0AEF4B00C10A8C /* DownloadRequest.swift */,
				319917A9209CDCB000103A19 /* HTTPHeaders.swift */,
				31727417218BAEC90039FFCC /* HTTPMethod.swift */,
				4CB928281C66BFBC00CE5F08 /* Notifications.swift */,
				3172741C218BB1790039FFCC /* ParameterEncoder.swift */,
				4CE2724E1AF88FB500F1D59A /* ParameterEncoding.swift */,
				3191B5741F5F53A6003960A8 /* Protected.swift */,
				31991790209CDA7F00103A19 /* Request.swift */,
				319917A4209CDAC400103A19 /* RequestTaskMap.swift */,
				31991791209CDA7F00103A19 /* Response.swift */,
				31991792209CDA7F00103A19 /* Session.swift */,
				31991793209CDA7F00103A19 /* SessionDelegate.swift */,
				318702F72B0AEEE400C10A8C /* UploadRequest.swift */,
				31D83FCD20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift */,
				318702EB2B0AEDBB00C10A8C /* WebSocketRequest.swift */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		31B6FE492B65814D003673E3 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				4C43669A1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift */,
				319917B8209CE53A00103A19 /* OperationQueue+Alamofire.swift */,
				4196936122FA1E05001EA5D5 /* Result+Alamofire.swift */,
				315A4C55241EF28B00D57C7A /* StringEncoding+Alamofire.swift */,
				4C0CB640220CA89400604EDC /* URLRequest+Alamofire.swift */,
				31F5085C20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		31B6FE4A2B65816C003673E3 /* Features */ = {
			isa = PBXGroup;
			children = (
				31DADDFA224811ED0051390F /* AlamofireExtended.swift */,
				4C67D1352454B12A00CBA725 /* AuthenticationInterceptor.swift */,
				4C4466EA21F8F5D800AC9703 /* CachedResponseHandler.swift */,
				318DD40E2439780500963291 /* Combine.swift */,
				31B3DE3A25C11CEA00760641 /* Concurrency.swift */,
				3111CE8720A77843008315E2 /* EventMonitor.swift */,
				4C23EB421B327C5B0090E0BC /* MultipartFormData.swift */,
				311B198F20B0D3B40036823B /* MultipartUpload.swift */,
				4C3D00531C66A63000D1F709 /* NetworkReachabilityManager.swift */,
				4C0CB645220CA8A400604EDC /* RedirectHandler.swift */,
				3165407229AEBC0400C9BE08 /* RequestCompression.swift */,
				4C256A0521EEB69000AD5D87 /* RequestInterceptor.swift */,
				4CDE2C451AF89FF300BABAE5 /* ResponseSerialization.swift */,
				4C256A1921F1449C00AD5D87 /* RetryPolicy.swift */,
				4C811F8C1B51856D00E0F59A /* ServerTrustEvaluation.swift */,
				31FB2F8622C828D8007FD6D5 /* URLEncodedFormEncoder.swift */,
				4CDE2C421AF89F0900BABAE5 /* Validation.swift */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		31BC5E782B7E75760069BDEF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				31BC5E792B7E75770069BDEF /* <EMAIL> */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		31EF4BF4279646000048A19D /* Test Plans */ = {
			isa = PBXGroup;
			children = (
				3145E0E227977AA300949557 /* iOS-NoTS.xctestplan */,
				31EF4BF627964B520048A19D /* iOS-Old.xctestplan */,
				31EF4BF5279646450048A19D /* iOS.xctestplan */,
				3145E0E92797D9E900949557 /* macOS-NoTS.xctestplan */,
				3145E0E82797D9E700949557 /* macOS.xctestplan */,
				3145E0E32797A8EF00949557 /* tvOS-NoTS.xctestplan */,
				3145E0E52797A8EF00949557 /* tvOS-Old.xctestplan */,
				3145E0E42797A8EF00949557 /* tvOS.xctestplan */,
				317339432A43BF9E00D4EA0A /* visionOS.xctestplan */,
				3145E0E72797D94200949557 /* watchOS-NoTS.xctestplan */,
				3145E0E62797D91600949557 /* watchOS.xctestplan */,
			);
			path = "Test Plans";
			sourceTree = "<group>";
		};
		4C256A4E1B09656A0065714F /* Core */ = {
			isa = PBXGroup;
			children = (
				F8E6024419CB46A800A3E7F1 /* AuthenticationTests.swift */,
				3106FB6423F8D9E0007FAB43 /* DataStreamTests.swift */,
				F8111E5B19A9674D0040E7D1 /* DownloadTests.swift */,
				31425AC0241F098000EE3CCC /* InternalRequestTests.swift */,
				31501E872196962A005829F2 /* ParameterEncoderTests.swift */,
				F8111E5C19A9674D0040E7D1 /* ParameterEncodingTests.swift */,
				31B51E8B2434FECB005356DB /* RequestModifierTests.swift */,
				F8111E5D19A9674D0040E7D1 /* RequestTests.swift */,
				F8111E5E19A9674D0040E7D1 /* ResponseTests.swift */,
				4C9DCE771CB1BCE2003E6463 /* SessionDelegateTests.swift */,
				F8D1C6F419D52968002E74FE /* SessionTests.swift */,
				F8111E5F19A9674D0040E7D1 /* UploadTests.swift */,
				31FEC68B26225A54009D17DB /* WebSocketTests.swift */,
			);
			name = Core;
			sourceTree = "<group>";
		};
		4C256A4F1B09656E0065714F /* Features */ = {
			isa = PBXGroup;
			children = (
				4CB0080C2455FE9700C38783 /* AuthenticationInterceptorTests.swift */,
				4CFD6B132201338E00FFB5E3 /* CachedResponseHandlerTests.swift */,
				4C341BB91B1A865A00C1B34D /* CacheTests.swift */,
				31BADE4D2439A8D1007D2AB9 /* CombineTests.swift */,
				31B3DE4E25C120D800760641 /* ConcurrencyTests.swift */,
				3113D46A21878227001CCD21 /* HTTPHeadersTests.swift */,
				4C3238E61B3604DB00FE04AE /* MultipartFormDataTests.swift */,
				4C3D00571C66A8B900D1F709 /* NetworkReachabilityManagerTests.swift */,
				3106FB6023F8C53A007FAB43 /* ProtectedTests.swift */,
				4C0CB64A220CA8D600604EDC /* RedirectHandlerTests.swift */,
				4C0CB630220BC70300604EDC /* RequestInterceptorTests.swift */,
				4C0B58381B747A4400C0B99C /* ResponseSerializationTests.swift */,
				4CBD217F220B48AE008F1C59 /* RetryPolicyTests.swift */,
				4C33A1421B52089C00873DFF /* ServerTrustEvaluatorTests.swift */,
				F86AEFE51AE6A282007D9C76 /* TLSEvaluationTests.swift */,
				4CCFA7991B2BE71600B6F460 /* URLProtocolTests.swift */,
				F8AE910119D28DCC0078C7B2 /* ValidationTests.swift */,
			);
			name = Features;
			sourceTree = "<group>";
		};
		4C3238E91B3617A600FE04AE /* Resources */ = {
			isa = PBXGroup;
			children = (
				4C33A1171B5207DB00873DFF /* Certificates */,
				4C33A1221B5207DB00873DFF /* Images */,
				4CFB02E81D7D2FA20056F249 /* Responses */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		4C33A1171B5207DB00873DFF /* Certificates */ = {
			isa = PBXGroup;
			children = (
				31181E1E2794FF9600E88600 /* alamofire-org */,
				31181E2B2794FF9600E88600 /* expired-badssl-com */,
				31181E162794FF9600E88600 /* selfSignedAndMalformedCerts */,
			);
			name = Certificates;
			path = Resources/Certificates;
			sourceTree = "<group>";
		};
		4C33A1221B5207DB00873DFF /* Images */ = {
			isa = PBXGroup;
			children = (
				4C33A1231B5207DB00873DFF /* rainbow.jpg */,
				4C33A1241B5207DB00873DFF /* unicorn.png */,
			);
			name = Images;
			path = Resources/Images;
			sourceTree = "<group>";
		};
		4C7C8D201B9D0D7300948136 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				31ED52E61D73889D00199085 /* AFError+AlamofireTests.swift */,
				31181E112794FE5400E88600 /* Bundle+AlamofireTests.swift */,
				4CFB028F1D7CF28F0056F249 /* FileManager+AlamofireTests.swift */,
				314998E927A6560600ABB856 /* Request+AlamofireTests.swift */,
				4C7DD7EA224C627300249836 /* Result+AlamofireTests.swift */,
			);
			name = Extensions;
			sourceTree = "<group>";
		};
		4C9E88361F5FB39F000BEC61 /* Migration Guides */ = {
			isa = PBXGroup;
			children = (
				4C9E88371F5FB3B0000BEC61 /* Alamofire 2.0 Migration Guide.md */,
				4C9E88381F5FB3B0000BEC61 /* Alamofire 3.0 Migration Guide.md */,
				4C9E88391F5FB3B0000BEC61 /* Alamofire 4.0 Migration Guide.md */,
				31F1AA4123F75AEE00C2BB80 /* Alamofire 5.0 Migration Guide.md */,
			);
			name = "Migration Guides";
			sourceTree = "<group>";
		};
		4CE292301EF4A386008DA555 /* Documentation */ = {
			isa = PBXGroup;
			children = (
				312D1E0C1FC2551400E51FF1 /* AdvancedUsage.md */,
				312D1E0B1FC2551400E51FF1 /* Usage.md */,
				4C9E88361F5FB39F000BEC61 /* Migration Guides */,
			);
			name = Documentation;
			sourceTree = "<group>";
		};
		4CE292381EF4B116008DA555 /* Deployment */ = {
			isa = PBXGroup;
			children = (
				319ECE9E25EC96E8001C38CA /* .github */,
				4CE292391EF4B12B008DA555 /* Alamofire.podspec */,
				4CE292321EF4A393008DA555 /* CHANGELOG.md */,
				4CE292331EF4A393008DA555 /* CONTRIBUTING.md */,
				4CF3B4281F5FC7900075BE59 /* LICENSE */,
				31B2CA9521AA25CD005B371A /* Package.swift */,
				4CE292311EF4A393008DA555 /* README.md */,
			);
			name = Deployment;
			sourceTree = "<group>";
		};
		4CFB02E81D7D2FA20056F249 /* Responses */ = {
			isa = PBXGroup;
			children = (
				4CFB02E91D7D2FA20056F249 /* JSON */,
				4CFB02F11D7D2FA20056F249 /* String */,
			);
			name = Responses;
			path = Resources/Responses;
			sourceTree = "<group>";
		};
		4CFB02E91D7D2FA20056F249 /* JSON */ = {
			isa = PBXGroup;
			children = (
				4CFB02EA1D7D2FA20056F249 /* empty_data.json */,
				4CFB02EB1D7D2FA20056F249 /* invalid_data.json */,
				4CFB02EC1D7D2FA20056F249 /* valid_data.json */,
			);
			path = JSON;
			sourceTree = "<group>";
		};
		4CFB02F11D7D2FA20056F249 /* String */ = {
			isa = PBXGroup;
			children = (
				4CFB02F21D7D2FA20056F249 /* empty_string.txt */,
				4CFB02F41D7D2FA20056F249 /* utf8_string.txt */,
				4CFB02F31D7D2FA20056F249 /* utf32_string.txt */,
			);
			path = String;
			sourceTree = "<group>";
		};
		F8111E2919A95C8B0040E7D1 = {
			isa = PBXGroup;
			children = (
				4CE292381EF4B116008DA555 /* Deployment */,
				4CE292301EF4A386008DA555 /* Documentation */,
				F8111E3519A95C8B0040E7D1 /* Source */,
				F8111E3F19A95C8B0040E7D1 /* Tests */,
				F8111E3419A95C8B0040E7D1 /* Products */,
				31BC5E782B7E75760069BDEF /* Frameworks */,
			);
			indentWidth = 4;
			sourceTree = "<group>";
			tabWidth = 4;
			usesTabs = 0;
		};
		F8111E3419A95C8B0040E7D1 /* Products */ = {
			isa = PBXGroup;
			children = (
				F8111E3319A95C8B0040E7D1 /* Alamofire.framework */,
				F8111E3E19A95C8B0040E7D1 /* Alamofire iOS Tests.xctest */,
				4DD67C0B1A5C55C900ED2280 /* Alamofire.framework */,
				F829C6B21A7A94F100A2CD59 /* Alamofire macOS Tests.xctest */,
				E4202FE01B667AA100C997FB /* Alamofire.framework */,
				4CF626EF1BA7CB3E0011A099 /* Alamofire.framework */,
				4CF626F81BA7CB3E0011A099 /* Alamofire tvOS Tests.xctest */,
				31293065263E17D600473CEA /* Alamofire watchOS Tests.xctest */,
				317338C42A43A4FA00D4EA0A /* Alamofire.framework */,
				317338F42A43BE5F00D4EA0A /* Alamofire visionOS Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F8111E3519A95C8B0040E7D1 /* Source */ = {
			isa = PBXGroup;
			children = (
				F897FF4019AA800700AB5182 /* Alamofire.swift */,
				31B6FE482B65812D003673E3 /* Core */,
				31B6FE492B65814D003673E3 /* Extensions */,
				31B6FE4A2B65816C003673E3 /* Features */,
				F8111E3619A95C8B0040E7D1 /* Supporting Files */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		F8111E3619A95C8B0040E7D1 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				F8111E3719A95C8B0040E7D1 /* Info.plist */,
				0AEC95C92AF5419400CD241A /* PrivacyInfo.xcprivacy */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		F8111E3F19A95C8B0040E7D1 /* Tests */ = {
			isa = PBXGroup;
			children = (
				4C256A501B096C2C0065714F /* BaseTestCase.swift */,
				312FC4FE2CB079E400E48EAB /* InternalHelpers.swift */,
				31762DC9247738FA0025C704 /* LeaksTests.swift */,
				31F9683B20BB70290009606F /* NSLoggingEventMonitor.swift */,
				31727421218BB9A50039FFCC /* TestHelpers.swift */,
				4C256A4E1B09656A0065714F /* Core */,
				4C7C8D201B9D0D7300948136 /* Extensions */,
				4C256A4F1B09656E0065714F /* Features */,
				4C3238E91B3617A600FE04AE /* Resources */,
				F8111E4019A95C8B0040E7D1 /* Supporting Files */,
				31EF4BF4279646000048A19D /* Test Plans */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		F8111E4019A95C8B0040E7D1 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				F8111E4119A95C8B0040E7D1 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		31293064263E17D600473CEA /* Alamofire watchOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 3129306F263E17D600473CEA /* Build configuration list for PBXNativeTarget "Alamofire watchOS Tests" */;
			buildPhases = (
				31293061263E17D600473CEA /* Sources */,
				31293062263E17D600473CEA /* Frameworks */,
				31293063263E17D600473CEA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				3129306C263E17D600473CEA /* PBXTargetDependency */,
			);
			name = "Alamofire watchOS Tests";
			productName = "Alamofire watchOS Tests";
			productReference = 31293065263E17D600473CEA /* Alamofire watchOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		317338C32A43A4FA00D4EA0A /* Alamofire visionOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 317338CA2A43A4FB00D4EA0A /* Build configuration list for PBXNativeTarget "Alamofire visionOS" */;
			buildPhases = (
				317338C02A43A4FA00D4EA0A /* Sources */,
				317338C12A43A4FA00D4EA0A /* Frameworks */,
				317338C22A43A4FA00D4EA0A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Alamofire visionOS";
			productName = "Alamofire visionOS";
			productReference = 317338C42A43A4FA00D4EA0A /* Alamofire.framework */;
			productType = "com.apple.product-type.framework";
		};
		317338F32A43BE5F00D4EA0A /* Alamofire visionOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 317338FB2A43BE5F00D4EA0A /* Build configuration list for PBXNativeTarget "Alamofire visionOS Tests" */;
			buildPhases = (
				317338F02A43BE5F00D4EA0A /* Sources */,
				317338F12A43BE5F00D4EA0A /* Frameworks */,
				317338F22A43BE5F00D4EA0A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				317338FA2A43BE5F00D4EA0A /* PBXTargetDependency */,
			);
			name = "Alamofire visionOS Tests";
			productName = "Alamofire visionOS Tests";
			productReference = 317338F42A43BE5F00D4EA0A /* Alamofire visionOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		4CF626EE1BA7CB3E0011A099 /* Alamofire tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4CF627041BA7CB3E0011A099 /* Build configuration list for PBXNativeTarget "Alamofire tvOS" */;
			buildPhases = (
				4CF626EA1BA7CB3E0011A099 /* Sources */,
				4CF626EB1BA7CB3E0011A099 /* Frameworks */,
				4CF626ED1BA7CB3E0011A099 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Alamofire tvOS";
			productName = "Alamofire tvOS";
			productReference = 4CF626EF1BA7CB3E0011A099 /* Alamofire.framework */;
			productType = "com.apple.product-type.framework";
		};
		4CF626F71BA7CB3E0011A099 /* Alamofire tvOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4CF627051BA7CB3E0011A099 /* Build configuration list for PBXNativeTarget "Alamofire tvOS Tests" */;
			buildPhases = (
				4CF626F41BA7CB3E0011A099 /* Sources */,
				4CF626F51BA7CB3E0011A099 /* Frameworks */,
				4CF626F61BA7CB3E0011A099 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				4CF626FB1BA7CB3E0011A099 /* PBXTargetDependency */,
			);
			name = "Alamofire tvOS Tests";
			productName = "Alamofire tvOSTests";
			productReference = 4CF626F81BA7CB3E0011A099 /* Alamofire tvOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		4DD67C0A1A5C55C900ED2280 /* Alamofire macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 4DD67C1E1A5C55C900ED2280 /* Build configuration list for PBXNativeTarget "Alamofire macOS" */;
			buildPhases = (
				4DD67C061A5C55C900ED2280 /* Sources */,
				4DD67C071A5C55C900ED2280 /* Frameworks */,
				4DD67C091A5C55C900ED2280 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Alamofire macOS";
			productName = AlamofireOSX;
			productReference = 4DD67C0B1A5C55C900ED2280 /* Alamofire.framework */;
			productType = "com.apple.product-type.framework";
		};
		E4202FCD1B667AA100C997FB /* Alamofire watchOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4202FDD1B667AA100C997FB /* Build configuration list for PBXNativeTarget "Alamofire watchOS" */;
			buildPhases = (
				E4202FCE1B667AA100C997FB /* Sources */,
				E4202FD91B667AA100C997FB /* Frameworks */,
				E4202FDC1B667AA100C997FB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Alamofire watchOS";
			productName = Alamofire;
			productReference = E4202FE01B667AA100C997FB /* Alamofire.framework */;
			productType = "com.apple.product-type.framework";
		};
		F8111E3219A95C8B0040E7D1 /* Alamofire iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8111E4619A95C8B0040E7D1 /* Build configuration list for PBXNativeTarget "Alamofire iOS" */;
			buildPhases = (
				F8111E2E19A95C8B0040E7D1 /* Sources */,
				F8111E2F19A95C8B0040E7D1 /* Frameworks */,
				F8111E3119A95C8B0040E7D1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Alamofire iOS";
			productName = Alamofire;
			productReference = F8111E3319A95C8B0040E7D1 /* Alamofire.framework */;
			productType = "com.apple.product-type.framework";
		};
		F8111E3D19A95C8B0040E7D1 /* Alamofire iOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8111E4919A95C8B0040E7D1 /* Build configuration list for PBXNativeTarget "Alamofire iOS Tests" */;
			buildPhases = (
				F8111E3A19A95C8B0040E7D1 /* Sources */,
				F8111E3B19A95C8B0040E7D1 /* Frameworks */,
				F8111E3C19A95C8B0040E7D1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F8111E6619A967880040E7D1 /* PBXTargetDependency */,
			);
			name = "Alamofire iOS Tests";
			productName = AlamofireTests;
			productReference = F8111E3E19A95C8B0040E7D1 /* Alamofire iOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F829C6B11A7A94F100A2CD59 /* Alamofire macOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F829C6BB1A7A94F100A2CD59 /* Build configuration list for PBXNativeTarget "Alamofire macOS Tests" */;
			buildPhases = (
				F829C6AE1A7A94F100A2CD59 /* Sources */,
				F829C6AF1A7A94F100A2CD59 /* Frameworks */,
				F829C6B01A7A94F100A2CD59 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F829C6BA1A7A94F100A2CD59 /* PBXTargetDependency */,
			);
			name = "Alamofire macOS Tests";
			productName = "Alamofire OSX Tests";
			productReference = F829C6B21A7A94F100A2CD59 /* Alamofire macOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F8111E2A19A95C8B0040E7D1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1600;
				ORGANIZATIONNAME = Alamofire;
				TargetAttributes = {
					31293064263E17D600473CEA = {
						CreatedOnToolsVersion = 12.5;
					};
					317338C32A43A4FA00D4EA0A = {
						CreatedOnToolsVersion = 15.0;
					};
					317338F32A43BE5F00D4EA0A = {
						CreatedOnToolsVersion = 15.0;
					};
					4CF626EE1BA7CB3E0011A099 = {
						CreatedOnToolsVersion = 7.1;
						LastSwiftMigration = 0900;
					};
					4CF626F71BA7CB3E0011A099 = {
						CreatedOnToolsVersion = 7.1;
						LastSwiftMigration = 0900;
						ProvisioningStyle = Manual;
					};
					4DD67C0A1A5C55C900ED2280 = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0900;
					};
					E4202FCD1B667AA100C997FB = {
						LastSwiftMigration = 0900;
					};
					F8111E3219A95C8B0040E7D1 = {
						CreatedOnToolsVersion = 6.0;
						LastSwiftMigration = 1020;
					};
					F8111E3D19A95C8B0040E7D1 = {
						CreatedOnToolsVersion = 6.0;
						LastSwiftMigration = 1020;
					};
					F829C6B11A7A94F100A2CD59 = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 0900;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = F8111E2D19A95C8B0040E7D1 /* Build configuration list for PBXProject "Alamofire" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F8111E2919A95C8B0040E7D1;
			productRefGroup = F8111E3419A95C8B0040E7D1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F8111E3219A95C8B0040E7D1 /* Alamofire iOS */,
				F8111E3D19A95C8B0040E7D1 /* Alamofire iOS Tests */,
				4DD67C0A1A5C55C900ED2280 /* Alamofire macOS */,
				F829C6B11A7A94F100A2CD59 /* Alamofire macOS Tests */,
				4CF626EE1BA7CB3E0011A099 /* Alamofire tvOS */,
				4CF626F71BA7CB3E0011A099 /* Alamofire tvOS Tests */,
				E4202FCD1B667AA100C997FB /* Alamofire watchOS */,
				31293064263E17D600473CEA /* Alamofire watchOS Tests */,
				317338C32A43A4FA00D4EA0A /* Alamofire visionOS */,
				317338F32A43BE5F00D4EA0A /* Alamofire visionOS Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		31293063263E17D600473CEA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31181E4F2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */,
				31181E632794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */,
				31181E772794FF9600E88600 /* expired.cer in Resources */,
				31181E332794FF9600E88600 /* certPEM.cer in Resources */,
				312930AA263E187200473CEA /* unicorn.png in Resources */,
				31181E432794FF9600E88600 /* certDER.cer in Resources */,
				312930AE263E187500473CEA /* invalid_data.json in Resources */,
				312930AD263E187500473CEA /* valid_data.json in Resources */,
				312930B1263E187800473CEA /* utf32_string.txt in Resources */,
				31181E5F2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */,
				312930AF263E187800473CEA /* empty_string.txt in Resources */,
				31181E6F2794FF9600E88600 /* valid-uri.cer in Resources */,
				31181E7F2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */,
				31181E832794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */,
				31181E4B2794FF9600E88600 /* certDER.crt in Resources */,
				31181E372794FF9600E88600 /* randomGibberish.crt in Resources */,
				31181E532794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */,
				31181E7B2794FF9600E88600 /* valid-dns-name.cer in Resources */,
				31181E8B2794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */,
				31181E3F2794FF9600E88600 /* keyDER.der in Resources */,
				31181E5B2794FF9600E88600 /* signed-by-ca1.cer in Resources */,
				312930AB263E187200473CEA /* rainbow.jpg in Resources */,
				31181E672794FF9600E88600 /* test.alamofire.org.cer in Resources */,
				312930B0263E187800473CEA /* utf8_string.txt in Resources */,
				31181E6B2794FF9600E88600 /* alamofire-root-ca.cer in Resources */,
				31181E572794FF9600E88600 /* signed-by-ca2.cer in Resources */,
				31181E872794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */,
				312930AC263E187500473CEA /* empty_data.json in Resources */,
				31181E732794FF9600E88600 /* multiple-dns-names.cer in Resources */,
				31181E472794FF9600E88600 /* certPEM.crt in Resources */,
				31181E3B2794FF9600E88600 /* certDER.der in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338C22A43A4FA00D4EA0A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31BC5E7F2B7E9D730069BDEF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338F22A43BE5F00D4EA0A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				317339242A43BEAC00D4EA0A /* alamofire-signing-ca2.cer in Resources */,
				317339252A43BEAC00D4EA0A /* alamofire-signing-ca1.cer in Resources */,
				317339262A43BEAC00D4EA0A /* signed-by-ca2.cer in Resources */,
				317339272A43BEAC00D4EA0A /* signed-by-ca1.cer in Resources */,
				317339282A43BEAC00D4EA0A /* missing-dns-name-and-uri.cer in Resources */,
				317339292A43BEAC00D4EA0A /* wildcard.alamofire.org.cer in Resources */,
				3173392A2A43BEAC00D4EA0A /* test.alamofire.org.cer in Resources */,
				3173392B2A43BEAC00D4EA0A /* alamofire-root-ca.cer in Resources */,
				3173392C2A43BEAC00D4EA0A /* valid-uri.cer in Resources */,
				3173392D2A43BEAC00D4EA0A /* multiple-dns-names.cer in Resources */,
				3173392E2A43BEAC00D4EA0A /* expired.cer in Resources */,
				3173392F2A43BEAC00D4EA0A /* valid-dns-name.cer in Resources */,
				317339302A43BEAC00D4EA0A /* expired.badssl.com-intermediate-ca-1.cer in Resources */,
				317339312A43BEAC00D4EA0A /* expired.badssl.com-intermediate-ca-2.cer in Resources */,
				317339322A43BEAC00D4EA0A /* expired.badssl.com-root-ca.cer in Resources */,
				317339332A43BEAC00D4EA0A /* expired.badssl.com-leaf.cer in Resources */,
				317339342A43BEAC00D4EA0A /* certPEM.cer in Resources */,
				317339352A43BEAC00D4EA0A /* randomGibberish.crt in Resources */,
				317339362A43BEAC00D4EA0A /* certDER.der in Resources */,
				317339372A43BEAC00D4EA0A /* keyDER.der in Resources */,
				317339382A43BEAC00D4EA0A /* certDER.cer in Resources */,
				317339392A43BEAC00D4EA0A /* certPEM.crt in Resources */,
				3173393A2A43BEAC00D4EA0A /* certDER.crt in Resources */,
				3173393B2A43BEAC00D4EA0A /* rainbow.jpg in Resources */,
				3173393C2A43BEAC00D4EA0A /* unicorn.png in Resources */,
				3173393D2A43BEAC00D4EA0A /* empty_data.json in Resources */,
				3173393E2A43BEAC00D4EA0A /* invalid_data.json in Resources */,
				3173393F2A43BEAC00D4EA0A /* valid_data.json in Resources */,
				317339402A43BEAC00D4EA0A /* empty_string.txt in Resources */,
				317339412A43BEAC00D4EA0A /* utf8_string.txt in Resources */,
				317339422A43BEAC00D4EA0A /* utf32_string.txt in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626ED1BA7CB3E0011A099 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31BC5E7D2B7E9D660069BDEF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626F61BA7CB3E0011A099 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31181E4E2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */,
				31181E622794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */,
				31181E762794FF9600E88600 /* expired.cer in Resources */,
				31181E322794FF9600E88600 /* certPEM.cer in Resources */,
				4CFB03091D7D2FA20056F249 /* empty_string.txt in Resources */,
				31181E422794FF9600E88600 /* certDER.cer in Resources */,
				4CF627341BA7CC300011A099 /* rainbow.jpg in Resources */,
				4CFB02FD1D7D2FA20056F249 /* valid_data.json in Resources */,
				4CFB030C1D7D2FA20056F249 /* utf32_string.txt in Resources */,
				31181E5E2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */,
				4CFB030F1D7D2FA20056F249 /* utf8_string.txt in Resources */,
				31181E6E2794FF9600E88600 /* valid-uri.cer in Resources */,
				31181E7E2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */,
				31181E822794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */,
				31181E4A2794FF9600E88600 /* certDER.crt in Resources */,
				31181E362794FF9600E88600 /* randomGibberish.crt in Resources */,
				31181E522794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */,
				31181E7A2794FF9600E88600 /* valid-dns-name.cer in Resources */,
				31181E8A2794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */,
				31181E3E2794FF9600E88600 /* keyDER.der in Resources */,
				31181E5A2794FF9600E88600 /* signed-by-ca1.cer in Resources */,
				4CF627351BA7CC300011A099 /* unicorn.png in Resources */,
				31181E662794FF9600E88600 /* test.alamofire.org.cer in Resources */,
				4CFB02FA1D7D2FA20056F249 /* invalid_data.json in Resources */,
				31181E6A2794FF9600E88600 /* alamofire-root-ca.cer in Resources */,
				31181E562794FF9600E88600 /* signed-by-ca2.cer in Resources */,
				31181E862794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */,
				4CFB02F71D7D2FA20056F249 /* empty_data.json in Resources */,
				31181E722794FF9600E88600 /* multiple-dns-names.cer in Resources */,
				31181E462794FF9600E88600 /* certPEM.crt in Resources */,
				31181E3A2794FF9600E88600 /* certDER.der in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DD67C091A5C55C900ED2280 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31BC5E7C2B7E9D5F0069BDEF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4202FDC1B667AA100C997FB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31BC5E7E2B7E9D6D0069BDEF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E3119A95C8B0040E7D1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31BC5E7B2B7E9D520069BDEF /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E3C19A95C8B0040E7D1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31181E4C2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */,
				31181E602794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */,
				31181E742794FF9600E88600 /* expired.cer in Resources */,
				31181E302794FF9600E88600 /* certPEM.cer in Resources */,
				4CFB03071D7D2FA20056F249 /* empty_string.txt in Resources */,
				31181E402794FF9600E88600 /* certDER.cer in Resources */,
				4C33A13B1B5207DB00873DFF /* unicorn.png in Resources */,
				4CFB02FB1D7D2FA20056F249 /* valid_data.json in Resources */,
				4CFB030A1D7D2FA20056F249 /* utf32_string.txt in Resources */,
				31181E5C2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */,
				4CFB030D1D7D2FA20056F249 /* utf8_string.txt in Resources */,
				31181E6C2794FF9600E88600 /* valid-uri.cer in Resources */,
				31181E7C2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */,
				31181E802794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */,
				31181E482794FF9600E88600 /* certDER.crt in Resources */,
				31181E342794FF9600E88600 /* randomGibberish.crt in Resources */,
				31181E502794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */,
				31181E782794FF9600E88600 /* valid-dns-name.cer in Resources */,
				31181E882794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */,
				31181E3C2794FF9600E88600 /* keyDER.der in Resources */,
				31181E582794FF9600E88600 /* signed-by-ca1.cer in Resources */,
				4C33A1391B5207DB00873DFF /* rainbow.jpg in Resources */,
				31181E642794FF9600E88600 /* test.alamofire.org.cer in Resources */,
				4CFB02F81D7D2FA20056F249 /* invalid_data.json in Resources */,
				31181E682794FF9600E88600 /* alamofire-root-ca.cer in Resources */,
				31181E542794FF9600E88600 /* signed-by-ca2.cer in Resources */,
				31181E842794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */,
				4CFB02F51D7D2FA20056F249 /* empty_data.json in Resources */,
				31181E702794FF9600E88600 /* multiple-dns-names.cer in Resources */,
				31181E442794FF9600E88600 /* certPEM.crt in Resources */,
				31181E382794FF9600E88600 /* certDER.der in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F829C6B01A7A94F100A2CD59 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31181E4D2794FF9600E88600 /* alamofire-signing-ca2.cer in Resources */,
				31181E612794FF9600E88600 /* wildcard.alamofire.org.cer in Resources */,
				31181E752794FF9600E88600 /* expired.cer in Resources */,
				31181E312794FF9600E88600 /* certPEM.cer in Resources */,
				4CFB03081D7D2FA20056F249 /* empty_string.txt in Resources */,
				31181E412794FF9600E88600 /* certDER.cer in Resources */,
				4C33A13C1B5207DB00873DFF /* unicorn.png in Resources */,
				4CFB02FC1D7D2FA20056F249 /* valid_data.json in Resources */,
				4CFB030B1D7D2FA20056F249 /* utf32_string.txt in Resources */,
				31181E5D2794FF9600E88600 /* missing-dns-name-and-uri.cer in Resources */,
				4CFB030E1D7D2FA20056F249 /* utf8_string.txt in Resources */,
				31181E6D2794FF9600E88600 /* valid-uri.cer in Resources */,
				31181E7D2794FF9600E88600 /* expired.badssl.com-intermediate-ca-1.cer in Resources */,
				31181E812794FF9600E88600 /* expired.badssl.com-intermediate-ca-2.cer in Resources */,
				31181E492794FF9600E88600 /* certDER.crt in Resources */,
				31181E352794FF9600E88600 /* randomGibberish.crt in Resources */,
				31181E512794FF9600E88600 /* alamofire-signing-ca1.cer in Resources */,
				31181E792794FF9600E88600 /* valid-dns-name.cer in Resources */,
				31181E892794FF9600E88600 /* expired.badssl.com-leaf.cer in Resources */,
				31181E3D2794FF9600E88600 /* keyDER.der in Resources */,
				31181E592794FF9600E88600 /* signed-by-ca1.cer in Resources */,
				4C33A13A1B5207DB00873DFF /* rainbow.jpg in Resources */,
				31181E652794FF9600E88600 /* test.alamofire.org.cer in Resources */,
				4CFB02F91D7D2FA20056F249 /* invalid_data.json in Resources */,
				31181E692794FF9600E88600 /* alamofire-root-ca.cer in Resources */,
				31181E552794FF9600E88600 /* signed-by-ca2.cer in Resources */,
				31181E852794FF9600E88600 /* expired.badssl.com-root-ca.cer in Resources */,
				4CFB02F61D7D2FA20056F249 /* empty_data.json in Resources */,
				31181E712794FF9600E88600 /* multiple-dns-names.cer in Resources */,
				31181E452794FF9600E88600 /* certPEM.crt in Resources */,
				31181E392794FF9600E88600 /* certDER.der in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		31293061263E17D600473CEA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				312FC5012CB079E800E48EAB /* InternalHelpers.swift in Sources */,
				31293080263E184000473CEA /* AFError+AlamofireTests.swift in Sources */,
				31293077263E183C00473CEA /* ResponseTests.swift in Sources */,
				31293081263E184000473CEA /* FileManager+AlamofireTests.swift in Sources */,
				3145E0EA2797DA4200949557 /* ConcurrencyTests.swift in Sources */,
				31293076263E183C00473CEA /* DownloadTests.swift in Sources */,
				3129308D263E184500473CEA /* ValidationTests.swift in Sources */,
				31293084263E184500473CEA /* RetryPolicyTests.swift in Sources */,
				3129307F263E183C00473CEA /* ParameterEncodingTests.swift in Sources */,
				3129308C263E184500473CEA /* ResponseSerializationTests.swift in Sources */,
				31293074263E183C00473CEA /* RequestTests.swift in Sources */,
				31293073263E183800473CEA /* NSLoggingEventMonitor.swift in Sources */,
				31293071263E183800473CEA /* LeaksTests.swift in Sources */,
				31293090263E184500473CEA /* TLSEvaluationTests.swift in Sources */,
				31293075263E183C00473CEA /* SessionDelegateTests.swift in Sources */,
				314998ED27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */,
				3129308B263E184500473CEA /* CacheTests.swift in Sources */,
				3129307A263E183C00473CEA /* UploadTests.swift in Sources */,
				31293091263E184500473CEA /* URLProtocolTests.swift in Sources */,
				3129307C263E183C00473CEA /* ParameterEncoderTests.swift in Sources */,
				3129308A263E184500473CEA /* AuthenticationInterceptorTests.swift in Sources */,
				31293086263E184500473CEA /* CombineTests.swift in Sources */,
				3129307E263E183C00473CEA /* RequestModifierTests.swift in Sources */,
				3129307B263E183C00473CEA /* AuthenticationTests.swift in Sources */,
				31293083263E184500473CEA /* NetworkReachabilityManagerTests.swift in Sources */,
				3129308E263E184500473CEA /* CachedResponseHandlerTests.swift in Sources */,
				31293087263E184500473CEA /* RedirectHandlerTests.swift in Sources */,
				31293089263E184500473CEA /* ServerTrustEvaluatorTests.swift in Sources */,
				31E382E126477307004533B3 /* WebSocketTests.swift in Sources */,
				3129307D263E183C00473CEA /* DataStreamTests.swift in Sources */,
				31293085263E184500473CEA /* HTTPHeadersTests.swift in Sources */,
				31293079263E183C00473CEA /* InternalRequestTests.swift in Sources */,
				3129308F263E184500473CEA /* MultipartFormDataTests.swift in Sources */,
				31293078263E183C00473CEA /* SessionTests.swift in Sources */,
				31181E152794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */,
				31293088263E184500473CEA /* RequestInterceptorTests.swift in Sources */,
				31293082263E184500473CEA /* ProtectedTests.swift in Sources */,
				31293070263E183500473CEA /* BaseTestCase.swift in Sources */,
				31293072263E183800473CEA /* TestHelpers.swift in Sources */,
				31293092263E184900473CEA /* Result+AlamofireTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338C02A43A4FA00D4EA0A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				317338CB2A43A51100D4EA0A /* Alamofire.swift in Sources */,
				317338CC2A43A51100D4EA0A /* AFError.swift in Sources */,
				317338CD2A43A51100D4EA0A /* HTTPHeaders.swift in Sources */,
				317338CE2A43A51100D4EA0A /* HTTPMethod.swift in Sources */,
				318702FC2B0AEEE400C10A8C /* UploadRequest.swift in Sources */,
				317338CF2A43A51100D4EA0A /* Notifications.swift in Sources */,
				317338D02A43A51100D4EA0A /* ParameterEncoder.swift in Sources */,
				318703082B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */,
				317338D12A43A51100D4EA0A /* ParameterEncoding.swift in Sources */,
				317338D22A43A51100D4EA0A /* Protected.swift in Sources */,
				317338D32A43A51100D4EA0A /* Request.swift in Sources */,
				317338D42A43A51100D4EA0A /* RequestTaskMap.swift in Sources */,
				317338D52A43A51100D4EA0A /* Response.swift in Sources */,
				317338D62A43A51100D4EA0A /* Session.swift in Sources */,
				317338D72A43A51100D4EA0A /* SessionDelegate.swift in Sources */,
				317338D82A43A51100D4EA0A /* URLConvertible+URLRequestConvertible.swift in Sources */,
				317338D92A43A51100D4EA0A /* DispatchQueue+Alamofire.swift in Sources */,
				317338DA2A43A51100D4EA0A /* OperationQueue+Alamofire.swift in Sources */,
				317338DB2A43A51100D4EA0A /* Result+Alamofire.swift in Sources */,
				318702F02B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */,
				318702F62B0AEE3700C10A8C /* DataRequest.swift in Sources */,
				317338DC2A43A51100D4EA0A /* StringEncoding+Alamofire.swift in Sources */,
				318703022B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */,
				317338DD2A43A51100D4EA0A /* URLRequest+Alamofire.swift in Sources */,
				317338DE2A43A51100D4EA0A /* URLSessionConfiguration+Alamofire.swift in Sources */,
				317338DF2A43A51100D4EA0A /* AlamofireExtended.swift in Sources */,
				317338E02A43A51100D4EA0A /* AuthenticationInterceptor.swift in Sources */,
				317338E12A43A51100D4EA0A /* CachedResponseHandler.swift in Sources */,
				317338E22A43A51100D4EA0A /* Combine.swift in Sources */,
				317338E32A43A51100D4EA0A /* Concurrency.swift in Sources */,
				317338E42A43A51100D4EA0A /* EventMonitor.swift in Sources */,
				317338E52A43A51100D4EA0A /* MultipartFormData.swift in Sources */,
				317338E62A43A51100D4EA0A /* MultipartUpload.swift in Sources */,
				317338E72A43A51100D4EA0A /* NetworkReachabilityManager.swift in Sources */,
				317338E82A43A51100D4EA0A /* RedirectHandler.swift in Sources */,
				317338E92A43A51100D4EA0A /* RequestCompression.swift in Sources */,
				317338EA2A43A51100D4EA0A /* RequestInterceptor.swift in Sources */,
				317338EB2A43A51100D4EA0A /* ResponseSerialization.swift in Sources */,
				317338EC2A43A51100D4EA0A /* RetryPolicy.swift in Sources */,
				317338ED2A43A51100D4EA0A /* ServerTrustEvaluation.swift in Sources */,
				317338EE2A43A51100D4EA0A /* URLEncodedFormEncoder.swift in Sources */,
				317338EF2A43A51100D4EA0A /* Validation.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		317338F02A43BE5F00D4EA0A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				312FC5022CB079E800E48EAB /* InternalHelpers.swift in Sources */,
				317338FE2A43BE9000D4EA0A /* BaseTestCase.swift in Sources */,
				317338FF2A43BE9000D4EA0A /* LeaksTests.swift in Sources */,
				317339002A43BE9000D4EA0A /* NSLoggingEventMonitor.swift in Sources */,
				317339012A43BE9000D4EA0A /* TestHelpers.swift in Sources */,
				317339022A43BE9000D4EA0A /* AuthenticationTests.swift in Sources */,
				317339032A43BE9000D4EA0A /* DataStreamTests.swift in Sources */,
				317339042A43BE9000D4EA0A /* DownloadTests.swift in Sources */,
				31F032542ABB9C0900D68FB2 /* WebSocketTests.swift in Sources */,
				317339052A43BE9000D4EA0A /* InternalRequestTests.swift in Sources */,
				317339062A43BE9000D4EA0A /* ParameterEncoderTests.swift in Sources */,
				317339072A43BE9000D4EA0A /* ParameterEncodingTests.swift in Sources */,
				317339082A43BE9000D4EA0A /* RequestModifierTests.swift in Sources */,
				317339092A43BE9000D4EA0A /* RequestTests.swift in Sources */,
				3173390A2A43BE9000D4EA0A /* ResponseTests.swift in Sources */,
				3173390B2A43BE9000D4EA0A /* SessionDelegateTests.swift in Sources */,
				3173390C2A43BE9000D4EA0A /* SessionTests.swift in Sources */,
				3173390D2A43BE9000D4EA0A /* UploadTests.swift in Sources */,
				3173390E2A43BE9000D4EA0A /* AFError+AlamofireTests.swift in Sources */,
				3173390F2A43BE9000D4EA0A /* Bundle+AlamofireTests.swift in Sources */,
				317339102A43BE9000D4EA0A /* FileManager+AlamofireTests.swift in Sources */,
				317339112A43BE9000D4EA0A /* Request+AlamofireTests.swift in Sources */,
				317339122A43BE9000D4EA0A /* Result+AlamofireTests.swift in Sources */,
				317339132A43BE9000D4EA0A /* AuthenticationInterceptorTests.swift in Sources */,
				317339142A43BE9000D4EA0A /* CachedResponseHandlerTests.swift in Sources */,
				317339152A43BE9000D4EA0A /* CacheTests.swift in Sources */,
				317339162A43BE9000D4EA0A /* CombineTests.swift in Sources */,
				317339172A43BE9000D4EA0A /* ConcurrencyTests.swift in Sources */,
				317339182A43BE9000D4EA0A /* HTTPHeadersTests.swift in Sources */,
				317339192A43BE9000D4EA0A /* MultipartFormDataTests.swift in Sources */,
				3173391A2A43BE9000D4EA0A /* NetworkReachabilityManagerTests.swift in Sources */,
				3173391B2A43BE9000D4EA0A /* ProtectedTests.swift in Sources */,
				3173391C2A43BE9000D4EA0A /* RedirectHandlerTests.swift in Sources */,
				3173391D2A43BE9000D4EA0A /* RequestInterceptorTests.swift in Sources */,
				3173391E2A43BE9000D4EA0A /* ResponseSerializationTests.swift in Sources */,
				3173391F2A43BE9000D4EA0A /* RetryPolicyTests.swift in Sources */,
				317339202A43BE9000D4EA0A /* ServerTrustEvaluatorTests.swift in Sources */,
				317339212A43BE9000D4EA0A /* TLSEvaluationTests.swift in Sources */,
				317339222A43BE9000D4EA0A /* URLProtocolTests.swift in Sources */,
				317339232A43BE9000D4EA0A /* ValidationTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626EA1BA7CB3E0011A099 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4CF627081BA7CBF60011A099 /* AFError.swift in Sources */,
				3191B5771F5F53A6003960A8 /* Protected.swift in Sources */,
				3199179A209CDA7F00103A19 /* Response.swift in Sources */,
				31D83FD020D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */,
				318702FA2B0AEEE400C10A8C /* UploadRequest.swift in Sources */,
				31B3DE3D25C11CEA00760641 /* Concurrency.swift in Sources */,
				319917A7209CDAC400103A19 /* RequestTaskMap.swift in Sources */,
				318703062B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */,
				4CF627131BA7CBF60011A099 /* Validation.swift in Sources */,
				31F5085F20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */,
				4196936422FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */,
				3172741F218BB1790039FFCC /* ParameterEncoder.swift in Sources */,
				4C67D1382454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */,
				319917BB209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */,
				319917AC209CDCB000103A19 /* HTTPHeaders.swift in Sources */,
				3172741A218BAEC90039FFCC /* HTTPMethod.swift in Sources */,
				4C0CB648220CA8A400604EDC /* RedirectHandler.swift in Sources */,
				4CF6270E1BA7CBF60011A099 /* MultipartFormData.swift in Sources */,
				4CB9282B1C66BFBC00CE5F08 /* Notifications.swift in Sources */,
				318702EE2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */,
				318702F42B0AEE3700C10A8C /* DataRequest.swift in Sources */,
				31FB2F8922C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */,
				318703002B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */,
				4C0CB643220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */,
				4CF6270F1BA7CBF60011A099 /* ResponseSerialization.swift in Sources */,
				4C256A0821EEB69000AD5D87 /* RequestInterceptor.swift in Sources */,
				3165407529AEBC0400C9BE08 /* RequestCompression.swift in Sources */,
				4C43669D1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */,
				315A4C58241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */,
				4C3D00561C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */,
				311B199220B0E3480036823B /* MultipartUpload.swift in Sources */,
				4C4466ED21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */,
				319917A2209CDA7F00103A19 /* SessionDelegate.swift in Sources */,
				4CF6270A1BA7CBF60011A099 /* ParameterEncoding.swift in Sources */,
				4C256A1C21F1449C00AD5D87 /* RetryPolicy.swift in Sources */,
				31991796209CDA7F00103A19 /* Request.swift in Sources */,
				4CF627101BA7CBF60011A099 /* ServerTrustEvaluation.swift in Sources */,
				318DD4112439780500963291 /* Combine.swift in Sources */,
				3199179E209CDA7F00103A19 /* Session.swift in Sources */,
				4CF627071BA7CBF60011A099 /* Alamofire.swift in Sources */,
				3111CE8A20A77945008315E2 /* EventMonitor.swift in Sources */,
				31DADDFD224811ED0051390F /* AlamofireExtended.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4CF626F41BA7CB3E0011A099 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				312FC5002CB079E800E48EAB /* InternalHelpers.swift in Sources */,
				31B51E8E2434FECB005356DB /* RequestModifierTests.swift in Sources */,
				4CF627181BA7CC240011A099 /* RequestTests.swift in Sources */,
				3111CE9720A7EC3A008315E2 /* ServerTrustEvaluatorTests.swift in Sources */,
				3111CE9420A7EC32008315E2 /* ResponseSerializationTests.swift in Sources */,
				3111CE8E20A7EBE7008315E2 /* MultipartFormDataTests.swift in Sources */,
				311B199620B0ED990036823B /* UploadTests.swift in Sources */,
				4C0CB633220BC70300604EDC /* RequestInterceptorTests.swift in Sources */,
				3107EA3720A11AE200445260 /* AuthenticationTests.swift in Sources */,
				31ED52EA1D73891C00199085 /* AFError+AlamofireTests.swift in Sources */,
				4C0CB64D220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */,
				3107EA3A20A11F9700445260 /* ResponseTests.swift in Sources */,
				4CFB02921D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */,
				4CF627141BA7CC240011A099 /* BaseTestCase.swift in Sources */,
				4C7DD7ED224C627300249836 /* Result+AlamofireTests.swift in Sources */,
				314998EC27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */,
				3106FB6323F8C53A007FAB43 /* ProtectedTests.swift in Sources */,
				4CB0080F2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */,
				31727424218BB9A50039FFCC /* TestHelpers.swift in Sources */,
				311A89C123185BC0003BB714 /* CachedResponseHandlerTests.swift in Sources */,
				31EBD9C320D1D89D00D1FF34 /* ValidationTests.swift in Sources */,
				3111CE8620A76370008315E2 /* SessionTests.swift in Sources */,
				31C2B0F220B271380089BA7C /* TLSEvaluationTests.swift in Sources */,
				3106FB6723F8D9E0007FAB43 /* DataStreamTests.swift in Sources */,
				3111CE9D20A7EC58008315E2 /* URLProtocolTests.swift in Sources */,
				4CBD2182220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */,
				317A6A7820B2208000A9FEC5 /* DownloadTests.swift in Sources */,
				31FEC68E26225A54009D17DB /* WebSocketTests.swift in Sources */,
				31F9683E20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */,
				3113D46D21878227001CCD21 /* HTTPHeadersTests.swift in Sources */,
				31181E142794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */,
				31501E8A2196962A005829F2 /* ParameterEncoderTests.swift in Sources */,
				3107EA4120A1267D00445260 /* SessionDelegateTests.swift in Sources */,
				31C2B0EC20B271060089BA7C /* CacheTests.swift in Sources */,
				3111CE9120A7EC27008315E2 /* NetworkReachabilityManagerTests.swift in Sources */,
				31BADE502439A8D1007D2AB9 /* CombineTests.swift in Sources */,
				31B3DE5125C120D800760641 /* ConcurrencyTests.swift in Sources */,
				31762DCC247738FA0025C704 /* LeaksTests.swift in Sources */,
				31425AC3241F098000EE3CCC /* InternalRequestTests.swift in Sources */,
				4CF627171BA7CC240011A099 /* ParameterEncodingTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DD67C061A5C55C900ED2280 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4CE272501AF88FB500F1D59A /* ParameterEncoding.swift in Sources */,
				3191B5761F5F53A6003960A8 /* Protected.swift in Sources */,
				4CDE2C471AF89FF300BABAE5 /* ResponseSerialization.swift in Sources */,
				31991799209CDA7F00103A19 /* Response.swift in Sources */,
				318702F92B0AEEE400C10A8C /* UploadRequest.swift in Sources */,
				31B3DE3C25C11CEA00760641 /* Concurrency.swift in Sources */,
				31D83FCF20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */,
				318703052B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */,
				319917A6209CDAC400103A19 /* RequestTaskMap.swift in Sources */,
				4C1DC8551B68908E00476DE3 /* AFError.swift in Sources */,
				31F5085E20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */,
				4196936322FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */,
				4C67D1372454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */,
				3172741E218BB1790039FFCC /* ParameterEncoder.swift in Sources */,
				319917BA209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */,
				319917AB209CDCB000103A19 /* HTTPHeaders.swift in Sources */,
				31727419218BAEC90039FFCC /* HTTPMethod.swift in Sources */,
				4C0CB647220CA8A400604EDC /* RedirectHandler.swift in Sources */,
				4CB9282A1C66BFBC00CE5F08 /* Notifications.swift in Sources */,
				318702ED2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */,
				318702F32B0AEE3700C10A8C /* DataRequest.swift in Sources */,
				4DD67C251A5C590000ED2280 /* Alamofire.swift in Sources */,
				318702FF2B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */,
				31FB2F8822C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */,
				4C0CB642220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */,
				4C23EB441B327C5B0090E0BC /* MultipartFormData.swift in Sources */,
				3165407429AEBC0400C9BE08 /* RequestCompression.swift in Sources */,
				4C256A0721EEB69000AD5D87 /* RequestInterceptor.swift in Sources */,
				315A4C57241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */,
				4C43669C1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */,
				4C811F8E1B51856D00E0F59A /* ServerTrustEvaluation.swift in Sources */,
				311B199120B0E3470036823B /* MultipartUpload.swift in Sources */,
				4C4466EC21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */,
				319917A1209CDA7F00103A19 /* SessionDelegate.swift in Sources */,
				4C3D00551C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */,
				4C256A1B21F1449C00AD5D87 /* RetryPolicy.swift in Sources */,
				31991795209CDA7F00103A19 /* Request.swift in Sources */,
				318DD4102439780500963291 /* Combine.swift in Sources */,
				4CDE2C441AF89F0900BABAE5 /* Validation.swift in Sources */,
				3199179D209CDA7F00103A19 /* Session.swift in Sources */,
				3111CE8920A77944008315E2 /* EventMonitor.swift in Sources */,
				31DADDFC224811ED0051390F /* AlamofireExtended.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4202FCE1B667AA100C997FB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4CEE82AD1C6813CF00E9C9F0 /* NetworkReachabilityManager.swift in Sources */,
				E4202FD01B667AA100C997FB /* ParameterEncoding.swift in Sources */,
				3191B5781F5F53A6003960A8 /* Protected.swift in Sources */,
				3199179B209CDA7F00103A19 /* Response.swift in Sources */,
				318702FB2B0AEEE400C10A8C /* UploadRequest.swift in Sources */,
				31B3DE3E25C11CEA00760641 /* Concurrency.swift in Sources */,
				31D83FD120D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */,
				318703072B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */,
				319917A8209CDAC400103A19 /* RequestTaskMap.swift in Sources */,
				4CEC605A1B745C9100E684F4 /* AFError.swift in Sources */,
				31F5086020B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */,
				4196936522FA1EAD001EA5D5 /* Result+Alamofire.swift in Sources */,
				4C67D1392454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */,
				31727420218BB1790039FFCC /* ParameterEncoder.swift in Sources */,
				319917BC209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */,
				319917AD209CDCB000103A19 /* HTTPHeaders.swift in Sources */,
				3172741B218BAEC90039FFCC /* HTTPMethod.swift in Sources */,
				4C0CB649220CA8A400604EDC /* RedirectHandler.swift in Sources */,
				E4202FD21B667AA100C997FB /* ResponseSerialization.swift in Sources */,
				318702EF2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */,
				318702F52B0AEE3700C10A8C /* DataRequest.swift in Sources */,
				4CB9282C1C66BFBC00CE5F08 /* Notifications.swift in Sources */,
				318703012B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */,
				31FB2F8A22C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */,
				4C0CB644220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */,
				4C256A0921EEB69000AD5D87 /* RequestInterceptor.swift in Sources */,
				3165407629AEBC0400C9BE08 /* RequestCompression.swift in Sources */,
				4C43669E1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */,
				315A4C59241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */,
				E4202FD41B667AA100C997FB /* Alamofire.swift in Sources */,
				311B199320B0E3480036823B /* MultipartUpload.swift in Sources */,
				4C4466EE21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */,
				319917A3209CDA7F00103A19 /* SessionDelegate.swift in Sources */,
				E4202FD51B667AA100C997FB /* MultipartFormData.swift in Sources */,
				4C256A1D21F1449C00AD5D87 /* RetryPolicy.swift in Sources */,
				31991797209CDA7F00103A19 /* Request.swift in Sources */,
				E4202FD61B667AA100C997FB /* ServerTrustEvaluation.swift in Sources */,
				318DD4122439780500963291 /* Combine.swift in Sources */,
				3199179F209CDA7F00103A19 /* Session.swift in Sources */,
				E4202FD81B667AA100C997FB /* Validation.swift in Sources */,
				3111CE8B20A77945008315E2 /* EventMonitor.swift in Sources */,
				31DADDFE224811ED0051390F /* AlamofireExtended.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E2E19A95C8B0040E7D1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4CE2724F1AF88FB500F1D59A /* ParameterEncoding.swift in Sources */,
				3191B5751F5F53A6003960A8 /* Protected.swift in Sources */,
				4CDE2C461AF89FF300BABAE5 /* ResponseSerialization.swift in Sources */,
				31991798209CDA7F00103A19 /* Response.swift in Sources */,
				318702F82B0AEEE400C10A8C /* UploadRequest.swift in Sources */,
				31B3DE3B25C11CEA00760641 /* Concurrency.swift in Sources */,
				31D83FCE20D5C29300D93E47 /* URLConvertible+URLRequestConvertible.swift in Sources */,
				318703042B0AEF4B00C10A8C /* DownloadRequest.swift in Sources */,
				319917A5209CDAC400103A19 /* RequestTaskMap.swift in Sources */,
				4C1DC8541B68908E00476DE3 /* AFError.swift in Sources */,
				31F5085D20B50DC400FE2A0C /* URLSessionConfiguration+Alamofire.swift in Sources */,
				4196936222FA1E05001EA5D5 /* Result+Alamofire.swift in Sources */,
				4C67D1362454B12A00CBA725 /* AuthenticationInterceptor.swift in Sources */,
				3172741D218BB1790039FFCC /* ParameterEncoder.swift in Sources */,
				319917B9209CE53A00103A19 /* OperationQueue+Alamofire.swift in Sources */,
				319917AA209CDCB000103A19 /* HTTPHeaders.swift in Sources */,
				31727418218BAEC90039FFCC /* HTTPMethod.swift in Sources */,
				4C0CB646220CA8A400604EDC /* RedirectHandler.swift in Sources */,
				F897FF4119AA800700AB5182 /* Alamofire.swift in Sources */,
				318702EC2B0AEDBB00C10A8C /* WebSocketRequest.swift in Sources */,
				318702F22B0AEE3700C10A8C /* DataRequest.swift in Sources */,
				4C23EB431B327C5B0090E0BC /* MultipartFormData.swift in Sources */,
				318702FE2B0AEF1D00C10A8C /* DataStreamRequest.swift in Sources */,
				31FB2F8722C828D8007FD6D5 /* URLEncodedFormEncoder.swift in Sources */,
				4C0CB641220CA89400604EDC /* URLRequest+Alamofire.swift in Sources */,
				4C811F8D1B51856D00E0F59A /* ServerTrustEvaluation.swift in Sources */,
				3165407329AEBC0400C9BE08 /* RequestCompression.swift in Sources */,
				4C256A0621EEB69000AD5D87 /* RequestInterceptor.swift in Sources */,
				315A4C56241EF28B00D57C7A /* StringEncoding+Alamofire.swift in Sources */,
				4C43669B1D7BB93D00C38AAD /* DispatchQueue+Alamofire.swift in Sources */,
				4C3D00541C66A63000D1F709 /* NetworkReachabilityManager.swift in Sources */,
				311B199020B0D3B40036823B /* MultipartUpload.swift in Sources */,
				4C4466EB21F8F5D800AC9703 /* CachedResponseHandler.swift in Sources */,
				319917A0209CDA7F00103A19 /* SessionDelegate.swift in Sources */,
				4CDE2C431AF89F0900BABAE5 /* Validation.swift in Sources */,
				4C256A1A21F1449C00AD5D87 /* RetryPolicy.swift in Sources */,
				31991794209CDA7F00103A19 /* Request.swift in Sources */,
				318DD40F2439780500963291 /* Combine.swift in Sources */,
				4CB928291C66BFBC00CE5F08 /* Notifications.swift in Sources */,
				3199179C209CDA7F00103A19 /* Session.swift in Sources */,
				3111CE8820A77843008315E2 /* EventMonitor.swift in Sources */,
				31DADDFB224811ED0051390F /* AlamofireExtended.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8111E3A19A95C8B0040E7D1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				312FC5032CB079E800E48EAB /* InternalHelpers.swift in Sources */,
				31B51E8C2434FECB005356DB /* RequestModifierTests.swift in Sources */,
				31ED52E81D73891B00199085 /* AFError+AlamofireTests.swift in Sources */,
				3111CE9520A7EC39008315E2 /* ServerTrustEvaluatorTests.swift in Sources */,
				3111CE9220A7EC30008315E2 /* ResponseSerializationTests.swift in Sources */,
				3111CE8C20A7EBE6008315E2 /* MultipartFormDataTests.swift in Sources */,
				311B199420B0ED980036823B /* UploadTests.swift in Sources */,
				4C0CB631220BC70300604EDC /* RequestInterceptorTests.swift in Sources */,
				3107EA3520A11AE100445260 /* AuthenticationTests.swift in Sources */,
				4CFB02901D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */,
				4C0CB64B220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */,
				3107EA3820A11F9600445260 /* ResponseTests.swift in Sources */,
				F8858DDD19A96B4300F55F93 /* RequestTests.swift in Sources */,
				4C256A531B096C770065714F /* BaseTestCase.swift in Sources */,
				4C7DD7EB224C627300249836 /* Result+AlamofireTests.swift in Sources */,
				314998EA27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */,
				3106FB6123F8C53A007FAB43 /* ProtectedTests.swift in Sources */,
				4CB0080D2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */,
				31727422218BB9A50039FFCC /* TestHelpers.swift in Sources */,
				311A89BF23185BBF003BB714 /* CachedResponseHandlerTests.swift in Sources */,
				31EBD9C120D1D89C00D1FF34 /* ValidationTests.swift in Sources */,
				3111CE8420A7636E008315E2 /* SessionTests.swift in Sources */,
				31C2B0F020B271370089BA7C /* TLSEvaluationTests.swift in Sources */,
				3106FB6523F8D9E0007FAB43 /* DataStreamTests.swift in Sources */,
				3111CE9B20A7EC57008315E2 /* URLProtocolTests.swift in Sources */,
				4CBD2180220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */,
				317A6A7620B2207F00A9FEC5 /* DownloadTests.swift in Sources */,
				31FEC68C26225A54009D17DB /* WebSocketTests.swift in Sources */,
				31F9683C20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */,
				3113D46B21878227001CCD21 /* HTTPHeadersTests.swift in Sources */,
				31181E122794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */,
				31501E882196962A005829F2 /* ParameterEncoderTests.swift in Sources */,
				3107EA3F20A1267C00445260 /* SessionDelegateTests.swift in Sources */,
				31C2B0EA20B271040089BA7C /* CacheTests.swift in Sources */,
				3111CE8F20A7EC26008315E2 /* NetworkReachabilityManagerTests.swift in Sources */,
				31BADE4E2439A8D1007D2AB9 /* CombineTests.swift in Sources */,
				31B3DE4F25C120D800760641 /* ConcurrencyTests.swift in Sources */,
				31762DCA247738FA0025C704 /* LeaksTests.swift in Sources */,
				31425AC1241F098000EE3CCC /* InternalRequestTests.swift in Sources */,
				F8111E6119A9674D0040E7D1 /* ParameterEncodingTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F829C6AE1A7A94F100A2CD59 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				312FC4FF2CB079E800E48EAB /* InternalHelpers.swift in Sources */,
				31B51E8D2434FECB005356DB /* RequestModifierTests.swift in Sources */,
				31ED52E91D73891C00199085 /* AFError+AlamofireTests.swift in Sources */,
				3111CE9620A7EC3A008315E2 /* ServerTrustEvaluatorTests.swift in Sources */,
				3111CE9320A7EC31008315E2 /* ResponseSerializationTests.swift in Sources */,
				3111CE8D20A7EBE7008315E2 /* MultipartFormDataTests.swift in Sources */,
				311B199520B0ED980036823B /* UploadTests.swift in Sources */,
				4C0CB632220BC70300604EDC /* RequestInterceptorTests.swift in Sources */,
				3107EA3620A11AE100445260 /* AuthenticationTests.swift in Sources */,
				4CFB02911D7CF28F0056F249 /* FileManager+AlamofireTests.swift in Sources */,
				4C0CB64C220CA8D600604EDC /* RedirectHandlerTests.swift in Sources */,
				3107EA3920A11F9600445260 /* ResponseTests.swift in Sources */,
				F829C6BE1A7A950600A2CD59 /* ParameterEncodingTests.swift in Sources */,
				F829C6BF1A7A950600A2CD59 /* RequestTests.swift in Sources */,
				4C7DD7EC224C627300249836 /* Result+AlamofireTests.swift in Sources */,
				314998EB27A6560600ABB856 /* Request+AlamofireTests.swift in Sources */,
				3106FB6223F8C53A007FAB43 /* ProtectedTests.swift in Sources */,
				4CB0080E2455FE9700C38783 /* AuthenticationInterceptorTests.swift in Sources */,
				31727423218BB9A50039FFCC /* TestHelpers.swift in Sources */,
				311A89C023185BBF003BB714 /* CachedResponseHandlerTests.swift in Sources */,
				31EBD9C220D1D89C00D1FF34 /* ValidationTests.swift in Sources */,
				3111CE8520A7636F008315E2 /* SessionTests.swift in Sources */,
				31C2B0F120B271370089BA7C /* TLSEvaluationTests.swift in Sources */,
				3106FB6623F8D9E0007FAB43 /* DataStreamTests.swift in Sources */,
				3111CE9C20A7EC58008315E2 /* URLProtocolTests.swift in Sources */,
				4CBD2181220B48AE008F1C59 /* RetryPolicyTests.swift in Sources */,
				317A6A7720B2208000A9FEC5 /* DownloadTests.swift in Sources */,
				31FEC68D26225A54009D17DB /* WebSocketTests.swift in Sources */,
				31F9683D20BB70290009606F /* NSLoggingEventMonitor.swift in Sources */,
				3113D46C21878227001CCD21 /* HTTPHeadersTests.swift in Sources */,
				31181E132794FE5400E88600 /* Bundle+AlamofireTests.swift in Sources */,
				31501E892196962A005829F2 /* ParameterEncoderTests.swift in Sources */,
				3107EA4020A1267C00445260 /* SessionDelegateTests.swift in Sources */,
				31C2B0EB20B271050089BA7C /* CacheTests.swift in Sources */,
				3111CE9020A7EC27008315E2 /* NetworkReachabilityManagerTests.swift in Sources */,
				31BADE4F2439A8D1007D2AB9 /* CombineTests.swift in Sources */,
				31B3DE5025C120D800760641 /* ConcurrencyTests.swift in Sources */,
				31762DCB247738FA0025C704 /* LeaksTests.swift in Sources */,
				31425AC2241F098000EE3CCC /* InternalRequestTests.swift in Sources */,
				4C256A541B096C770065714F /* BaseTestCase.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		3129306C263E17D600473CEA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4202FCD1B667AA100C997FB /* Alamofire watchOS */;
			targetProxy = 3129306B263E17D600473CEA /* PBXContainerItemProxy */;
		};
		317338FA2A43BE5F00D4EA0A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 317338C32A43A4FA00D4EA0A /* Alamofire visionOS */;
			targetProxy = 317338F92A43BE5F00D4EA0A /* PBXContainerItemProxy */;
		};
		4CF626FB1BA7CB3E0011A099 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4CF626EE1BA7CB3E0011A099 /* Alamofire tvOS */;
			targetProxy = 4CF626FA1BA7CB3E0011A099 /* PBXContainerItemProxy */;
		};
		F8111E6619A967880040E7D1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F8111E3219A95C8B0040E7D1 /* Alamofire iOS */;
			targetProxy = F8111E6519A967880040E7D1 /* PBXContainerItemProxy */;
		};
		F829C6BA1A7A94F100A2CD59 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 4DD67C0A1A5C55C900ED2280 /* Alamofire macOS */;
			targetProxy = F829C6B91A7A94F100A2CD59 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		3129306D263E17D600473CEA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-watchOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 7.4;
			};
			name = Debug;
		};
		3129306E263E17D600473CEA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-watchOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 7.4;
			};
			name = Release;
		};
		317338C82A43A4FB00D4EA0A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = xros;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "xros xrsimulator";
				TARGETED_DEVICE_FAMILY = 7;
			};
			name = Debug;
		};
		317338C92A43A4FB00D4EA0A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = xros;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "xros xrsimulator";
				TARGETED_DEVICE_FAMILY = 7;
			};
			name = Release;
		};
		317338FC2A43BE5F00D4EA0A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Tests/Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-visionOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = xros;
				SUPPORTED_PLATFORMS = "xros xrsimulator";
			};
			name = Debug;
		};
		317338FD2A43BE5F00D4EA0A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				INFOPLIST_FILE = Tests/Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-visionOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = xros;
				SUPPORTED_PLATFORMS = "xros xrsimulator";
			};
			name = Release;
		};
		4CF627001BA7CB3E0011A099 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		4CF627011BA7CB3E0011A099 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		4CF627021BA7CB3E0011A099 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
			};
			name = Debug;
		};
		4CF627031BA7CB3E0011A099 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.Alamofire-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
			};
			name = Release;
		};
		4DD67C1F1A5C55C900ED2280 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		4DD67C201A5C55C900ED2280 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEAD_CODE_STRIPPING = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		E4202FDE1B667AA100C997FB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = org.alamofire.Alamofire.watchOS;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		E4202FDF1B667AA100C997FB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = org.alamofire.Alamofire.watchOS;
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
		F8111E4419A95C8B0040E7D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_UNDEFINED_BEHAVIOR_SANITIZER_INTEGER = YES;
				CLANG_UNDEFINED_BEHAVIOR_SANITIZER_NULLABILITY = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Source/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				MARKETING_VERSION = 5.10.2;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"-framework",
					CFNetwork,
				);
				PRODUCT_BUNDLE_IDENTIFIER = org.alamofire.Alamofire;
				PRODUCT_NAME = Alamofire;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_STRICT_CONCURRENCY = "$(SWIFT_STRICT_CONCURRENCY_XCODE_$(XCODE_VERSION_MAJOR))";
				SWIFT_STRICT_CONCURRENCY_XCODE_1500 = minimal;
				SWIFT_STRICT_CONCURRENCY_XCODE_1600 = complete;
				SWIFT_UPCOMING_FEATURE_EXISTENTIAL_ANY = YES;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VERSIONING_SYSTEM = "apple-generic";
				WATCHOS_DEPLOYMENT_TARGET = 3.0;
				XROS_DEPLOYMENT_TARGET = 1.0;
			};
			name = Debug;
		};
		F8111E4519A95C8B0040E7D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_UNDEFINED_BEHAVIOR_SANITIZER_INTEGER = YES;
				CLANG_UNDEFINED_BEHAVIOR_SANITIZER_NULLABILITY = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Source/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				MARKETING_VERSION = 5.10.2;
				OTHER_LDFLAGS = (
					"-framework",
					CFNetwork,
				);
				PRODUCT_BUNDLE_IDENTIFIER = org.alamofire.Alamofire;
				PRODUCT_NAME = Alamofire;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_STRICT_CONCURRENCY = "$(SWIFT_STRICT_CONCURRENCY_XCODE_$(XCODE_VERSION_MAJOR))";
				SWIFT_STRICT_CONCURRENCY_XCODE_1500 = minimal;
				SWIFT_STRICT_CONCURRENCY_XCODE_1600 = complete;
				SWIFT_UPCOMING_FEATURE_EXISTENTIAL_ANY = YES;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				WATCHOS_DEPLOYMENT_TARGET = 3.0;
				XROS_DEPLOYMENT_TARGET = 1.0;
			};
			name = Release;
		};
		F8111E4719A95C8B0040E7D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F8111E4819A95C8B0040E7D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_IDENTITY = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F8111E4A19A95C8B0040E7D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		F8111E4B19A95C8B0040E7D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
			};
			name = Release;
		};
		F829C6BC1A7A94F100A2CD59 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				DEAD_CODE_STRIPPING = YES;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
			};
			name = Debug;
		};
		F829C6BD1A7A94F100A2CD59 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = NO;
				DEAD_CODE_STRIPPING = YES;
				INFOPLIST_FILE = Tests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		3129306F263E17D600473CEA /* Build configuration list for PBXNativeTarget "Alamofire watchOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3129306D263E17D600473CEA /* Debug */,
				3129306E263E17D600473CEA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		317338CA2A43A4FB00D4EA0A /* Build configuration list for PBXNativeTarget "Alamofire visionOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				317338C82A43A4FB00D4EA0A /* Debug */,
				317338C92A43A4FB00D4EA0A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		317338FB2A43BE5F00D4EA0A /* Build configuration list for PBXNativeTarget "Alamofire visionOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				317338FC2A43BE5F00D4EA0A /* Debug */,
				317338FD2A43BE5F00D4EA0A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4CF627041BA7CB3E0011A099 /* Build configuration list for PBXNativeTarget "Alamofire tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4CF627001BA7CB3E0011A099 /* Debug */,
				4CF627011BA7CB3E0011A099 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4CF627051BA7CB3E0011A099 /* Build configuration list for PBXNativeTarget "Alamofire tvOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4CF627021BA7CB3E0011A099 /* Debug */,
				4CF627031BA7CB3E0011A099 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		4DD67C1E1A5C55C900ED2280 /* Build configuration list for PBXNativeTarget "Alamofire macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4DD67C1F1A5C55C900ED2280 /* Debug */,
				4DD67C201A5C55C900ED2280 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4202FDD1B667AA100C997FB /* Build configuration list for PBXNativeTarget "Alamofire watchOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4202FDE1B667AA100C997FB /* Debug */,
				E4202FDF1B667AA100C997FB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8111E2D19A95C8B0040E7D1 /* Build configuration list for PBXProject "Alamofire" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8111E4419A95C8B0040E7D1 /* Debug */,
				F8111E4519A95C8B0040E7D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8111E4619A95C8B0040E7D1 /* Build configuration list for PBXNativeTarget "Alamofire iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8111E4719A95C8B0040E7D1 /* Debug */,
				F8111E4819A95C8B0040E7D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8111E4919A95C8B0040E7D1 /* Build configuration list for PBXNativeTarget "Alamofire iOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8111E4A19A95C8B0040E7D1 /* Debug */,
				F8111E4B19A95C8B0040E7D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F829C6BB1A7A94F100A2CD59 /* Build configuration list for PBXNativeTarget "Alamofire macOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F829C6BC1A7A94F100A2CD59 /* Debug */,
				F829C6BD1A7A94F100A2CD59 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F8111E2A19A95C8B0040E7D1 /* Project object */;
}
