<!DOCTYPE html>
<html lang="en">
  <head>
    <title>RequestInterceptor Protocol Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Protocol/RequestInterceptor" class="dashAnchor"></a>

    <a title="RequestInterceptor Protocol Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Protocols.html">Protocols</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      RequestInterceptor Protocol Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>RequestInterceptor</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">protocol</span> <span class="kt">RequestInterceptor</span> <span class="p">:</span> <span class="kt"><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></span><span class="p">,</span> <span class="kt"><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></span></code></pre>

                </div>
              </div>
            <p>Type that provides both <code><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></code> and <code><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></code> functionality.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF"></a>
                    <a name="//apple_ref/swift/Method/adapt(_:for:completion:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire14RequestAdapterP5adapt_3for10completiony10Foundation10URLRequestV_AA7SessionCys6ResultOyAIs5Error_pGYbctF">adapt(_:<wbr>for:<wbr>completion:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">adapt</span><span class="p">(</span><span class="n">_</span> <span class="nv">urlRequest</span><span class="p">:</span> <span class="kt">URLRequest</span><span class="p">,</span> <span class="k">for</span> <span class="nv">session</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html">Session</a></span><span class="p">,</span> <span class="nv">completion</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">Result</span><span class="o">&lt;</span><span class="kt">URLRequest</span><span class="p">,</span> <span class="kd">any</span> <span class="kt">Error</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF"></a>
                    <a name="//apple_ref/swift/Method/retry(_:for:dueTo:completion:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire14RequestRetrierP5retry_3for5dueTo10completionyAA0B0C_AA7SessionCs5Error_pyAA11RetryResultOYbctF">retry(_:<wbr>for:<wbr>dueTo:<wbr>completion:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">retry</span><span class="p">(</span><span class="n">_</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span>
                  <span class="k">for</span> <span class="nv">session</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html">Session</a></span><span class="p">,</span>
                  <span class="n">dueTo</span> <span class="nv">error</span><span class="p">:</span> <span class="kd">any</span> <span class="kt">Error</span><span class="p">,</span>
                  <span class="nv">completion</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Enums/RetryResult.html">RetryResult</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60Self%60%20%3D%3D%20%60DeflateRequestCompressor%60"></a>
                <a name="//apple_ref/swift/Section/Available where `Self` == `DeflateRequestCompressor`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60Self%60%20%3D%3D%20%60DeflateRequestCompressor%60"></a>
                  <h3 class="section-name"><span>Available where <code>Self</code> == <code><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE0AEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/deflateCompressor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE0AEvpZ">deflateCompressor</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Create a <code><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></code> with default <code>duplicateHeaderBehavior</code> and <code>shouldCompressBodyData</code> values.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="k">var</span> <span class="nv">deflateCompressor</span><span class="p">:</span> <span class="kt"><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE023duplicateHeaderBehavior22shouldCompressBodyDataA2E09DuplicatehI0O_Sb10Foundation0M0VYbctFZ"></a>
                    <a name="//apple_ref/swift/Method/deflateCompressor(duplicateHeaderBehavior:shouldCompressBodyData:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A07DeflateB10CompressorVRszrlE07deflateE023duplicateHeaderBehavior22shouldCompressBodyDataA2E09DuplicatehI0O_Sb10Foundation0M0VYbctFZ">deflateCompressor(duplicateHeaderBehavior:<wbr>shouldCompressBodyData:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></code> with the provided <code>DuplicateHeaderBehavior</code> and <code>shouldCompressBodyData</code>
closure.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">deflateCompressor</span><span class="p">(</span>
    <span class="nv">duplicateHeaderBehavior</span><span class="p">:</span> <span class="kt"><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></span><span class="o">.</span><span class="kt">DuplicateHeaderBehavior</span> <span class="o">=</span> <span class="o">.</span><span class="n">error</span><span class="p">,</span>
    <span class="nv">shouldCompressBodyData</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">bodyData</span><span class="p">:</span> <span class="kt">Data</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="p">{</span> <span class="n">_</span> <span class="k">in</span> <span class="kc">true</span> <span class="p">}</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>duplicateHeaderBehavior</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DuplicateHeaderBehavior</code> to use.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldCompressBodyData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure which determines whether the outgoing body data should be compressed. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60Self%60%20%3D%3D%20%60Interceptor%60"></a>
                <a name="//apple_ref/swift/Section/Available where `Self` == `Interceptor`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60Self%60%20%3D%3D%20%60Interceptor%60"></a>
                  <h3 class="section-name"><span>Available where <code>Self</code> == <code><a href="../Classes/Interceptor.html">Interceptor</a></code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAEy10Foundation10URLRequestV_AA7SessionCys6ResultOyAKs5Error_pGctc_yAA0B0C_AMsAP_pyAA05RetryJ0OctctFZ"></a>
                    <a name="//apple_ref/swift/Method/interceptor(adapter:retrier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAEy10Foundation10URLRequestV_AA7SessionCys6ResultOyAKs5Error_pGctc_yAA0B0C_AMsAP_pyAA05RetryJ0OctctFZ">interceptor(adapter:<wbr>retrier:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/Interceptor.html">Interceptor</a></code> using the provided <code><a href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a></code> and <code><a href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a></code> closures.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">interceptor</span><span class="p">(</span><span class="nv">adapter</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a></span><span class="p">,</span> <span class="nv">retrier</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/Interceptor.html">Interceptor</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>adapter</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>AdapterHandler</code>to use to adapt the request.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retrier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a></code> to use to retry the request.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/Interceptor.html">Interceptor</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAeA0B7Adapter_p_AA0B7Retrier_ptFZ"></a>
                    <a name="//apple_ref/swift/Method/interceptor(adapter:retrier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor7adapter7retrierAeA0B7Adapter_p_AA0B7Retrier_ptFZ">interceptor(adapter:<wbr>retrier:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/Interceptor.html">Interceptor</a></code> using the provided <code><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></code> and <code><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></code> instances.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">interceptor</span><span class="p">(</span><span class="nv">adapter</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></span><span class="p">,</span> <span class="nv">retrier</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/Interceptor.html">Interceptor</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>adapter</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></code> to use to adapt the request</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retrier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></code> to use to retry the request.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/Interceptor.html">Interceptor</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor8adapters8retriers12interceptorsAESayAA0B7Adapter_pG_SayAA0B7Retrier_pGSayAaB_pGtFZ"></a>
                    <a name="//apple_ref/swift/Method/interceptor(adapters:retriers:interceptors:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A0C0CRszrlE11interceptor8adapters8retriers12interceptorsAESayAA0B7Adapter_pG_SayAA0B7Retrier_pGSayAaB_pGtFZ">interceptor(adapters:<wbr>retriers:<wbr>interceptors:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/Interceptor.html">Interceptor</a></code> using the provided <code><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></code>s, <code><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></code>s, and <code>RequestInterceptor</code>s.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">interceptor</span><span class="p">(</span><span class="nv">adapters</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></span><span class="p">]</span> <span class="o">=</span> <span class="p">[],</span>
                               <span class="nv">retriers</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></span><span class="p">]</span> <span class="o">=</span> <span class="p">[],</span>
                               <span class="nv">interceptors</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt">RequestInterceptor</span><span class="p">]</span> <span class="o">=</span> <span class="p">[])</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/Interceptor.html">Interceptor</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>adapters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestAdapter.html">RequestAdapter</a></code>s to use to adapt the request. These adapters will be run until one fails.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retriers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestRetrier.html">RequestRetrier</a></code>s to use to retry the request. These retriers will be run one at a time until
          a retry is triggered.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptors</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>RequestInterceptor</code>s to use to intercept the request.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/Interceptor.html">Interceptor</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60Self%60%20%3D%3D%20%60RetryPolicy%60"></a>
                <a name="//apple_ref/swift/Section/Available where `Self` == `RetryPolicy`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60Self%60%20%3D%3D%20%60RetryPolicy%60"></a>
                  <h3 class="section-name"><span>Available where <code>Self</code> == <code><a href="../Classes/RetryPolicy.html">RetryPolicy</a></code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE0AEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/retryPolicy" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE0AEvpZ">retryPolicy</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Provides a default <code><a href="../Classes/RetryPolicy.html">RetryPolicy</a></code> instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="k">var</span> <span class="nv">retryPolicy</span><span class="p">:</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE00F5Limit22exponentialBackoffBase0hI5Scale20retryableHTTPMethods0L15HTTPStatusCodes0l8URLErrorO0AESu_SuSdShyAA10HTTPMethodVGShySiGShy10Foundation0P0V4CodeVGtFZ"></a>
                    <a name="//apple_ref/swift/Method/retryPolicy(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:retryableHTTPStatusCodes:retryableURLErrorCodes:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A11RetryPolicyCRszrlE05retryE00F5Limit22exponentialBackoffBase0hI5Scale20retryableHTTPMethods0L15HTTPStatusCodes0l8URLErrorO0AESu_SuSdShyAA10HTTPMethodVGShySiGShy10Foundation0P0V4CodeVGtFZ">retryPolicy(retryLimit:<wbr>exponentialBackoffBase:<wbr>exponentialBackoffScale:<wbr>retryableHTTPMethods:<wbr>retryableHTTPStatusCodes:<wbr>retryableURLErrorCodes:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/RetryPolicy.html">RetryPolicy</a></code> from the specified parameters.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">retryPolicy</span><span class="p">(</span><span class="nv">retryLimit</span><span class="p">:</span> <span class="kt">UInt</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryLimit</span><span class="p">,</span>
                               <span class="nv">exponentialBackoffBase</span><span class="p">:</span> <span class="kt">UInt</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultExponentialBackoffBase</span><span class="p">,</span>
                               <span class="nv">exponentialBackoffScale</span><span class="p">:</span> <span class="kt">Double</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultExponentialBackoffScale</span><span class="p">,</span>
                               <span class="nv">retryableHTTPMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryableHTTPMethods</span><span class="p">,</span>
                               <span class="nv">retryableHTTPStatusCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryableHTTPStatusCodes</span><span class="p">,</span>
                               <span class="nv">retryableURLErrorCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">URLError</span><span class="o">.</span><span class="kt">Code</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryableURLErrorCodes</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>retryLimit</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The total number of times the request is allowed to be retried. <code>2</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>exponentialBackoffBase</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The base of the exponential backoff policy. <code>2</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>exponentialBackoffScale</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The scale of the exponential backoff. <code>0.5</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retryableHTTPMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The HTTP methods that are allowed to be retried.
                      <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC27defaultRetryableHTTPMethodsShyAA10HTTPMethodVGvpZ">RetryPolicy.defaultRetryableHTTPMethods</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retryableHTTPStatusCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The HTTP status codes that are automatically retried by the policy.
                      <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC31defaultRetryableHTTPStatusCodesShySiGvpZ">RetryPolicy.defaultRetryableHTTPStatusCodes</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retryableURLErrorCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The URL error codes that are automatically retried by the policy.
                      <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC29defaultRetryableURLErrorCodesShy10Foundation0F0V4CodeVGvpZ">RetryPolicy.defaultRetryableURLErrorCodes</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/RetryPolicy.html">RetryPolicy</a></code></p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Available%20where%20%60Self%60%20%3D%3D%20%60ConnectionLostRetryPolicy%60"></a>
                <a name="//apple_ref/swift/Section/Available where `Self` == `ConnectionLostRetryPolicy`" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Available%20where%20%60Self%60%20%3D%3D%20%60ConnectionLostRetryPolicy%60"></a>
                  <h3 class="section-name"><span>Available where <code>Self</code> == <code><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></code></span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG0AEvpZ"></a>
                    <a name="//apple_ref/swift/Variable/connectionLostRetryPolicy" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG0AEvpZ">connectionLostRetryPolicy</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Provides a default <code><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></code> instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="k">var</span> <span class="nv">connectionLostRetryPolicy</span><span class="p">:</span> <span class="kt"><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG010retryLimit22exponentialBackoffBase0kL5Scale20retryableHTTPMethodsAESu_SuSdShyAA10HTTPMethodVGtFZ"></a>
                    <a name="//apple_ref/swift/Method/connectionLostRetryPolicy(retryLimit:exponentialBackoffBase:exponentialBackoffScale:retryableHTTPMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire18RequestInterceptorPA2A25ConnectionLostRetryPolicyCRszrlE010connectionefG010retryLimit22exponentialBackoffBase0kL5Scale20retryableHTTPMethodsAESu_SuSdShyAA10HTTPMethodVGtFZ">connectionLostRetryPolicy(retryLimit:<wbr>exponentialBackoffBase:<wbr>exponentialBackoffScale:<wbr>retryableHTTPMethods:<wbr>)</a>
                    </code>
                      <span class="declaration-note">
                        Extension method
                      </span>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></code> instance from the specified parameters.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">func</span> <span class="nf">connectionLostRetryPolicy</span><span class="p">(</span><span class="nv">retryLimit</span><span class="p">:</span> <span class="kt">UInt</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryLimit</span><span class="p">,</span>
                                             <span class="nv">exponentialBackoffBase</span><span class="p">:</span> <span class="kt">UInt</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultExponentialBackoffBase</span><span class="p">,</span>
                                             <span class="nv">exponentialBackoffScale</span><span class="p">:</span> <span class="kt">Double</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultExponentialBackoffScale</span><span class="p">,</span>
                                             <span class="nv">retryableHTTPMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/RetryPolicy.html">RetryPolicy</a></span><span class="o">.</span><span class="n">defaultRetryableHTTPMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>retryLimit</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The total number of times the request is allowed to be retried.
                     <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC07defaultB5LimitSuvpZ">RetryPolicy.defaultRetryLimit</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>exponentialBackoffBase</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The base of the exponential backoff policy.
                     <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC29defaultExponentialBackoffBaseSuvpZ">RetryPolicy.defaultExponentialBackoffBase</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>exponentialBackoffScale</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The scale of the exponential backoff.
                     <code><a href="../Classes/RetryPolicy.html#/s:9Alamofire11RetryPolicyC30defaultExponentialBackoffScaleSdvpZ">RetryPolicy.defaultExponentialBackoffScale</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>retryableHTTPMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The idempotent http methods to retry.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
