/*! Jazzy - https://github.com/realm/jazzy
 *  Copyright Realm Inc.
 *  SPDX-License-Identifier: MIT
 */
/* Credit to https://gist.github.com/wataru420/2048287 */
.highlight .c {
  color: #999988;
  font-style: italic; }

.highlight .err {
  color: #a61717;
  background-color: #e3d2d2; }

.highlight .k {
  color: #000000;
  font-weight: bold; }

.highlight .o {
  color: #000000;
  font-weight: bold; }

.highlight .cm {
  color: #999988;
  font-style: italic; }

.highlight .cp {
  color: #999999;
  font-weight: bold; }

.highlight .c1 {
  color: #999988;
  font-style: italic; }

.highlight .cs {
  color: #999999;
  font-weight: bold;
  font-style: italic; }

.highlight .gd {
  color: #000000;
  background-color: #ffdddd; }

.highlight .gd .x {
  color: #000000;
  background-color: #ffaaaa; }

.highlight .ge {
  color: #000000;
  font-style: italic; }

.highlight .gr {
  color: #aa0000; }

.highlight .gh {
  color: #999999; }

.highlight .gi {
  color: #000000;
  background-color: #ddffdd; }

.highlight .gi .x {
  color: #000000;
  background-color: #aaffaa; }

.highlight .go {
  color: #888888; }

.highlight .gp {
  color: #555555; }

.highlight .gs {
  font-weight: bold; }

.highlight .gu {
  color: #aaaaaa; }

.highlight .gt {
  color: #aa0000; }

.highlight .kc {
  color: #000000;
  font-weight: bold; }

.highlight .kd {
  color: #000000;
  font-weight: bold; }

.highlight .kp {
  color: #000000;
  font-weight: bold; }

.highlight .kr {
  color: #000000;
  font-weight: bold; }

.highlight .kt {
  color: #445588; }

.highlight .m {
  color: #009999; }

.highlight .s {
  color: #d14; }

.highlight .na {
  color: #008080; }

.highlight .nb {
  color: #0086B3; }

.highlight .nc {
  color: #445588;
  font-weight: bold; }

.highlight .no {
  color: #008080; }

.highlight .ni {
  color: #800080; }

.highlight .ne {
  color: #990000;
  font-weight: bold; }

.highlight .nf {
  color: #990000; }

.highlight .nn {
  color: #555555; }

.highlight .nt {
  color: #000080; }

.highlight .nv {
  color: #008080; }

.highlight .ow {
  color: #000000;
  font-weight: bold; }

.highlight .w {
  color: #bbbbbb; }

.highlight .mf {
  color: #009999; }

.highlight .mh {
  color: #009999; }

.highlight .mi {
  color: #009999; }

.highlight .mo {
  color: #009999; }

.highlight .sb {
  color: #d14; }

.highlight .sc {
  color: #d14; }

.highlight .sd {
  color: #d14; }

.highlight .s2 {
  color: #d14; }

.highlight .se {
  color: #d14; }

.highlight .sh {
  color: #d14; }

.highlight .si {
  color: #d14; }

.highlight .sx {
  color: #d14; }

.highlight .sr {
  color: #009926; }

.highlight .s1 {
  color: #d14; }

.highlight .ss {
  color: #990073; }

.highlight .bp {
  color: #999999; }

.highlight .vc {
  color: #008080; }

.highlight .vg {
  color: #008080; }

.highlight .vi {
  color: #008080; }

.highlight .il {
  color: #009999; }
