<!DOCTYPE html>
<html lang="en">
  <head>
    <title>URLEncodedFormEncoder Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/URLEncodedFormEncoder" class="dashAnchor"></a>

    <a title="URLEncodedFormEncoder Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Classes.html">Classes</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      URLEncodedFormEncoder Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>URLEncodedFormEncoder</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">URLEncodedFormEncoder</span></code></pre>

                </div>
              </div>
            <p>An object that encodes instances into URL-encoded query strings.</p>

<p><code>ArrayEncoding</code> can be used to configure how <code>Array</code> values are encoded. By default, the <code>.brackets</code> encoding is
used, encoding array values with brackets for each value. e.g <code>array[]=1&amp;array[]=2</code>.</p>

<p><code>BoolEncoding</code> can be used to configure how <code>Bool</code> values are encoded. By default, the <code>.numeric</code> encoding is used,
encoding <code>true</code> as <code>1</code> and <code>false</code> as <code>0</code>.</p>

<p><code>DataEncoding</code> can be used to configure how <code>Data</code> values are encoded. By default, the <code>.deferredToData</code> encoding is
used, which encodes <code>Data</code> values using their default <code>Encodable</code> implementation.</p>

<p><code>DateEncoding</code> can be used to configure how <code>Date</code> values are encoded. By default, the <code>.deferredToDate</code>
encoding is used, which encodes <code>Date</code>s using their default <code>Encodable</code> implementation.</p>

<p><code>KeyEncoding</code> can be used to configure how keys are encoded. By default, the <code>.useDefaultKeys</code> encoding is used,
which encodes the keys directly from the <code>Encodable</code> implementation.</p>

<p><code>KeyPathEncoding</code> can be used to configure how paths within nested objects are encoded. By default, the <code>.brackets</code>
encoding is used, which encodes each sub-key in brackets. e.g. <code>parent[child][grandchild]=value</code>.</p>

<p><code>NilEncoding</code> can be used to configure how <code>nil</code> <code>Optional</code> values are encoded. By default, the <code>.dropKey</code> encoding
is used, which drops <code>nil</code> key / value pairs from the output entirely.</p>

<p><code>SpaceEncoding</code> can be used to configure how spaces are encoded. By default, the <code>.percentEscaped</code> encoding is used,
replacing spaces with <code>%20</code>.</p>

<p>This type is largely based on Vapor&rsquo;s <a href="https://github.com/vapor/url-encoded-form"><code>url-encoded-form</code></a> project.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/ArrayEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC13ArrayEncodingO">ArrayEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for <code>Array</code> values.</p>

                        <a href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">ArrayEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12BoolEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/BoolEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12BoolEncodingO">BoolEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for <code>Bool</code> values.</p>

                        <a href="../Classes/URLEncodedFormEncoder/BoolEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">BoolEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12DataEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/DataEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12DataEncodingO">DataEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for <code>Data</code> values.</p>

                        <a href="../Classes/URLEncodedFormEncoder/DataEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">DataEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/DateEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12DateEncodingO">DateEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for <code>Date</code> values.</p>

                        <a href="../Classes/URLEncodedFormEncoder/DateEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">DateEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/KeyEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC11KeyEncodingO">KeyEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for keys.</p>

<p>This type is derived from <a href="https://github.com/apple/swift/blob/6aa313b8dd5f05135f7f878eccc1db6f9fbe34ff/stdlib/public/Darwin/Foundation/JSONEncoder.swift#L128"><code>JSONEncoder</code>&lsquo;s <code>KeyEncodingStrategy</code></a>
and <a href="https://github.com/MaxDesiatov/XMLCoder/blob/master/Sources/XMLCoder/Encoder/XMLEncoder.swift#L102"><code>XMLEncoder</code>s <code>KeyEncodingStrategy</code></a>.</p>

                        <a href="../Classes/URLEncodedFormEncoder/KeyEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">KeyEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC15KeyPathEncodingV"></a>
                    <a name="//apple_ref/swift/Struct/KeyPathEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC15KeyPathEncodingV">KeyPathEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for nested object and <code>Encodable</code> value key paths.</p>
<pre class="highlight swift"><code><span class="p">[</span><span class="s">"parent"</span> <span class="p">:</span> <span class="p">[</span><span class="s">"child"</span> <span class="p">:</span> <span class="p">[</span><span class="s">"grandchild"</span><span class="p">:</span> <span class="s">"value"</span><span class="p">]]]</span>
</code></pre>

<p>This encoding affects how the <code>parent</code>, <code>child</code>, <code>grandchild</code> path is encoded. Brackets are used by default.
e.g. <code>parent[child][grandchild]=value</code>.</p>

                        <a href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">KeyPathEncoding</span> <span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV"></a>
                    <a name="//apple_ref/swift/Struct/NilEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC11NilEncodingV">NilEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for <code>nil</code> values.</p>

                        <a href="../Classes/URLEncodedFormEncoder/NilEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">NilEncoding</span> <span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC13SpaceEncodingO"></a>
                    <a name="//apple_ref/swift/Enum/SpaceEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC13SpaceEncodingO">SpaceEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encoding to use for spaces.</p>

                        <a href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">SpaceEncoding</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC5ErrorO"></a>
                    <a name="//apple_ref/swift/Enum/Error" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC5ErrorO">Error</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a></code> error.</p>

                        <a href="../Classes/URLEncodedFormEncoder/Error.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">Error</span> <span class="p">:</span> <span class="kt">Swift</span><span class="o">.</span><span class="kt">Error</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairsSbvp"></a>
                    <a name="//apple_ref/swift/Property/alphabetizeKeyValuePairs" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairsSbvp">alphabetizeKeyValuePairs</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Whether or not to sort the encoded key value pairs.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    This setting ensures a consistent ordering for all encodings of the same parameters. When set to <code>false</code>,
    encoded <code>Dictionary</code> values may have a different encoded order each time they&rsquo;re encoded due to
  <code>Dictionary</code>&lsquo;s random storage order, but <code>Encodable</code> types will maintain their encoded order.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">alphabetizeKeyValuePairs</span><span class="p">:</span> <span class="kt">Bool</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC13arrayEncodingAC05ArrayF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/arrayEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC13arrayEncodingAC05ArrayF0Ovp">arrayEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">ArrayEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">arrayEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">ArrayEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12boolEncodingAC04BoolF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/boolEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12boolEncodingAC04BoolF0Ovp">boolEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">BoolEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">boolEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">BoolEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12dataEncodingAC04DataF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/dataEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12dataEncodingAC04DataF0Ovp">dataEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>THe <code><a href="../Classes/URLEncodedFormEncoder/DataEncoding.html">DataEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">dataEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/DataEncoding.html">DataEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC12dateEncodingAC04DateF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/dateEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC12dateEncodingAC04DateF0Ovp">dateEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/DateEncoding.html">DateEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">dateEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/DateEncoding.html">DateEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC11keyEncodingAC03KeyF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/keyEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC11keyEncodingAC03KeyF0Ovp">keyEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">KeyEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">keyEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">KeyEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC15keyPathEncodingAC03KeyfG0Vvp"></a>
                    <a name="//apple_ref/swift/Property/keyPathEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC15keyPathEncodingAC03KeyfG0Vvp">keyPathEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">KeyPathEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">keyPathEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">KeyPathEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC11nilEncodingAC03NilF0Vvp"></a>
                    <a name="//apple_ref/swift/Property/nilEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC11nilEncodingAC03NilF0Vvp">nilEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/NilEncoding.html">NilEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">nilEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/NilEncoding.html">NilEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC13spaceEncodingAC05SpaceF0Ovp"></a>
                    <a name="//apple_ref/swift/Property/spaceEncoding" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC13spaceEncodingAC05SpaceF0Ovp">spaceEncoding</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code><a href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">SpaceEncoding</a></code> to use.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">spaceEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">SpaceEncoding</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC17allowedCharacters10Foundation12CharacterSetVvp"></a>
                    <a name="//apple_ref/swift/Property/allowedCharacters" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC17allowedCharacters10Foundation12CharacterSetVvp">allowedCharacters</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>The <code>CharacterSet</code> of allowed (non-escaped) characters.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">allowedCharacters</span><span class="p">:</span> <span class="kt">CharacterSet</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairs13arrayEncoding04boolJ004dataJ004dateJ003keyJ00n4PathJ003nilJ005spaceJ017allowedCharactersACSb_AC05ArrayJ0OAC04BoolJ0OAC04DataJ0OAC04DateJ0OAC0fJ0OAC0foJ0VAC03NilJ0VAC05SpaceJ0O10Foundation12CharacterSetVtcfc"></a>
                    <a name="//apple_ref/swift/Method/init(alphabetizeKeyValuePairs:arrayEncoding:boolEncoding:dataEncoding:dateEncoding:keyEncoding:keyPathEncoding:nilEncoding:spaceEncoding:allowedCharacters:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC24alphabetizeKeyValuePairs13arrayEncoding04boolJ004dataJ004dateJ003keyJ00n4PathJ003nilJ005spaceJ017allowedCharactersACSb_AC05ArrayJ0OAC04BoolJ0OAC04DataJ0OAC04DateJ0OAC0fJ0OAC0foJ0VAC03NilJ0VAC05SpaceJ0O10Foundation12CharacterSetVtcfc">init(alphabetizeKeyValuePairs:<wbr>arrayEncoding:<wbr>boolEncoding:<wbr>dataEncoding:<wbr>dateEncoding:<wbr>keyEncoding:<wbr>keyPathEncoding:<wbr>nilEncoding:<wbr>spaceEncoding:<wbr>allowedCharacters:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an instance from the supplied parameters.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="nf">init</span><span class="p">(</span><span class="nv">alphabetizeKeyValuePairs</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
            <span class="nv">arrayEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">ArrayEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">brackets</span><span class="p">,</span>
            <span class="nv">boolEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">BoolEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">numeric</span><span class="p">,</span>
            <span class="nv">dataEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/DataEncoding.html">DataEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">base64</span><span class="p">,</span>
            <span class="nv">dateEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/DateEncoding.html">DateEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">deferredToDate</span><span class="p">,</span>
            <span class="nv">keyEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">KeyEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">useDefaultKeys</span><span class="p">,</span>
            <span class="nv">keyPathEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">KeyPathEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">brackets</span><span class="p">,</span>
            <span class="nv">nilEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/NilEncoding.html">NilEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">dropKey</span><span class="p">,</span>
            <span class="nv">spaceEncoding</span><span class="p">:</span> <span class="kt"><a href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">SpaceEncoding</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">percentEscaped</span><span class="p">,</span>
            <span class="nv">allowedCharacters</span><span class="p">:</span> <span class="kt">CharacterSet</span> <span class="o">=</span> <span class="o">.</span><span class="n">afURLQueryAllowed</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>alphabetizeKeyValuePairs</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Whether or not to sort the encoded key value pairs. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>arrayEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">ArrayEncoding</a></code> to use. <code>.brackets</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>boolEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">BoolEncoding</a></code> to use. <code>.numeric</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/DataEncoding.html">DataEncoding</a></code> to use. <code>.base64</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dateEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/DateEncoding.html">DateEncoding</a></code> to use. <code>.deferredToDate</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>keyEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">KeyEncoding</a></code> to use. <code>.useDefaultKeys</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>nilEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/NilEncoding.html">NilEncoding</a></code> to use. <code>.drop</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>spaceEncoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code><a href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">SpaceEncoding</a></code> to use. <code>.percentEscaped</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>allowedCharacters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>CharacterSet</code> of allowed (non-escaped) characters. <code>.afURLQueryAllowed</code> by
                      default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC6encodeySSSE_pKF"></a>
                    <a name="//apple_ref/swift/Method/encode(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC6encodeySSSE_pKF">encode(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encodes the <code>value</code> as a URL form encoded <code>String</code>.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <p>An <code><a href="../Classes/URLEncodedFormEncoder/Error.html">Error</a></code> or <code>EncodingError</code> instance if encoding fails.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">encode</span><span class="p">(</span><span class="n">_</span> <span class="nv">value</span><span class="p">:</span> <span class="kd">any</span> <span class="kt">Encodable</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>value</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Encodable</code> value.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The encoded <code>String</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire21URLEncodedFormEncoderC6encodey10Foundation4DataVSE_pKF"></a>
                    <a name="//apple_ref/swift/Method/encode(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire21URLEncodedFormEncoderC6encodey10Foundation4DataVSE_pKF">encode(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Encodes the value as <code>Data</code>. This is performed by first creating an encoded <code>String</code> and then returning the
<code>.utf8</code> data.</p>
<div class="aside aside-throws">
    <p class="aside-title">Throws</p>
    <p>An <code><a href="../Classes/URLEncodedFormEncoder/Error.html">Error</a></code> or <code>EncodingError</code> instance if encoding fails.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">encode</span><span class="p">(</span><span class="n">_</span> <span class="nv">value</span><span class="p">:</span> <span class="kd">any</span> <span class="kt">Encodable</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">Data</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>value</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Encodable</code> value.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The encoded <code>Data</code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
