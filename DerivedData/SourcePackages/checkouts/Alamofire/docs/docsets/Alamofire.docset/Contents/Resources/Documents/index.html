<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Alamofire  Reference</title>
    <link rel="stylesheet" type="text/css" href="css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="css/highlight.css" />
    <meta charset="utf-8">
    <script src="js/jquery.min.js" defer></script>
    <script src="js/jazzy.js" defer></script>
    
    <script src="js/lunr.min.js" defer></script>
    <script src="js/typeahead.jquery.js" defer></script>
    <script src="js/jazzy.search.js" defer></script>
  </head>
  <body>


    <a title="Alamofire  Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="index.html">Alamofire</a>
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            
            <p><img src="https://raw.githubusercontent.com/Alamofire/Alamofire/master/Resources/AlamofireLogo.png" alt="Alamofire: Elegant Networking in Swift"></p>

<p><a href="https://img.shields.io/badge/Swift-5.9_5.10_6.0-Orange?style=flat-square"><img src="https://img.shields.io/badge/Swift-5.9_5.10_6.0-orange?style=flat-square" alt="Swift"></a>
<a href="https://img.shields.io/badge/Platforms-macOS_iOS_tvOS_watchOS_vision_OS_Linux_Windows_Android-Green?style=flat-square"><img src="https://img.shields.io/badge/Platforms-macOS_iOS_tvOS_watchOS_visionOS_Linux_Windows_Android-yellowgreen?style=flat-square" alt="Platforms"></a>
<a href="https://img.shields.io/cocoapods/v/Alamofire.svg"><img src="https://img.shields.io/cocoapods/v/Alamofire.svg?style=flat-square" alt="CocoaPods Compatible"></a>
<a href="https://github.com/Carthage/Carthage"><img src="https://img.shields.io/badge/Carthage-compatible-4BC51D.svg?style=flat-square" alt="Carthage Compatible"></a>
<a href="https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square"><img src="https://img.shields.io/badge/Swift_Package_Manager-compatible-orange?style=flat-square" alt="Swift Package Manager"></a>
<a href="https://forums.swift.org/c/related-projects/alamofire/37"><img src="https://img.shields.io/badge/Swift_Forums-Alamofire-orange?style=flat-square" alt="Swift Forums"></a></p>

<p>Alamofire is an HTTP networking library written in Swift.</p>

<ul>
<li><a href="#features">Features</a></li>
<li><a href="#component-libraries">Component Libraries</a></li>
<li><a href="#requirements">Requirements</a></li>
<li><a href="#migration-guides">Migration Guides</a></li>
<li><a href="#communication">Communication</a></li>
<li><a href="#installation">Installation</a></li>
<li><a href="#contributing">Contributing</a></li>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#using-alamofire">Usage</a>

<ul>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#introduction"><strong>Introduction -</strong></a> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#making-requests">Making Requests</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#response-handling">Response Handling</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#response-validation">Response Validation</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#response-caching">Response Caching</a></li>
<li><strong>HTTP -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#http-methods">HTTP Methods</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md##request-parameters-and-parameter-encoders">Parameters and Parameter Encoder</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#http-headers">HTTP Headers</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#authentication">Authentication</a></li>
<li><strong>Large Data -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#downloading-data-to-a-file">Downloading Data to a File</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#uploading-data-to-a-server">Uploading Data to a Server</a></li>
<li><strong>Tools -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#statistical-metrics">Statistical Metrics</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Usage.md#curl-command-output">cURL Command Output</a></li>
</ul></li>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md">Advanced Usage</a>

<ul>
<li><strong>URL Session -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#session">Session Manager</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#sessiondelegate">Session Delegate</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#request">Request</a></li>
<li><strong>Routing -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#routing-requests">Routing Requests</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#adapting-and-retrying-requests-with-requestinterceptor">Adapting and Retrying Requests</a></li>
<li><strong>Model Objects -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#customizing-response-handlers">Custom Response Handlers</a></li>
<li><strong>Advanced Concurrency -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#using-alamofire-with-swift-concurrency">Swift Concurrency</a> and <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#using-alamofire-with-combine">Combine</a></li>
<li><strong>Connection -</strong> <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#security">Security</a>, <a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/AdvancedUsage.md#network-reachability">Network Reachability</a></li>
</ul></li>
<li><a href="#open-radars">Open Radars</a></li>
<li><a href="#faq">FAQ</a></li>
<li><a href="#credits">Credits</a></li>
<li><a href="#donations">Donations</a></li>
<li><a href="#license">License</a></li>
</ul>
<h2 id='features' class='heading'>Features</h2>

<ul>
<li>[x] Chainable Request / Response Methods</li>
<li>[x] Swift Concurrency Support Back to iOS 13, macOS 10.15, tvOS 13, and watchOS 6.</li>
<li>[x] Combine Support</li>
<li>[x] URL / JSON Parameter Encoding</li>
<li>[x] Upload File / Data / Stream / MultipartFormData</li>
<li>[x] Download File using Request or Resume Data</li>
<li>[x] Authentication with <code>URLCredential</code></li>
<li>[x] HTTP Response Validation</li>
<li>[x] Upload and Download Progress Closures with Progress</li>
<li>[x] cURL Command Output</li>
<li>[x] Dynamically Adapt and Retry Requests</li>
<li>[x] TLS Certificate and Public Key Pinning</li>
<li>[x] Network Reachability</li>
<li>[x] Comprehensive Unit and Integration Test Coverage</li>
<li>[x] <a href="https://alamofire.github.io/Alamofire">Complete Documentation</a></li>
</ul>
<h2 id='write-requests-fast' class='heading'>Write Requests Fast!</h2>

<p>Alamofire&rsquo;s compact syntax and extensive feature set allow requests with powerful features like automatic retry to be written in just a few lines of code.</p>
<pre class="highlight swift"><code><span class="c1">// Automatic String to URL conversion, Swift concurrency support, and automatic retry.</span>
<span class="k">let</span> <span class="nv">response</span> <span class="o">=</span> <span class="k">await</span> <span class="kt">AF</span><span class="o">.</span><span class="nf">request</span><span class="p">(</span><span class="s">"https://httpbin.org/get"</span><span class="p">,</span> <span class="nv">interceptor</span><span class="p">:</span> <span class="o">.</span><span class="n">retryPolicy</span><span class="p">)</span>
                       <span class="c1">// Automatic HTTP Basic Auth.</span>
                       <span class="o">.</span><span class="nf">authenticate</span><span class="p">(</span><span class="nv">username</span><span class="p">:</span> <span class="s">"user"</span><span class="p">,</span> <span class="nv">password</span><span class="p">:</span> <span class="s">"pass"</span><span class="p">)</span>
                       <span class="c1">// Caching customization.</span>
                       <span class="o">.</span><span class="nf">cacheResponse</span><span class="p">(</span><span class="nv">using</span><span class="p">:</span> <span class="o">.</span><span class="n">cache</span><span class="p">)</span>
                       <span class="c1">// Redirect customization.</span>
                       <span class="o">.</span><span class="nf">redirect</span><span class="p">(</span><span class="nv">using</span><span class="p">:</span> <span class="o">.</span><span class="n">follow</span><span class="p">)</span>
                       <span class="c1">// Validate response code and Content-Type.</span>
                       <span class="o">.</span><span class="nf">validate</span><span class="p">()</span>
                       <span class="c1">// Produce a cURL command for the request.</span>
                       <span class="o">.</span><span class="n">cURLDescription</span> <span class="p">{</span> <span class="n">description</span> <span class="k">in</span>
                         <span class="nf">print</span><span class="p">(</span><span class="n">description</span><span class="p">)</span>
                       <span class="p">}</span>
                       <span class="c1">// Automatic Decodable support with background parsing.</span>
                       <span class="o">.</span><span class="nf">serializingDecodable</span><span class="p">(</span><span class="kt">DecodableType</span><span class="o">.</span><span class="k">self</span><span class="p">)</span>
                       <span class="c1">// Await the full response with metrics and a parsed body.</span>
                       <span class="o">.</span><span class="n">response</span>
<span class="c1">// Detailed response description for easy debugging.</span>
<span class="nf">debugPrint</span><span class="p">(</span><span class="n">response</span><span class="p">)</span>
</code></pre>
<h2 id='component-libraries' class='heading'>Component Libraries</h2>

<p>In order to keep Alamofire focused specifically on core networking implementations, additional component libraries have been created by the <a href="https://github.com/Alamofire/Foundation">Alamofire Software Foundation</a> to bring additional functionality to the Alamofire ecosystem.</p>

<ul>
<li><a href="https://github.com/Alamofire/AlamofireImage">AlamofireImage</a> - An image library including image response serializers, <code>UIImage</code> and <code>UIImageView</code> extensions, custom image filters, an auto-purging in-memory cache, and a priority-based image downloading system.</li>
<li><a href="https://github.com/Alamofire/AlamofireNetworkActivityIndicator">AlamofireNetworkActivityIndicator</a> - Controls the visibility of the network activity indicator on iOS using Alamofire. It contains configurable delay timers to help mitigate flicker and can support <code>URLSession</code> instances not managed by Alamofire.</li>
</ul>
<h2 id='requirements' class='heading'>Requirements</h2>

<table><thead>
<tr>
<th>Platform</th>
<th>Minimum Swift Version</th>
<th>Installation</th>
<th>Status</th>
</tr>
</thead><tbody>
<tr>
<td>iOS 10.0+ / macOS 10.12+ / tvOS 10.0+ / watchOS 3.0+</td>
<td>5.9 / Xcode 15.0</td>
<td><a href="#cocoapods">CocoaPods</a>, <a href="#carthage">Carthage</a>, <a href="#swift-package-manager">Swift Package Manager</a>, <a href="#manually">Manual</a></td>
<td>Fully Tested</td>
</tr>
<tr>
<td>Linux</td>
<td>Latest Only</td>
<td><a href="#swift-package-manager">Swift Package Manager</a></td>
<td>Building But Unsupported</td>
</tr>
<tr>
<td>Windows</td>
<td>Latest Only</td>
<td><a href="#swift-package-manager">Swift Package Manager</a></td>
<td>Building But Unsupported</td>
</tr>
<tr>
<td>Android</td>
<td>Latest Only</td>
<td><a href="#swift-package-manager">Swift Package Manager</a></td>
<td>Building But Unsupported</td>
</tr>
</tbody></table>
<h4 id='known-issues-on-linux-and-windows' class='heading'>Known Issues on Linux and Windows</h4>

<p>Alamofire builds on Linux, Windows, and Android but there are missing features and many issues in the underlying <code>swift-corelibs-foundation</code> that prevent full functionality and may cause crashes. These include:</p>

<ul>
<li><code><a href="Classes/ServerTrustManager.html">ServerTrustManager</a></code> and associated certificate functionality is unavailable, so there is no certificate pinning and no client certificate support.</li>
<li>Various methods of HTTP authentication may crash, including HTTP Basic and HTTP Digest. Crashes may occur if responses contain server challenges.</li>
<li>Cache control through <code><a href="Protocols/CachedResponseHandler.html">CachedResponseHandler</a></code> and associated APIs is unavailable, as the underlying delegate methods aren&rsquo;t called.</li>
<li><code>URLSessionTaskMetrics</code> are never gathered.</li>
<li><code>WebSocketRequest</code> is not available.</li>
</ul>

<p>Due to these issues, Alamofire is unsupported on Linux, Windows, and Android. Please report any crashes to the <a href="https://bugs.swift.org">Swift bug reporter</a>.</p>
<h2 id='migration-guides' class='heading'>Migration Guides</h2>

<ul>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Alamofire%205.0%20Migration%20Guide.md">Alamofire 5.0 Migration Guide</a></li>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Alamofire%204.0%20Migration%20Guide.md">Alamofire 4.0 Migration Guide</a></li>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Alamofire%203.0%20Migration%20Guide.md">Alamofire 3.0 Migration Guide</a></li>
<li><a href="https://github.com/Alamofire/Alamofire/blob/master/Documentation/Alamofire%202.0%20Migration%20Guide.md">Alamofire 2.0 Migration Guide</a></li>
</ul>
<h2 id='communication' class='heading'>Communication</h2>

<ul>
<li>If you <strong>need help with making network requests</strong> using Alamofire, use <a href="https://stackoverflow.com/questions/tagged/alamofire">Stack Overflow</a> and tag <code>alamofire</code>.</li>
<li>If you need to <strong>find or understand an API</strong>, check <a href="http://alamofire.github.io/Alamofire/">our documentation</a> or <a href="https://developer.apple.com/documentation/foundation/url_loading_system">Apple&rsquo;s documentation for <code>URLSession</code></a>, on top of which Alamofire is built.</li>
<li>If you need <strong>help with an Alamofire feature</strong>, use <a href="https://forums.swift.org/c/related-projects/alamofire">our forum on swift.org</a>.</li>
<li>If you&rsquo;d like to <strong>discuss Alamofire best practices</strong>, use <a href="https://forums.swift.org/c/related-projects/alamofire">our forum on swift.org</a>.</li>
<li>If you&rsquo;d like to <strong>discuss a feature request</strong>, use <a href="https://forums.swift.org/c/related-projects/alamofire">our forum on swift.org</a>.</li>
<li>If you <strong>found a bug</strong>, open an issue here on GitHub and follow the guide. The more detail the better!</li>
</ul>
<h2 id='installation' class='heading'>Installation</h2>
<h3 id='swift-package-manager' class='heading'>Swift Package Manager</h3>

<p>The <a href="https://swift.org/package-manager/">Swift Package Manager</a> is a tool for automating the distribution of Swift code and is integrated into the <code>swift</code> compiler.</p>

<p>Once you have your Swift package set up, adding Alamofire as a dependency is as easy as adding it to the <code>dependencies</code> value of your <code>Package.swift</code> or the Package list in Xcode.</p>
<pre class="highlight swift"><code><span class="nv">dependencies</span><span class="p">:</span> <span class="p">[</span>
    <span class="o">.</span><span class="nf">package</span><span class="p">(</span><span class="nv">url</span><span class="p">:</span> <span class="s">"https://github.com/Alamofire/Alamofire.git"</span><span class="p">,</span> <span class="o">.</span><span class="nf">upToNextMajor</span><span class="p">(</span><span class="nv">from</span><span class="p">:</span> <span class="s">"5.10.0"</span><span class="p">))</span>
<span class="p">]</span>
</code></pre>

<p>Normally you&rsquo;ll want to depend on the <code>Alamofire</code> target:</p>
<pre class="highlight swift"><code><span class="o">.</span><span class="nf">product</span><span class="p">(</span><span class="nv">name</span><span class="p">:</span> <span class="s">"Alamofire"</span><span class="p">,</span> <span class="nv">package</span><span class="p">:</span> <span class="s">"Alamofire"</span><span class="p">)</span>
</code></pre>

<p>But if you want to force Alamofire to be dynamically linked (do not do this unless you&rsquo;re sure you need it), you can depend on the <code>AlamofireDynamic</code> target:</p>
<pre class="highlight swift"><code><span class="o">.</span><span class="nf">product</span><span class="p">(</span><span class="nv">name</span><span class="p">:</span> <span class="s">"AlamofireDynamic"</span><span class="p">,</span> <span class="nv">package</span><span class="p">:</span> <span class="s">"Alamofire"</span><span class="p">)</span>
</code></pre>
<h3 id='cocoapods' class='heading'>CocoaPods</h3>

<p><a href="https://cocoapods.org">CocoaPods</a> is a dependency manager for Cocoa projects. For usage and installation instructions, visit their website. To integrate Alamofire into your Xcode project using CocoaPods, specify it in your <code>Podfile</code>:</p>
<pre class="highlight ruby"><code><span class="n">pod</span> <span class="s1">'Alamofire'</span>
</code></pre>
<h3 id='carthage' class='heading'>Carthage</h3>

<p><a href="https://github.com/Carthage/Carthage">Carthage</a> is a decentralized dependency manager that builds your dependencies and provides you with binary frameworks. To integrate Alamofire into your Xcode project using Carthage, specify it in your <code>Cartfile</code>:</p>
<pre class="highlight plaintext"><code>github "Alamofire/Alamofire"
</code></pre>
<h3 id='manually' class='heading'>Manually</h3>

<p>If you prefer not to use any of the aforementioned dependency managers, you can integrate Alamofire into your project manually.</p>
<h4 id='embedded-framework' class='heading'>Embedded Framework</h4>

<ul>
<li>Open up Terminal, <code>cd</code> into your top-level project directory, and run the following command &ldquo;if&rdquo; your project is not initialized as a git repository:</li>
</ul>
<pre class="highlight shell"><code>  <span class="nv">$ </span>git init
</code></pre>

<ul>
<li>Add Alamofire as a git <a href="https://git-scm.com/docs/git-submodule">submodule</a> by running the following command:</li>
</ul>
<pre class="highlight shell"><code>  <span class="nv">$ </span>git submodule add https://github.com/Alamofire/Alamofire.git
</code></pre>

<ul>
<li>Open the new <code>Alamofire</code> folder, and drag the <code>Alamofire.xcodeproj</code> into the Project Navigator of your application&rsquo;s Xcode project.</li>
</ul>

<blockquote>
<p>It should appear nested underneath your application&rsquo;s blue project icon. Whether it is above or below all the other Xcode groups does not matter.</p>
</blockquote>

<ul>
<li>Select the <code>Alamofire.xcodeproj</code> in the Project Navigator and verify the deployment target matches that of your application target.</li>
<li>Next, select your application project in the Project Navigator (blue project icon) to navigate to the target configuration window and select the application target under the &ldquo;Targets&rdquo; heading in the sidebar.</li>
<li>In the tab bar at the top of that window, open the &ldquo;General&rdquo; panel.</li>
<li>Click on the <code>+</code> button under the &ldquo;Embedded Binaries&rdquo; section.</li>
<li>You will see two different <code>Alamofire.xcodeproj</code> folders each with two different versions of the <code>Alamofire.framework</code> nested inside a <code>Products</code> folder.</li>
</ul>

<blockquote>
<p>It does not matter which <code>Products</code> folder you choose from, but it does matter whether you choose the top or bottom <code>Alamofire.framework</code>.</p>
</blockquote>

<ul>
<li>Select the top <code>Alamofire.framework</code> for iOS and the bottom one for macOS.</li>
</ul>

<blockquote>
<p>You can verify which one you selected by inspecting the build log for your project. The build target for <code>Alamofire</code> will be listed as <code>Alamofire iOS</code>, <code>Alamofire macOS</code>, <code>Alamofire tvOS</code>, or <code>Alamofire watchOS</code>.</p>
</blockquote>

<ul>
<li>And that&rsquo;s it!</li>
</ul>

<blockquote>
<p>The <code>Alamofire.framework</code> is automagically added as a target dependency, linked framework and embedded framework in a copy files build phase which is all you need to build on the simulator and a device.</p>
</blockquote>
<h2 id='contributing' class='heading'>Contributing</h2>

<p>Before contributing to Alamofire, please read the instructions detailed in our <a href="https://github.com/Alamofire/Alamofire/blob/master/CONTRIBUTING.md">contribution guide</a>.</p>
<h2 id='open-radars' class='heading'>Open Radars</h2>

<p>The following radars have some effect on the current implementation of Alamofire.</p>

<ul>
<li><a href="http://www.openradar.me/radar?id=5517037090635776"><code>rdar://21349340</code></a> - Compiler throwing warning due to toll-free bridging issue in the test case</li>
<li><code>rdar://26870455</code> - Background URL Session Configurations do not work in the simulator</li>
<li><code>rdar://26849668</code> - Some URLProtocol APIs do not properly handle <code>URLRequest</code></li>
</ul>
<h2 id='resolved-radars' class='heading'>Resolved Radars</h2>

<p>The following radars have been resolved over time after being filed against the Alamofire project.</p>

<ul>
<li><a href="http://www.openradar.me/radar?id=5010235949318144"><code>rdar://26761490</code></a> - Swift string interpolation causing memory leak with common usage.

<ul>
<li>(Resolved): 9/1/17 in Xcode 9 beta 6.</li>
</ul></li>
<li><a href="http://openradar.appspot.com/radar?id=4942308441063424"><code>rdar://36082113</code></a> - <code>URLSessionTaskMetrics</code> failing to link on watchOS 3.0+

<ul>
<li>(Resolved): Just add <code>CFNetwork</code> to your linked frameworks.</li>
</ul></li>
<li><code>*********</code> - <code>urlSession(_:task:didFinishCollecting:)</code> never called on watchOS

<ul>
<li>(Resolved): Metrics now collected on watchOS 7+.</li>
</ul></li>
</ul>
<h2 id='faq' class='heading'>FAQ</h2>
<h3 id='what-39-s-the-origin-of-the-name-alamofire' class='heading'>What&rsquo;s the origin of the name Alamofire?</h3>

<p>Alamofire is named after the <a href="https://aggie-horticulture.tamu.edu/wildseed/alamofire.html">Alamo Fire flower</a>, a hybrid variant of the Bluebonnet, the official state flower of Texas.</p>
<h2 id='credits' class='heading'>Credits</h2>

<p>Alamofire is owned and maintained by the <a href="http://alamofire.org">Alamofire Software Foundation</a>. You can follow them on Twitter at <a href="https://twitter.com/AlamofireSF">@AlamofireSF</a> for project updates and releases.</p>
<h3 id='security-disclosure' class='heading'>Security Disclosure</h3>

<p>If you believe you have identified a security vulnerability with Alamofire, you should report it as soon as possible via email to <a href="mailto:<EMAIL>"><EMAIL></a>. Please do not post it to a public issue tracker.</p>
<h2 id='sponsorship' class='heading'>Sponsorship</h2>

<p>The <a href="https://github.com/Alamofire/Foundation#members">ASF</a> is looking to raise money to officially stay registered as a federal non-profit organization.
Registering will allow Foundation members to gain some legal protections and also allow us to put donations to use, tax-free.
Sponsoring the ASF will enable us to:</p>

<ul>
<li>Pay our yearly legal fees to keep the non-profit in good status</li>
<li>Pay for our mail servers to help us stay on top of all questions and security issues</li>
<li>Potentially fund test servers to make it easier for us to test the edge cases</li>
<li>Potentially fund developers to work on one of our projects full-time</li>
</ul>

<p>The community adoption of the ASF libraries has been amazing.
We are greatly humbled by your enthusiasm around the projects and want to continue to do everything we can to move the needle forward.
With your continued support, the ASF will be able to improve its reach and also provide better legal safety for the core members.
If you use any of our libraries for work, see if your employers would be interested in donating.
Any amount you can donate, whether once or monthly, to help us reach our goal would be greatly appreciated.</p>

<p><a href="https://github.com/sponsors/Alamofire">Sponsor Alamofire</a></p>
<h2 id='supporters' class='heading'>Supporters</h2>

<p><a href="https://macstadium.com">MacStadium</a> provides Alamofire with a free, hosted Mac mini.</p>

<p><img src="https://raw.githubusercontent.com/Alamofire/Alamofire/master/Resources/MacStadiumLogo.png" alt="Powered by MacStadium"></p>
<h2 id='license' class='heading'>License</h2>

<p>Alamofire is released under the MIT license. <a href="https://github.com/Alamofire/Alamofire/blob/master/LICENSE">See LICENSE</a> for details.</p>

          </div>
        </section>


      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
