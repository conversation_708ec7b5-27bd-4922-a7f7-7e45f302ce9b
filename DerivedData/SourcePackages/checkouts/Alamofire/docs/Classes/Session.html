<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Session Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/Session" class="dashAnchor"></a>

    <a title="Session Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Classes.html">Classes</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      Session Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>Session</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">class</span> <span class="kt">Session</span> <span class="p">:</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>
<pre class="highlight swift"><code><span class="kd">extension</span> <span class="kt">Session</span><span class="p">:</span> <span class="kt"><a href="../Protocols/RequestDelegate.html">RequestDelegate</a></span></code></pre>

                </div>
              </div>
            <p><code>Session</code> creates and manages Alamofire&rsquo;s <code><a href="../Classes/Request.html">Request</a></code> types during their lifetimes. It also provides common
functionality for all <code><a href="../Classes/Request.html">Request</a></code>s, including queuing, interception, trust management, redirect handling, and response
cache handling.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7defaultACvpZ"></a>
                    <a name="//apple_ref/swift/Variable/default" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7defaultACvpZ">default</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Shared singleton instance used by all <code>AF.request</code> APIs. Cannot be modified.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">static</span> <span class="kd">let</span> <span class="p">`</span><span class="nv">default</span><span class="p">`:</span> <span class="kt">Session</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7sessionSo12NSURLSessionCvp"></a>
                    <a name="//apple_ref/swift/Property/session" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7sessionSo12NSURLSessionCvp">session</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Underlying <code>URLSession</code> used to create <code>URLSessionTasks</code> for this instance, and for which this instance&rsquo;s
<code><a href="../Classes/Session.html#/s:9Alamofire7SessionC8delegateAA0B8DelegateCvp">delegate</a></code> handles <code>URLSessionDelegate</code> callbacks.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    This instance should <strong>NOT</strong> be used to interact with the underlying <code>URLSessionTask</code>s. Doing so will
    break internal Alamofire logic that tracks those tasks.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">session</span><span class="p">:</span> <span class="kt">URLSession</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC8delegateAA0B8DelegateCvp"></a>
                    <a name="//apple_ref/swift/Property/delegate" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC8delegateAA0B8DelegateCvp">delegate</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Instance&rsquo;s <code><a href="../Classes/SessionDelegate.html">SessionDelegate</a></code>, which handles the <code>URLSessionDelegate</code> methods and <code><a href="../Classes/Request.html">Request</a></code> interaction.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">delegate</span><span class="p">:</span> <span class="kt"><a href="../Classes/SessionDelegate.html">SessionDelegate</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp"></a>
                    <a name="//apple_ref/swift/Property/rootQueue" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Root <code>DispatchQueue</code> for all internal callbacks and state update. <strong>MUST</strong> be a serial queue.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">rootQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC24startRequestsImmediatelySbvp"></a>
                    <a name="//apple_ref/swift/Property/startRequestsImmediately" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC24startRequestsImmediatelySbvp">startRequestsImmediately</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Value determining whether this instance automatically calls <code>resume()</code> on all created <code><a href="../Classes/Request.html">Request</a></code>s.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">startRequestsImmediately</span><span class="p">:</span> <span class="kt">Bool</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC12requestQueueSo17OS_dispatch_queueCvp"></a>
                    <a name="//apple_ref/swift/Property/requestQueue" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC12requestQueueSo17OS_dispatch_queueCvp">requestQueue</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>DispatchQueue</code> on which <code>URLRequest</code>s are created asynchronously. By default this queue uses <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its
<code>target</code>, but a separate queue can be used if request creation is determined to be a bottleneck. Always profile
and test before introducing an additional queue.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">requestQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC18serializationQueueSo17OS_dispatch_queueCvp"></a>
                    <a name="//apple_ref/swift/Property/serializationQueue" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC18serializationQueueSo17OS_dispatch_queueCvp">serializationQueue</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code>DispatchQueue</code> passed to all <code><a href="../Classes/Request.html">Request</a></code>s on which they perform their response serialization. By default this
queue uses <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its <code>target</code> but a separate queue can be used if response serialization is determined
to be a bottleneck. Always profile and test before introducing an additional queue.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">serializationQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC11interceptorAA18RequestInterceptor_pSgvp"></a>
                    <a name="//apple_ref/swift/Property/interceptor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC11interceptorAA18RequestInterceptor_pSgvp">interceptor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> used for all <code><a href="../Classes/Request.html">Request</a></code> created by the instance. <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>s can also be set on a
per-<code><a href="../Classes/Request.html">Request</a></code> basis, in which case the <code><a href="../Classes/Request.html">Request</a></code>&lsquo;s interceptor takes precedence over this value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC18serverTrustManagerAA06ServerdE0CSgvp"></a>
                    <a name="//apple_ref/swift/Property/serverTrustManager" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC18serverTrustManagerAA06ServerdE0CSgvp">serverTrustManager</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></code> instance used to evaluate all trust challenges and provide certificate and key pinning.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">serverTrustManager</span><span class="p">:</span> <span class="kt"><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></span><span class="p">?</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC15redirectHandlerAA08RedirectD0_pSgvp"></a>
                    <a name="//apple_ref/swift/Property/redirectHandler" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC15redirectHandlerAA08RedirectD0_pSgvp">redirectHandler</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></code> instance used to provide customization for request redirection.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">redirectHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></span><span class="p">)?</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC21cachedResponseHandlerAA06CacheddE0_pSgvp"></a>
                    <a name="//apple_ref/swift/Property/cachedResponseHandler" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC21cachedResponseHandlerAA06CacheddE0_pSgvp">cachedResponseHandler</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></code> instance used to provide customization of cached response handling.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">cachedResponseHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></span><span class="p">)?</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC12eventMonitorAA014CompositeEventD0Cvp"></a>
                    <a name="//apple_ref/swift/Property/eventMonitor" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC12eventMonitorAA014CompositeEventD0Cvp">eventMonitor</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a></code> used to compose any passed <code><a href="../Protocols/EventMonitor.html">EventMonitor</a></code>s.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">eventMonitor</span><span class="p">:</span> <span class="kt"><a href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC20defaultEventMonitorsSayAA0D7Monitor_pGvp"></a>
                    <a name="//apple_ref/swift/Property/defaultEventMonitors" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC20defaultEventMonitorsSayAA0D7Monitor_pGvp">defaultEventMonitors</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Protocols/EventMonitor.html">EventMonitor</a></code>s included in all instances unless overwritten. <code>[AlamofireNotifications()]</code> by default.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="o">*</span><span class="p">,</span> <span class="n">deprecated</span><span class="p">,</span> <span class="nv">message</span><span class="p">:</span> <span class="s">"Use [AlamofireNotifications(﹚] directly."</span><span class="p">)</span>
<span class="kd">public</span> <span class="k">let</span> <span class="nv">defaultEventMonitors</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/EventMonitor.html">EventMonitor</a></span><span class="p">]</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7session8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo12NSURLSessionC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc"></a>
                    <a name="//apple_ref/swift/Method/init(session:delegate:rootQueue:startRequestsImmediately:requestQueue:serializationQueue:interceptor:serverTrustManager:redirectHandler:cachedResponseHandler:eventMonitors:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7session8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo12NSURLSessionC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc">init(session:<wbr>delegate:<wbr>rootQueue:<wbr>startRequestsImmediately:<wbr>requestQueue:<wbr>serializationQueue:<wbr>interceptor:<wbr>serverTrustManager:<wbr>redirectHandler:<wbr>cachedResponseHandler:<wbr>eventMonitors:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>Session</code> from a <code>URLSession</code> and other parameters.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>When passing a <code>URLSession</code>, you must create the <code>URLSession</code> with a specific <code>delegateQueue</code> value and
    pass the <code>delegateQueue</code>&lsquo;s <code>underlyingQueue</code> as the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> parameter of this initializer.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="nf">init</span><span class="p">(</span><span class="nv">session</span><span class="p">:</span> <span class="kt">URLSession</span><span class="p">,</span>
            <span class="nv">delegate</span><span class="p">:</span> <span class="kt"><a href="../Classes/SessionDelegate.html">SessionDelegate</a></span><span class="p">,</span>
            <span class="nv">rootQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span><span class="p">,</span>
            <span class="nv">startRequestsImmediately</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
            <span class="nv">requestQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">serializationQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">serverTrustManager</span><span class="p">:</span> <span class="kt"><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">redirectHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">cachedResponseHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
            <span class="nv">eventMonitors</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/EventMonitor.html">EventMonitor</a></span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="kt"><a href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a></span><span class="p">()])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>session</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Underlying <code>URLSession</code> for this instance.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>delegate</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/SessionDelegate.html">SessionDelegate</a></code> that handles <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7sessionSo12NSURLSessionCvp">session</a></code>&lsquo;s delegate callbacks as well as <code><a href="../Classes/Request.html">Request</a></code>
                      interaction.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>rootQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Root <code>DispatchQueue</code> for all internal callbacks and state updates. <strong>MUST</strong> be a
                      serial queue.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>startRequestsImmediately</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Determines whether this instance will automatically start all <code><a href="../Classes/Request.html">Request</a></code>s. <code>true</code>
                      by default. If set to <code>false</code>, all <code><a href="../Classes/Request.html">Request</a></code>s created must have <code>.resume()</code> called.
                      on them for them to start.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>URLRequest</code> creation. By default this queue
                      will use the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its <code>target</code>. A separate queue can be used if it&rsquo;s
                      determined request creation is a bottleneck, but that should only be done after
                      careful testing and profiling. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>serializationQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform all response serialization. By default this
                      queue will use the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its <code>target</code>. A separate queue can be used if
                      it&rsquo;s determined response serialization is a bottleneck, but that should only be
                      done after careful testing and profiling. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> to be used for all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance. <code>nil</code>
                      by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>serverTrustManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></code> to be used for all trust evaluations by this instance. <code>nil</code>
                      by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>redirectHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></code> to be used by all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance. <code>nil</code> by
                      default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>cachedResponseHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></code> to be used by all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance.
                      <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>eventMonitors</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/EventMonitor.html">EventMonitor</a></code>s used by the instance. <code>[AlamofireNotifications()]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC13configuration8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo25NSURLSessionConfigurationC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc"></a>
                    <a name="//apple_ref/swift/Method/init(configuration:delegate:rootQueue:startRequestsImmediately:requestQueue:serializationQueue:interceptor:serverTrustManager:redirectHandler:cachedResponseHandler:eventMonitors:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC13configuration8delegate9rootQueue24startRequestsImmediately07requestF0013serializationF011interceptor18serverTrustManager15redirectHandler014cachedResponseQ013eventMonitorsACSo25NSURLSessionConfigurationC_AA0B8DelegateCSo17OS_dispatch_queueCSbATSgAuA18RequestInterceptor_pSgAA06ServernO0CSgAA08RedirectQ0_pSgAA06CachedsQ0_pSgSayAA12EventMonitor_pGtcfc">init(configuration:<wbr>delegate:<wbr>rootQueue:<wbr>startRequestsImmediately:<wbr>requestQueue:<wbr>serializationQueue:<wbr>interceptor:<wbr>serverTrustManager:<wbr>redirectHandler:<wbr>cachedResponseHandler:<wbr>eventMonitors:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>Session</code> from a <code>URLSessionConfiguration</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This initializer lets Alamofire handle the creation of the underlying <code>URLSession</code> and its
    <code>delegateQueue</code>, and is the recommended initializer for most uses.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">convenience</span> <span class="nf">init</span><span class="p">(</span><span class="nv">configuration</span><span class="p">:</span> <span class="kt">URLSessionConfiguration</span> <span class="o">=</span> <span class="kt">URLSessionConfiguration</span><span class="o">.</span><span class="n">af</span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                        <span class="nv">delegate</span><span class="p">:</span> <span class="kt"><a href="../Classes/SessionDelegate.html">SessionDelegate</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/SessionDelegate.html">SessionDelegate</a></span><span class="p">(),</span>
                        <span class="nv">rootQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="kt">DispatchQueue</span><span class="p">(</span><span class="nv">label</span><span class="p">:</span> <span class="s">"org.alamofire.session.rootQueue"</span><span class="p">),</span>
                        <span class="nv">startRequestsImmediately</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                        <span class="nv">requestQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">serializationQueue</span><span class="p">:</span> <span class="kt">DispatchQueue</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">serverTrustManager</span><span class="p">:</span> <span class="kt"><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">redirectHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">cachedResponseHandler</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">eventMonitors</span><span class="p">:</span> <span class="p">[</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/EventMonitor.html">EventMonitor</a></span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="kt"><a href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a></span><span class="p">()])</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>configuration</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>URLSessionConfiguration</code> to be used to create the underlying <code>URLSession</code>. Changes
                      to this value after being passed to this initializer will have no effect.
                      <code>URLSessionConfiguration.af.default</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>delegate</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/SessionDelegate.html">SessionDelegate</a></code> that handles <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7sessionSo12NSURLSessionCvp">session</a></code>&lsquo;s delegate callbacks as well as <code><a href="../Classes/Request.html">Request</a></code>
                      interaction. <code>SessionDelegate()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>rootQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Root <code>DispatchQueue</code> for all internal callbacks and state updates. <strong>MUST</strong> be a
                      serial queue. <code>DispatchQueue(label: &quot;org.alamofire.session.rootQueue&quot;)</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>startRequestsImmediately</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Determines whether this instance will automatically start all <code><a href="../Classes/Request.html">Request</a></code>s. <code>true</code>
                      by default. If set to <code>false</code>, all <code><a href="../Classes/Request.html">Request</a></code>s created must have <code>.resume()</code> called.
                      on them for them to start.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform <code>URLRequest</code> creation. By default this queue
                      will use the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its <code>target</code>. A separate queue can be used if it&rsquo;s
                      determined request creation is a bottleneck, but that should only be done after
                      careful testing and profiling. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>serializationQueue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which to perform all response serialization. By default this
                      queue will use the <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code> as its <code>target</code>. A separate queue can be used if
                      it&rsquo;s determined response serialization is a bottleneck, but that should only be
                      done after careful testing and profiling. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> to be used for all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance. <code>nil</code>
                      by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>serverTrustManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/ServerTrustManager.html">ServerTrustManager</a></code> to be used for all trust evaluations by this instance. <code>nil</code>
                      by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>redirectHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RedirectHandler.html">RedirectHandler</a></code> to be used by all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance. <code>nil</code> by
                      default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>cachedResponseHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a></code> to be used by all <code><a href="../Classes/Request.html">Request</a></code>s created by this instance.
                      <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>eventMonitors</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/EventMonitor.html">EventMonitor</a></code>s used by the instance. <code>[AlamofireNotifications()]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/All%20Requests%20API"></a>
                <a name="//apple_ref/swift/Section/All Requests API" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/All%20Requests%20API"></a>
                  <h3 class="section-name"><span>All Requests API</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC15withAllRequests7performyyShyAA7RequestCGYbc_tF"></a>
                    <a name="//apple_ref/swift/Method/withAllRequests(perform:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC15withAllRequests7performyyShyAA7RequestCGYbc_tF">withAllRequests(perform:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Perform an action on all active <code><a href="../Classes/Request.html">Request</a></code>s.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>The provided <code>action</code> closure is performed asynchronously, meaning that some <code><a href="../Classes/Request.html">Request</a></code>s may complete and
    be unavailable by time it runs. Additionally, this action is performed on the instances&rsquo;s <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC9rootQueueSo17OS_dispatch_queueCvp">rootQueue</a></code>,
    so care should be taken that actions are fast. Once the work on the <code><a href="../Classes/Request.html">Request</a></code>s is complete, any
    additional work should be performed on another queue.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">withAllRequests</span><span class="p">(</span><span class="n">perform</span> <span class="nv">action</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>action</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure to perform with all <code><a href="../Classes/Request.html">Request</a></code>s.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC17cancelAllRequests17completingOnQueue10completionySo17OS_dispatch_queueC_yyYbcSgtF"></a>
                    <a name="//apple_ref/swift/Method/cancelAllRequests(completingOnQueue:completion:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC17cancelAllRequests17completingOnQueue10completionySo17OS_dispatch_queueC_yyYbcSgtF">cancelAllRequests(completingOnQueue:<wbr>completion:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Cancel all active <code><a href="../Classes/Request.html">Request</a></code>s, optionally calling a completion handler when complete.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This is an asynchronous operation and does not block the creation of future <code><a href="../Classes/Request.html">Request</a></code>s. Cancelled
    <code><a href="../Classes/Request.html">Request</a></code>s may not cancel immediately due internal work, and may not cancel at all if they are close to
    completion when cancelled.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">cancelAllRequests</span><span class="p">(</span><span class="n">completingOnQueue</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span> <span class="nv">completion</span><span class="p">:</span> <span class="p">(</span><span class="kd">@Sendable</span> <span class="p">()</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the completion handler is run. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completion</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Closure to be called when all <code><a href="../Classes/Request.html">Request</a></code>s have been cancelled.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataRequest"></a>
                <a name="//apple_ref/swift/Section/DataRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataRequest"></a>
                  <h3 class="section-name"><span>DataRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC15RequestModifiera"></a>
                    <a name="//apple_ref/swift/Alias/RequestModifier" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Closure which provides a <code>URLRequest</code> for mutation.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">RequestModifier</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="k">inout</span> <span class="kt">URLRequest</span><span class="p">)</span> <span class="k">throws</span> <span class="o">-&gt;</span> <span class="kt">Void</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7request_6method10parameters8encoding7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/request(_:method:parameters:encoding:headers:interceptor:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7request_6method10parameters8encoding7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF">request(_:<wbr>method:<wbr>parameters:<wbr>encoding:<wbr>headers:<wbr>interceptor:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> from a <code>URLRequest</code> created using the passed components and a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">request</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                  <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                  <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                  <span class="nv">encoding</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/URLEncoding.html">URLEncoding</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                  <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                  <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                  <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></code> (a.k.a. <code>[String: Any]</code>) value to be encoded into the <code>URLRequest</code>. <code>nil</code> by
             default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></code> to be used to encode the <code>parameters</code> value into the <code>URLRequest</code>.
             <code><a href="../Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV7defaultACvpZ">URLEncoding.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7request_6method10parameters7encoder7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/request(_:method:parameters:encoder:headers:interceptor:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7request_6method10parameters7encoder7headers11interceptor0C8ModifierAA11DataRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0K11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF">request(_:<wbr>method:<wbr>parameters:<wbr>encoder:<wbr>headers:<wbr>interceptor:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> from a <code>URLRequest</code> created using the passed components, <code>Encodable</code> parameters, and a
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="n">request</span><span class="o">&lt;</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">:</span> <span class="kt">Encodable</span> <span class="o">&amp;</span> <span class="kt">Sendable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                                                    <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                                                    <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                    <span class="nv">encoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                                                    <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                    <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                    <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Encodable</code> value to be encoded into the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></code> to be used to encode the <code>parameters</code> value into the <code>URLRequest</code>.
             <code><a href="../Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ">URLEncodedFormParameterEncoder.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from
             the provided parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC7request_11interceptorAA11DataRequestCAA21URLRequestConvertible_p_AA0F11Interceptor_pSgtF"></a>
                    <a name="//apple_ref/swift/Method/request(_:interceptor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC7request_11interceptorAA11DataRequestCAA21URLRequestConvertible_p_AA0F11Interceptor_pSgtF">request(_:<wbr>interceptor:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataRequest.html">DataRequest</a></code> from a <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">request</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span> <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataRequest.html">DataRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataRequest.html">DataRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataStreamRequest"></a>
                <a name="//apple_ref/swift/Section/DataStreamRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataStreamRequest"></a>
                  <h3 class="section-name"><span>DataStreamRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC13streamRequest_6method10parameters7encoder7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatalD0CAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/streamRequest(_:method:parameters:encoder:headers:automaticallyCancelOnStreamError:interceptor:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC13streamRequest_6method10parameters7encoder7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatalD0CAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtSERzs8SendableRzlF">streamRequest(_:<wbr>method:<wbr>parameters:<wbr>encoder:<wbr>headers:<wbr>automaticallyCancelOnStreamError:<wbr>interceptor:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></code> from the passed components, <code>Encodable</code> parameters, and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="n">streamRequest</span><span class="o">&lt;</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">:</span> <span class="kt">Encodable</span> <span class="o">&amp;</span> <span class="kt">Sendable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                                                          <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                                                          <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                          <span class="nv">encoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                                                          <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                          <span class="nv">automaticallyCancelOnStreamError</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">false</span><span class="p">,</span>
                                                          <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                          <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Encodable</code> value to be encoded into the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></code> to be used to encode the <code>parameters</code> value into the
                              <code>URLRequest</code>.
                              <code><a href="../Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ">URLEncodedFormParameterEncoder.default</a></code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>automaticallyCancelOnStreamError</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> indicating whether the instance should be canceled when an <code>Error</code>
                              is thrown while serializing stream <code>Data</code>. <code>false</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code>
                              by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from
                              the provided parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code>DataStream</code> request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC13streamRequest_6method7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatajD0CAA14URLConvertible_p_AA10HTTPMethodVAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/streamRequest(_:method:headers:automaticallyCancelOnStreamError:interceptor:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC13streamRequest_6method7headers32automaticallyCancelOnStreamError11interceptor15requestModifierAA04DatajD0CAA14URLConvertible_p_AA10HTTPMethodVAA11HTTPHeadersVSgSbAA0D11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgtF">streamRequest(_:<wbr>method:<wbr>headers:<wbr>automaticallyCancelOnStreamError:<wbr>interceptor:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></code> from the passed components and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">streamRequest</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                        <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                        <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">automaticallyCancelOnStreamError</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">false</span><span class="p">,</span>
                        <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                        <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>automaticallyCancelOnStreamError</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> indicating whether the instance should be canceled when an <code>Error</code>
                              is thrown while serializing stream <code>Data</code>. <code>false</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code>
                              by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from
                              the provided parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code>DataStream</code> request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC13streamRequest_32automaticallyCancelOnStreamError11interceptorAA04DatahD0CAA21URLRequestConvertible_p_SbAA0D11Interceptor_pSgtF"></a>
                    <a name="//apple_ref/swift/Method/streamRequest(_:automaticallyCancelOnStreamError:interceptor:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC13streamRequest_32automaticallyCancelOnStreamError11interceptorAA04DatahD0CAA21URLRequestConvertible_p_SbAA0D11Interceptor_pSgtF">streamRequest(_:<wbr>automaticallyCancelOnStreamError:<wbr>interceptor:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></code> from the passed <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">streamRequest</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                        <span class="nv">automaticallyCancelOnStreamError</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">false</span><span class="p">,</span>
                        <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>automaticallyCancelOnStreamError</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> indicating whether the instance should be canceled when an <code>Error</code>
                              is thrown while serializing stream <code>Data</code>. <code>false</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code>
                               by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DataStreamRequest.html">DataStreamRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DownloadRequest"></a>
                <a name="//apple_ref/swift/Section/DownloadRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DownloadRequest"></a>
                  <h3 class="section-name"><span>DownloadRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC8download_6method10parameters8encoding7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAZ3URLV011destinationW0_AM7OptionsV7optionstA3__So17NSHTTPURLResponseCtYbcSgtF"></a>
                    <a name="//apple_ref/swift/Method/download(_:method:parameters:encoding:headers:interceptor:requestModifier:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC8download_6method10parameters8encoding7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVSDySSs8Sendable_pGSgAA17ParameterEncoding_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAZ3URLV011destinationW0_AM7OptionsV7optionstA3__So17NSHTTPURLResponseCtYbcSgtF">download(_:<wbr>method:<wbr>parameters:<wbr>encoding:<wbr>headers:<wbr>interceptor:<wbr>requestModifier:<wbr>to:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> using a <code>URLRequest</code> created using the passed components, <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, and
<code>Destination</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                   <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                   <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="nv">encoding</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></span> <span class="o">=</span> <span class="kt"><a href="../Structs/URLEncoding.html">URLEncoding</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                   <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></code> (a.k.a. <code>[String: Any]</code>) value to be encoded into the <code>URLRequest</code>. <code>nil</code> by
             default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ParameterEncoding.html">ParameterEncoding</a></code> to be used to encode the <code>parameters</code> value into the <code>URLRequest</code>.
             Defaults to <code><a href="../Structs/URLEncoding.html#/s:9Alamofire11URLEncodingV7defaultACvpZ">URLEncoding.default</a></code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used to determine how and where the downloaded file
             should be moved. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC8download_6method10parameters7encoder7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAX3URLV011destinationV0_AM7OptionsV7optionstA1__So17NSHTTPURLResponseCtYbcSgtSERzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/download(_:method:parameters:encoder:headers:interceptor:requestModifier:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC8download_6method10parameters7encoder7headers11interceptor15requestModifier2toAA15DownloadRequestCAA14URLConvertible_p_AA10HTTPMethodVxSgAA16ParameterEncoder_pAA11HTTPHeadersVSgAA0M11Interceptor_pSgy10Foundation10URLRequestVzYbKcSgAX3URLV011destinationV0_AM7OptionsV7optionstA1__So17NSHTTPURLResponseCtYbcSgtSERzs8SendableRzlF">download(_:<wbr>method:<wbr>parameters:<wbr>encoder:<wbr>headers:<wbr>interceptor:<wbr>requestModifier:<wbr>to:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> from a <code>URLRequest</code> created using the passed components, <code>Encodable</code> parameters, and
a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="n">download</span><span class="o">&lt;</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">:</span> <span class="kt">Encodable</span> <span class="o">&amp;</span> <span class="kt">Sendable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                                                     <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="k">get</span><span class="p">,</span>
                                                     <span class="nv">parameters</span><span class="p">:</span> <span class="kt"><a href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                     <span class="nv">encoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a></span><span class="o">.</span><span class="k">default</span><span class="p">,</span>
                                                     <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                     <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                     <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                                                     <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.get</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>parameters</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Value conforming to <code>Encodable</code> to be encoded into the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ParameterEncoder.html">ParameterEncoder</a></code> to be used to encode the <code>parameters</code> value into the <code>URLRequest</code>.
             Defaults to <code><a href="../Classes/URLEncodedFormParameterEncoder.html#/s:9Alamofire30URLEncodedFormParameterEncoderC7defaultACvpZ">URLEncodedFormParameterEncoder.default</a></code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used to determine how and where the downloaded file
             should be moved. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC8download_11interceptor2toAA15DownloadRequestCAA21URLRequestConvertible_p_AA0G11Interceptor_pSg10Foundation3URLV011destinationL0_AH7OptionsV7optionstAN_So17NSHTTPURLResponseCtYbcSgtF"></a>
                    <a name="//apple_ref/swift/Method/download(_:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC8download_11interceptor2toAA15DownloadRequestCAA21URLRequestConvertible_p_AA0G11Interceptor_pSg10Foundation3URLV011destinationL0_AH7OptionsV7optionstAN_So17NSHTTPURLResponseCtYbcSgtF">download(_:<wbr>interceptor:<wbr>to:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> from a <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value, a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, and a <code>Destination</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">_</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                   <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used to determine how and where the downloaded file
         should be moved. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC8download12resumingWith11interceptor2toAA15DownloadRequestC10Foundation4DataV_AA0I11Interceptor_pSgAJ3URLV011destinationM0_AI7OptionsV7optionstAP_So17NSHTTPURLResponseCtYbcSgtF"></a>
                    <a name="//apple_ref/swift/Method/download(resumingWith:interceptor:to:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC8download12resumingWith11interceptor2toAA15DownloadRequestC10Foundation4DataV_AA0I11Interceptor_pSgAJ3URLV011destinationM0_AI7OptionsV7optionstAP_So17NSHTTPURLResponseCtYbcSgtF">download(resumingWith:<wbr>interceptor:<wbr>to:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> from the <code>resumeData</code> produced from a previously cancelled <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>, as
well as a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>, and a <code>Destination</code>.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If <code>destination</code> is not specified, the download will be moved to a temporary location determined by
    Alamofire. The file will not be deleted until the system purges the temporary files.</p>

</div><div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>On some versions of all Apple platforms (iOS 10 - 10.2, macOS 10.12 - 10.12.2, tvOS 10 - 10.1, watchOS 3 - 3.1.1),
<code>resumeData</code> is broken on background URL session configurations. There&rsquo;s an underlying bug in the <code>resumeData</code>
generation logic where the data is written incorrectly and will always fail to resume the download. For more
information about the bug and possible workarounds, please refer to the <a href="http://stackoverflow.com/a/39347461/1342462">this Stack Overflow post</a>.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">download</span><span class="p">(</span><span class="n">resumingWith</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                   <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                   <span class="n">to</span> <span class="nv">destination</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span><span class="o">.</span><span class="kt">Destination</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html">DownloadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The resume data from a previously cancelled <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code> or <code>URLSessionDownloadTask</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>destination</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">DownloadRequest.Destination</a></code> closure used to determine how and where the downloaded file
         should be moved. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/DownloadRequest.html">DownloadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Data"></a>
                <a name="//apple_ref/swift/Section/Data" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Data"></a>
                  <h3 class="section-name"><span>Data</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation4DataV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation4DataV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF">upload(_:<wbr>to:<wbr>method:<wbr>headers:<wbr>interceptor:<wbr>fileManager:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the given <code>Data</code>, <code>URLRequest</code> components, and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                 <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                 <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                 <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">,</span>
                 <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Data</code> to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
             default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation4DataV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF">upload(_:<wbr>with:<wbr>interceptor:<wbr>fileManager:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the given <code>Data</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span>
                 <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>Data</code> to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/File"></a>
                <a name="//apple_ref/swift/Section/File" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/File"></a>
                  <h3 class="section-name"><span>File</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation3URLV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestC10Foundation3URLV_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0CyAM10URLRequestVzYbKcSgtF">upload(_:<wbr>to:<wbr>method:<wbr>headers:<wbr>interceptor:<wbr>fileManager:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the file at the given file <code>URL</code>, using a <code>URLRequest</code> from the provided
components and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span>
                 <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                 <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                 <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">,</span>
                 <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>fileURL</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>URL</code> of the file to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
             default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestC10Foundation3URLV_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF">upload(_:<wbr>with:<wbr>interceptor:<wbr>fileManager:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the file at the given file <code>URL</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span>
                 <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>fileURL</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>URL</code> of the file to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/InputStream"></a>
                <a name="//apple_ref/swift/Section/InputStream" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/InputStream"></a>
                  <h3 class="section-name"><span>InputStream</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCSo13NSInputStreamC_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0Cy10Foundation10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:to:method:headers:interceptor:fileManager:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_2to6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCSo13NSInputStreamC_AA14URLConvertible_pAA10HTTPMethodVAA11HTTPHeadersVSgAA0M11Interceptor_pSgSo06NSFileI0Cy10Foundation10URLRequestVzYbKcSgtF">upload(_:<wbr>to:<wbr>method:<wbr>headers:<wbr>interceptor:<wbr>fileManager:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> from the <code>InputStream</code> provided using a <code>URLRequest</code> from the provided components and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">stream</span><span class="p">:</span> <span class="kt">InputStream</span><span class="p">,</span>
                 <span class="n">to</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                 <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                 <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">,</span>
                 <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>InputStream</code> that provides the data to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
             default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the provided
             parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF"></a>
                    <a name="//apple_ref/swift/Method/upload(_:with:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload_4with11interceptor11fileManagerAA13UploadRequestCSo13NSInputStreamC_AA21URLRequestConvertible_pAA0I11Interceptor_pSgSo06NSFileG0CtF">upload(_:<wbr>with:<wbr>interceptor:<wbr>fileManager:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> from the provided <code>InputStream</code> using the <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value and
<code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="n">_</span> <span class="nv">stream</span><span class="p">:</span> <span class="kt">InputStream</span><span class="p">,</span>
                 <span class="n">with</span> <span class="nv">convertible</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>stream</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The <code>InputStream</code> that provides the data to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>convertible</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
         default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/MultipartFormData"></a>
                <a name="//apple_ref/swift/Section/MultipartFormData" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/MultipartFormData"></a>
                  <h3 class="section-name"><span>MultipartFormData</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCyAA09MultiparteF0Cc_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCyAA09MultiparteF0Cc_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF">upload(multipartFormData:<wbr>to:<wbr>usingThreshold:<wbr>method:<wbr>headers:<wbr>interceptor:<wbr>fileManager:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the multipart form data built using a closure and sent using the provided
<code>URLRequest</code> components and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="p">(</span><span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">,</span>
                 <span class="n">to</span> <span class="nv">url</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                 <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                 <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                 <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">,</span>
                 <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the
                     provided parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCyAA09MultiparteF0Cc_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF">upload(multipartFormData:<wbr>with:<wbr>usingThreshold:<wbr>interceptor:<wbr>fileManager:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> using a <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure, the provided <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code>
value, and a <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="p">(</span><span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">,</span>
                 <span class="n">with</span> <span class="nv">request</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                 <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> building closure.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>request</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCAA09MultiparteF0C_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:to:usingThreshold:method:headers:interceptor:fileManager:requestModifier:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload17multipartFormData2to14usingThreshold6method7headers11interceptor11fileManager15requestModifierAA13UploadRequestCAA09MultiparteF0C_AA14URLConvertible_ps6UInt64VAA10HTTPMethodVAA11HTTPHeadersVSgAA0R11Interceptor_pSgSo06NSFileN0Cy10Foundation10URLRequestVzYbKcSgtF">upload(multipartFormData:<wbr>to:<wbr>usingThreshold:<wbr>method:<wbr>headers:<wbr>interceptor:<wbr>fileManager:<wbr>requestModifier:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the prebuilt <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> value using the provided <code>URLRequest</code> components
and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">,</span>
                 <span class="n">to</span> <span class="nv">url</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLConvertible.html">URLConvertible</a></span><span class="p">,</span>
                 <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                 <span class="nv">method</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span> <span class="o">=</span> <span class="o">.</span><span class="n">post</span><span class="p">,</span>
                 <span class="nv">headers</span><span class="p">:</span> <span class="kt"><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">,</span>
                 <span class="nv">requestModifier</span><span class="p">:</span> <span class="kt"><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> instance to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>url</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLConvertible.html">URLConvertible</a></code> value to be used as the <code>URLRequest</code>&lsquo;s <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>method</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code> for the <code>URLRequest</code>. <code>.post</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>headers</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPHeaders.html">HTTPHeaders</a></code> value to be added to the <code>URLRequest</code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> to be used if the form data exceeds the memory threshold and is
                     written to disk before being uploaded. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>requestModifier</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/Session.html#/s:9Alamofire7SessionC15RequestModifiera">RequestModifier</a></code> which will be applied to the <code>URLRequest</code> created from the
                     provided parameters. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF"></a>
                    <a name="//apple_ref/swift/Method/upload(multipartFormData:with:usingThreshold:interceptor:fileManager:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire7SessionC6upload17multipartFormData4with14usingThreshold11interceptor11fileManagerAA13UploadRequestCAA09MultiparteF0C_AA21URLRequestConvertible_ps6UInt64VAA0N11Interceptor_pSgSo06NSFileL0CtF">upload(multipartFormData:<wbr>with:<wbr>usingThreshold:<wbr>interceptor:<wbr>fileManager:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates an <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code> for the prebuilt <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> value using the providing <code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code>
value and <code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code>.</p>

<p>It is important to understand the memory implications of uploading <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code>. If the cumulative
payload is small, encoding the data in-memory and directly uploading to a server is the by far the most
efficient approach. However, if the payload is too large, encoding the data in-memory could cause your app to
be terminated. Larger payloads must first be written to disk using input and output streams to keep the memory
footprint low, then the data can be uploaded as a stream from the resulting file. Streaming from disk MUST be
used for larger payloads such as video content.</p>

<p>The <code>encodingMemoryThreshold</code> parameter allows Alamofire to automatically determine whether to encode in-memory
or stream from disk. If the content length of the <code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> is below the <code>encodingMemoryThreshold</code>,
encoding takes place in-memory. If the content length exceeds the threshold, the data is streamed to disk
during the encoding process. Then the result is uploaded as data or as a stream depending on which encoding
technique was used.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">open</span> <span class="kd">func</span> <span class="nf">upload</span><span class="p">(</span><span class="nv">multipartFormData</span><span class="p">:</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="p">,</span>
                 <span class="n">with</span> <span class="nv">request</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></span><span class="p">,</span>
                 <span class="n">usingThreshold</span> <span class="nv">encodingMemoryThreshold</span><span class="p">:</span> <span class="kt">UInt64</span> <span class="o">=</span> <span class="kt"><a href="../Classes/MultipartFormData.html">MultipartFormData</a></span><span class="o">.</span><span class="n">encodingMemoryThreshold</span><span class="p">,</span>
                 <span class="nv">interceptor</span><span class="p">:</span> <span class="p">(</span><span class="kd">any</span> <span class="kt"><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></span><span class="p">)?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                 <span class="nv">fileManager</span><span class="p">:</span> <span class="kt">FileManager</span> <span class="o">=</span> <span class="o">.</span><span class="k">default</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/UploadRequest.html">UploadRequest</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>multipartFormData</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/MultipartFormData.html">MultipartFormData</a></code> instance to upload.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>request</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a></code> value to be used to create the <code>URLRequest</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encodingMemoryThreshold</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>Byte threshold used to determine whether the form data is encoded into memory or
                     onto disk before being uploaded. <code><a href="../Classes/MultipartFormData.html#/s:9Alamofire17MultipartFormDataC23encodingMemoryThresholds6UInt64VvpZ">MultipartFormData.encodingMemoryThreshold</a></code> by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>interceptor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/RequestInterceptor.html">RequestInterceptor</a></code> value to be used by the returned <code><a href="../Classes/DataRequest.html">DataRequest</a></code>. <code>nil</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>fileManager</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>FileManager</code> instance to be used by the returned <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>. <code><a href="../Classes/Session.html#/s:9Alamofire7SessionC7defaultACvpZ">.default</a></code> instance by
                     default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The created <code><a href="../Classes/UploadRequest.html">UploadRequest</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/RequestDelegate"></a>
                <a name="//apple_ref/swift/Section/RequestDelegate" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/RequestDelegate"></a>
                  <h3 class="section-name"><span>RequestDelegate</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15RequestDelegateP20sessionConfigurationSo012NSURLSessionE0Cvp"></a>
                    <a name="//apple_ref/swift/Property/sessionConfiguration" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15RequestDelegateP20sessionConfigurationSo012NSURLSessionE0Cvp">sessionConfiguration</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">sessionConfiguration</span><span class="p">:</span> <span class="kt">URLSessionConfiguration</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15RequestDelegateP16startImmediatelySbvp"></a>
                    <a name="//apple_ref/swift/Property/startImmediately" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15RequestDelegateP16startImmediatelySbvp">startImmediately</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">startImmediately</span><span class="p">:</span> <span class="kt">Bool</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15RequestDelegateP7cleanup5afteryAA0B0C_tF"></a>
                    <a name="//apple_ref/swift/Method/cleanup(after:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15RequestDelegateP7cleanup5afteryAA0B0C_tF">cleanup(after:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">cleanup</span><span class="p">(</span><span class="n">after</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15RequestDelegateP11retryResult3for5dueTo10completionyAA0B0C_AA7AFErrorOyAA05RetryE0OYbctF"></a>
                    <a name="//apple_ref/swift/Method/retryResult(for:dueTo:completion:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15RequestDelegateP11retryResult3for5dueTo10completionyAA0B0C_AA7AFErrorOyAA05RetryE0OYbctF">retryResult(for:<wbr>dueTo:<wbr>completion:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">retryResult</span><span class="p">(</span><span class="k">for</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span> <span class="n">dueTo</span> <span class="nv">error</span><span class="p">:</span> <span class="kt"><a href="../Enums/AFError.html">AFError</a></span><span class="p">,</span> <span class="nv">completion</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Enums/RetryResult.html">RetryResult</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15RequestDelegateP05retryB0_9withDelayyAA0B0C_SdSgtF"></a>
                    <a name="//apple_ref/swift/Method/retryRequest(_:withDelay:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15RequestDelegateP05retryB0_9withDelayyAA0B0C_SdSgtF">retryRequest(_:<wbr>withDelay:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">retryRequest</span><span class="p">(</span><span class="n">_</span> <span class="nv">request</span><span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span> <span class="n">withDelay</span> <span class="nv">timeDelay</span><span class="p">:</span> <span class="kt">TimeInterval</span><span class="p">?)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
