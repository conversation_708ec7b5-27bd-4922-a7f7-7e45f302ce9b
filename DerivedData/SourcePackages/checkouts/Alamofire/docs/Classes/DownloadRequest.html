<!DOCTYPE html>
<html lang="en">
  <head>
    <title>DownloadRequest Class Reference</title>
    <link rel="stylesheet" type="text/css" href="../css/jazzy.css" />
    <link rel="stylesheet" type="text/css" href="../css/highlight.css" />
    <meta charset="utf-8">
    <script src="../js/jquery.min.js" defer></script>
    <script src="../js/jazzy.js" defer></script>
    
    <script src="../js/lunr.min.js" defer></script>
    <script src="../js/typeahead.jquery.js" defer></script>
    <script src="../js/jazzy.search.js" defer></script>
  </head>
  <body>

    <a name="//apple_ref/swift/Class/DownloadRequest" class="dashAnchor"></a>

    <a title="DownloadRequest Class Reference"></a>

    <header class="header">
      <p class="header-col header-col--primary">
        <a class="header-link" href="../index.html">
          Alamofire 5.10.2 Docs
        </a>
         (96% documented)
      </p>
    
      <div class="header-col--secondary">
        <form role="search" action="../search.json">
          <input type="text" placeholder="Search documentation" data-typeahead>
        </form>
      </div>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="https://github.com/Alamofire/Alamofire">
            <img class="header-icon" src="../img/gh.png" alt="GitHub"/>
            View on GitHub
          </a>
        </p>
    
        <p class="header-col header-col--secondary">
          <a class="header-link" href="dash-feed://https%3A%2F%2Falamofire.github.io%2FAlamofire%2Fdocsets%2FAlamofire.xml">
            <img class="header-icon" src="../img/dash.png" alt="Dash"/>
            Install in Dash
          </a>
        </p>
    </header>

    <p class="breadcrumbs">
      <a class="breadcrumb" href="../index.html">Alamofire</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      <a class="breadcrumb" href="../Classes.html">Classes</a>
      <img class="carat" src="../img/carat.png" alt=""/>
      DownloadRequest Class Reference
    </p>

    <div class="content-wrapper">
      <nav class="navigation">
        <ul class="nav-groups">
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Classes.html">Classes</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Adapter.html">Adapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AlamofireNotifications.html">AlamofireNotifications</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor.html">AuthenticationInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/AuthenticationInterceptor/RefreshWindow.html">– RefreshWindow</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ClosureEventMonitor.html">ClosureEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeEventMonitor.html">CompositeEventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/CompositeTrustEvaluator.html">CompositeTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ConnectionLostRetryPolicy.html">ConnectionLostRetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataRequest.html">DataRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest.html">DataStreamRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Stream.html">– Stream</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Event.html">– Event</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/Completion.html">– Completion</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DataStreamRequest/CancellationToken.html">– CancellationToken</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DefaultTrustEvaluator.html">DefaultTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DisabledTrustEvaluator.html">DisabledTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest.html">DownloadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/DownloadRequest/Downloadable.html">– Downloadable</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Interceptor.html">Interceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONParameterEncoder.html">JSONParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/MultipartFormData.html">MultipartFormData</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager.html">NetworkReachabilityManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/NetworkReachabilityManager/NetworkReachabilityStatus.html">– NetworkReachabilityStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PinnedCertificatesTrustEvaluator.html">PinnedCertificatesTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/PublicKeysTrustEvaluator.html">PublicKeysTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request.html">Request</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/State.html">– State</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Request/ResponseDisposition.html">– ResponseDisposition</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Retrier.html">Retrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RetryPolicy.html">RetryPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator.html">RevocationTrustEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/RevocationTrustEvaluator/Options.html">– Options</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/ServerTrustManager.html">ServerTrustManager</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/Session.html">Session</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/SessionDelegate.html">SessionDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder.html">URLEncodedFormEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DataEncoding.html">– DataEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/DateEncoding.html">– DateEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyEncoding.html">– KeyEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/KeyPathEncoding.html">– KeyPathEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/NilEncoding.html">– NilEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/SpaceEncoding.html">– SpaceEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormEncoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder.html">URLEncodedFormParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/URLEncodedFormParameterEncoder/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest.html">UploadRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Classes/UploadRequest/Uploadable.html">– Uploadable</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Global%20Variables.html">Global Variables</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Global%20Variables.html#/s:9Alamofire2AFAA7SessionCvp">AF</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Enums.html">Enumerations</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError.html">AFError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/MultipartEncodingFailureReason.html">– MultipartEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/UnexpectedInputStreamLength.html">– UnexpectedInputStreamLength</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncodingFailureReason.html">– ParameterEncodingFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ParameterEncoderFailureReason.html">– ParameterEncoderFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseValidationFailureReason.html">– ResponseValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ResponseSerializationFailureReason.html">– ResponseSerializationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/ServerTrustFailureReason.html">– ServerTrustFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFError/URLRequestValidationFailureReason.html">– URLRequestValidationFailureReason</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AFInfo.html">AFInfo</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/AuthenticationError.html">AuthenticationError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Enums/RetryResult.html">RetryResult</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Extensions.html">Extensions</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:Sa">Array</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:objc(cs)NSBundle">Bundle</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/CharacterSet.html">CharacterSet</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Error.html">Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/HTTPURLResponse.html">HTTPURLResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation11JSONDecoderC">JSONDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/Notification.html">Notification</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@OSStatus">OSStatus</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/s:10Foundation19PropertyListDecoderC">PropertyListDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/Protected">Protected</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecCertificateRef">SecCertificate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecPolicyRef">SecPolicy</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@T@SecTrustRef">SecTrust</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions.html#/c:@E@SecTrustResultType">SecTrustResultType</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/String.html">String</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URL.html">URL</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLComponents.html">URLComponents</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLRequest.html">URLRequest</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/URLSessionConfiguration.html">URLSessionConfiguration</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Extensions/%5BServerTrustEvaluating%5D.html">[ServerTrustEvaluating]</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Protocols.html">Protocols</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AlamofireExtended.html">AlamofireExtended</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/AuthenticationCredential.html">AuthenticationCredential</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/Authenticator.html">Authenticator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/CachedResponseHandler.html">CachedResponseHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataDecoder.html">DataDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataPreprocessor.html">DataPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataResponseSerializerProtocol.html">DataResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DataStreamSerializer.html">DataStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EmptyResponse.html">EmptyResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/EventMonitor.html">EventMonitor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoder.html">ParameterEncoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ParameterEncoding.html">ParameterEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RedirectHandler.html">RedirectHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestAdapter.html">RequestAdapter</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestDelegate.html">RequestDelegate</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestInterceptor.html">RequestInterceptor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/RequestRetrier.html">RequestRetrier</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ResponseSerializer.html">ResponseSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/ServerTrustEvaluating.html">ServerTrustEvaluating</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLConvertible.html">URLConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/URLRequestConvertible.html">URLRequestConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols.html#/s:9Alamofire17UploadConvertibleP">UploadConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/UploadableConvertible.html">UploadableConvertible</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Protocols/WebSocketMessageSerializer.html">WebSocketMessageSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Structs.html">Structures</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/AlamofireExtension.html">AlamofireExtension</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponse.html">DataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataResponsePublisher.html">DataResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamPublisher.html">DataStreamPublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataStreamTask.html">DataStreamTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DataTask.html">DataTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableStreamSerializer.html">DecodableStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder.html">DecodableWebSocketMessageDecoder</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DecodableWebSocketMessageDecoder/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html">DeflateRequestCompressor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor/DuplicateHeaderBehavior.html">– DuplicateHeaderBehavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DeflateRequestCompressor.html#/s:9Alamofire24DeflateRequestCompressorV20DuplicateHeaderErrorV">– DuplicateHeaderError</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponse.html">DownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/DownloadTask.html">DownloadTask</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Empty.html">Empty</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/GoogleXSSIPreprocessor.html">GoogleXSSIPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeader.html">HTTPHeader</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPHeaders.html">HTTPHeaders</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/HTTPMethod.html">HTTPMethod</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding.html">JSONEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/JSONEncoding/Error.html">– Error</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughPreprocessor.html">PassthroughPreprocessor</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/PassthroughStreamSerializer.html">PassthroughStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector.html">Redirector</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/Redirector/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/RequestAdapterState.html">RequestAdapterState</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher.html">ResponseCacher</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/ResponseCacher/Behavior.html">– Behavior</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf.html">StreamOf</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StreamOf/Iterator.html">– Iterator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/StringStreamSerializer.html">StringStreamSerializer</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding.html">URLEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/Destination.html">– Destination</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/ArrayEncoding.html">– ArrayEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLEncoding/BoolEncoding.html">– BoolEncoding</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a>
              </li>
            </ul>
          </li>
          <li class="nav-group-name">
            <a class="nav-group-name-link" href="../Typealiases.html">Type Aliases</a>
            <ul class="nav-group-tasks">
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire14AFDataResponsea">AFDataResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire8AFResulta">AFResult</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12AdaptHandlera">AdaptHandler</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire17DisabledEvaluatora">DisabledEvaluator</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire10Parametersa">Parameters</a>
              </li>
              <li class="nav-group-task">
                <a class="nav-group-task-link" href="../Typealiases.html#/s:9Alamofire12RetryHandlera">RetryHandler</a>
              </li>
            </ul>
          </li>
        </ul>
      </nav>
      <article class="main-content">

        <section class="section">
          <div class="section-content top-matter">
            <h1>DownloadRequest</h1>
              <div class="declaration">
                <div class="language">
                  
                  <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">final</span> <span class="kd">class</span> <span class="kt">DownloadRequest</span> <span class="p">:</span> <span class="kt"><a href="../Classes/Request.html">Request</a></span><span class="p">,</span> <span class="kd">@unchecked</span> <span class="kt">Sendable</span></code></pre>

                </div>
              </div>
            <p><code><a href="../Classes/Request.html">Request</a></code> subclass which downloads <code>Data</code> to a file on disk using <code>URLSessionDownloadTask</code>.</p>

          </div>
        </section>

        <section class="section">
          <div class="section-content">
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC7OptionsV"></a>
                    <a name="//apple_ref/swift/Struct/Options" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC7OptionsV">Options</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A set of options to be executed prior to moving a downloaded file from the temporary <code>URL</code> to the destination
<code>URL</code>.</p>

                        <a href="../Classes/DownloadRequest/Options.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">struct</span> <span class="kt">Options</span> <span class="p">:</span> <span class="kt">OptionSet</span><span class="p">,</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Destination"></a>
                <a name="//apple_ref/swift/Section/Destination" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Destination"></a>
                  <h3 class="section-name"><span>Destination</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC11Destinationa"></a>
                    <a name="//apple_ref/swift/Alias/Destination" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC11Destinationa">Destination</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A closure executed once a <code>DownloadRequest</code> has successfully completed in order to determine where to move the
temporary file written to during the download process. The closure takes two arguments: the temporary file URL
and the <code>HTTPURLResponse</code>, and returns two values: the file URL where the temporary file should be moved and
the options defining how the file should be moved.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    Downloads from a local <code>file://</code> <code>URL</code>s do not use the <code>Destination</code> closure, as those downloads do not
    return an <code>HTTPURLResponse</code>. Instead the file is merely moved within the temporary directory.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">Destination</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">temporaryURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span>
                                          <span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="p">(</span><span class="nv">destinationURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">,</span> <span class="nv">options</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest/Options.html">Options</a></span><span class="p">)</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC09suggestedB11Destination3for2in7options10Foundation3URLV011destinationJ0_AC7OptionsVAGtAJ_So17NSHTTPURLResponseCtYbcSo21NSSearchPathDirectoryV_So0nO10DomainMaskVAMtFZ"></a>
                    <a name="//apple_ref/swift/Method/suggestedDownloadDestination(for:in:options:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC09suggestedB11Destination3for2in7options10Foundation3URLV011destinationJ0_AC7OptionsVAGtAJ_So17NSHTTPURLResponseCtYbcSo21NSSearchPathDirectoryV_So0nO10DomainMaskVAMtFZ">suggestedDownloadDestination(for:<wbr>in:<wbr>options:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a download file destination closure which uses the default file manager to move the temporary file to a
file URL in the first available directory with the specified search path directory and search path domain mask.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">class</span> <span class="kd">func</span> <span class="nf">suggestedDownloadDestination</span><span class="p">(</span><span class="k">for</span> <span class="nv">directory</span><span class="p">:</span> <span class="kt">FileManager</span><span class="o">.</span><span class="kt">SearchPathDirectory</span> <span class="o">=</span> <span class="o">.</span><span class="n">documentDirectory</span><span class="p">,</span>
                                               <span class="k">in</span> <span class="nv">domain</span><span class="p">:</span> <span class="kt">FileManager</span><span class="o">.</span><span class="kt">SearchPathDomainMask</span> <span class="o">=</span> <span class="o">.</span><span class="n">userDomainMask</span><span class="p">,</span>
                                               <span class="nv">options</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest/Options.html">Options</a></span> <span class="o">=</span> <span class="p">[])</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">Destination</a></span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>directory</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The search path directory. <code>.documentDirectory</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>domain</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The search path domain mask. <code>.userDomainMask</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>options</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest/Options.html">DownloadRequest.Options</a></code> used when moving the downloaded file to its destination. None by
       default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC11Destinationa">Destination</a></code> closure.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Downloadable"></a>
                <a name="//apple_ref/swift/Section/Downloadable" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Downloadable"></a>
                  <h3 class="section-name"><span>Downloadable</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC12DownloadableO"></a>
                    <a name="//apple_ref/swift/Enum/Downloadable" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC12DownloadableO">Downloadable</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Type describing the source used to create the underlying <code>URLSessionDownloadTask</code>.</p>

                        <a href="../Classes/DownloadRequest/Downloadable.html" class="slightly-smaller">See more</a>
                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">enum</span> <span class="kt">Downloadable</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Mutable%20State"></a>
                <a name="//apple_ref/swift/Section/Mutable State" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Mutable%20State"></a>
                  <h3 class="section-name"><span>Mutable State</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC10resumeData10Foundation0E0VSgvp"></a>
                    <a name="//apple_ref/swift/Property/resumeData" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC10resumeData10Foundation0E0VSgvp">resumeData</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>If the download is resumable and is eventually cancelled or fails, this value may be used to resume the download
using the <code>download(resumingWith data:)</code> API.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    For more information about <code>resumeData</code>, see <a href="https://developer.apple.com/documentation/foundation/urlsessiondownloadtask/1411634-cancel">Apple&rsquo;s documentation</a>.

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">resumeData</span><span class="p">:</span> <span class="kt">Data</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC7fileURL10Foundation0E0VSgvp"></a>
                    <a name="//apple_ref/swift/Property/fileURL" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC7fileURL10Foundation0E0VSgvp">fileURL</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>If the download is successful, the <code>URL</code> where the file was downloaded.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">var</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">?</span> <span class="p">{</span> <span class="k">get</span> <span class="p">}</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Initial%20State"></a>
                <a name="//apple_ref/swift/Section/Initial State" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Initial%20State"></a>
                  <h3 class="section-name"><span>Initial State</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC12downloadableAC12DownloadableOvp"></a>
                    <a name="//apple_ref/swift/Property/downloadable" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC12downloadableAC12DownloadableOvp">downloadable</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p><code><a href="../Classes/DownloadRequest/Downloadable.html">Downloadable</a></code> value used for this instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="k">let</span> <span class="nv">downloadable</span><span class="p">:</span> <span class="kt"><a href="../Classes/DownloadRequest/Downloadable.html">Downloadable</a></span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC4task13forResumeData5usingSo16NSURLSessionTaskC10Foundation0G0V_So0I0CtF"></a>
                    <a name="//apple_ref/swift/Method/task(forResumeData:using:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC4task13forResumeData5usingSo16NSURLSessionTaskC10Foundation0G0V_So0I0CtF">task(forResumeData:<wbr>using:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code>URLSessionTask</code> from the provided resume data.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">task</span><span class="p">(</span><span class="n">forResumeData</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">,</span> <span class="n">using</span> <span class="nv">session</span><span class="p">:</span> <span class="kt">URLSession</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">URLSessionTask</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>data</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Data</code> used to resume the download.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>session</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>URLSession</code> used to create the <code>URLSessionTask</code>.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code>URLSessionTask</code> created.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC6cancelACXDyF"></a>
                    <a name="//apple_ref/swift/Method/cancel()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC6cancelACXDyF">cancel()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Cancels the instance. Once cancelled, a <code>DownloadRequest</code> can no longer be resumed or suspended.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This method will NOT produce resume data. If you wish to cancel and produce resume data, use
    <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC6cancel19producingResumeDataACXDSb_tF">cancel(producingResumeData:)</a></code> or <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC6cancel21byProducingResumeDataACXDy10Foundation0H0VSgc_tF">cancel(byProducingResumeData:)</a></code>.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="k">override</span> <span class="kd">public</span> <span class="kd">func</span> <span class="nf">cancel</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC6cancel19producingResumeDataACXDSb_tF"></a>
                    <a name="//apple_ref/swift/Method/cancel(producingResumeData:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC6cancel19producingResumeDataACXDSb_tF">cancel(producingResumeData:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Cancels the instance, optionally producing resume data. Once cancelled, a <code>DownloadRequest</code> can no longer be
resumed or suspended.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If <code>producingResumeData</code> is <code>true</code>, the <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10resumeData10Foundation0E0VSgvp">resumeData</a></code> property will be populated with any resume data, if
    available.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">cancel</span><span class="p">(</span><span class="n">producingResumeData</span> <span class="nv">shouldProduceResumeData</span><span class="p">:</span> <span class="kt">Bool</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC6cancel21byProducingResumeDataACXDy10Foundation0H0VSgc_tF"></a>
                    <a name="//apple_ref/swift/Method/cancel(byProducingResumeData:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC6cancel21byProducingResumeDataACXDy10Foundation0H0VSgc_tF">cancel(byProducingResumeData:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Cancels the instance while producing resume data. Once cancelled, a <code>DownloadRequest</code> can no longer be resumed
or suspended.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>The resume data passed to the completion handler will also be available on the instance&rsquo;s <code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10resumeData10Foundation0E0VSgvp">resumeData</a></code>
    property.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">cancel</span><span class="p">(</span><span class="n">byProducingResumeData</span> <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">data</span><span class="p">:</span> <span class="kt">Data</span><span class="p">?)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The completion handler that is called when the download has been successfully
                           cancelled. It is not guaranteed to be called on a particular queue, so you may
                           want use an appropriate queue to perform your work.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI3URLVSgtYbcF"></a>
                    <a name="//apple_ref/swift/Method/validate(_:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8validateyACXDs6ResultOyyts5Error_pG10Foundation10URLRequestVSg_So17NSHTTPURLResponseCAI3URLVSgtYbcF">validate(_:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates the request, using the specified closure.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">(</span><span class="n">_</span> <span class="nv">validation</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kt"><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10Validationa">Validation</a></span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>validation</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Classes/DownloadRequest.html#/s:9Alamofire15DownloadRequestC10Validationa">Validation</a></code> closure to validate the response.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/Response%20Serialization"></a>
                <a name="//apple_ref/swift/Section/Response Serialization" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/Response%20Serialization"></a>
                  <h3 class="section-name"><span>Response Serialization</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation3URLVSgAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8response5queue17completionHandlerACXDSo012OS_dispatch_E0C_yAA0B8ResponseVy10Foundation3URLVSgAA7AFErrorOGctF">response(queue:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">response</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                     <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">URL</span><span class="p">?</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0bkF8ProtocolRzlF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:responseSerializer:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0bkF8ProtocolRzlF">response(queue:<wbr>responseSerializer:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">response</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></span><span class="o">&gt;</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                                     <span class="nv">responseSerializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                     <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>responseSerializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The response serializer responsible for serializing the request, response, and data
                contained in the destination <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0kF0RzlF"></a>
                    <a name="//apple_ref/swift/Method/response(queue:responseSerializer:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8response5queue0D10Serializer17completionHandlerACXDSo012OS_dispatch_E0C_xyAA0B8ResponseVy16SerializedObjectQzAA7AFErrorOGYbctAA0kF0RzlF">response(queue:<wbr>responseSerializer:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">response</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                                     <span class="nv">responseSerializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                     <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>responseSerializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The response serializer responsible for serializing the request, response, and data
                contained in the destination <code>URL</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The code to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC11responseURL5queue17completionHandlerACXDSo012OS_dispatch_F0C_yAA0B8ResponseVy10Foundation0E0VAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseURL(queue:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC11responseURL5queue17completionHandlerACXDSo012OS_dispatch_F0C_yAA0B8ResponseVy10Foundation0E0VAA7AFErrorOGctF">responseURL(queue:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a></code> to be called once the request is finished.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseURL</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                        <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">URL</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC12responseData5queue16dataPreprocessor18emptyResponseCodes0iC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0eH0_pShySiGShyAA10HTTPMethodVGyAA0bJ0Vy10Foundation0E0VAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseData(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC12responseData5queue16dataPreprocessor18emptyResponseCodes0iC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA0eH0_pShySiGShyAA10HTTPMethodVGyAA0bJ0Vy10Foundation0E0VAA7AFErrorOGctF">responseData(queue:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></code> to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseData</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                         <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                         <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                         <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                         <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is called. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseString(queue:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC14responseString5queue16dataPreprocessor8encoding18emptyResponseCodes0jC7Methods17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGyAA0bK0VySSAA7AFErrorOGctF">responseString(queue:<wbr>dataPreprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></code> to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseString</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                           <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                           <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                           <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                           <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The string encoding. Defaults to <code>nil</code>, in which case the encoding will be determined
                 from the server response, falling back to the default HTTP character set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF"></a>
                    <a name="//apple_ref/swift/Method/responseJSON(queue:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:options:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC12responseJSON5queue16dataPreprocessor18emptyResponseCodes0iC7Methods7options17completionHandlerACXDSo012OS_dispatch_F0C_AA04DataH0_pShySiGShyAA10HTTPMethodVGSo20NSJSONReadingOptionsVyAA0bJ0VyypAA7AFErrorOGctF">responseJSON(queue:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>options:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></code> to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="o">*</span><span class="p">,</span> <span class="n">deprecated</span><span class="p">,</span> <span class="nv">message</span><span class="p">:</span> <span class="s">"responseJSON deprecated and will be removed in Alamofire 6. Use responseDecodable instead."</span><span class="p">)</span>
<span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">responseJSON</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                         <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                         <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                         <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/JSONResponseSerializer.html">JSONResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                         <span class="nv">options</span><span class="p">:</span> <span class="kt">JSONSerialization</span><span class="o">.</span><span class="kt">ReadingOptions</span> <span class="o">=</span> <span class="o">.</span><span class="n">allowFragments</span><span class="p">,</span>
                         <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">Any</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>options</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>JSONSerialization.ReadingOptions</code> used when parsing the response. <code>.allowFragments</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA04DataI0_pAA0S7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF"></a>
                    <a name="//apple_ref/swift/Method/responseDecodable(of:queue:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:completionHandler:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC17responseDecodable2of5queue16dataPreprocessor7decoder18emptyResponseCodes0kC7Methods17completionHandlerACXDxm_So012OS_dispatch_G0CAA04DataI0_pAA0S7Decoder_pShySiGShyAA10HTTPMethodVGyAA0bL0VyxAA7AFErrorOGctSeRzlF">responseDecodable(of:<wbr>queue:<wbr>dataPreprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>completionHandler:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Adds a handler using a <code><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></code> to be called once the request has finished.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This handler will read the entire downloaded file into memory, use with caution.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">responseDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">of</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                            <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                            <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                            <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                            <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                            <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">,</span>
                                            <span class="nv">completionHandler</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="kt"><a href="../Typealiases.html#/s:9Alamofire18AFDownloadResponsea">AFDownloadResponse</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt">Void</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">T</span><span class="p">:</span> <span class="kt">Sendable</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to decode from response data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The queue on which the completion handler is dispatched. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                 <code>completionHandler</code>. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> to use to decode the response. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>completionHandler</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>A closure to be executed once the request has finished.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DataRequest%20%2F%20UploadRequest"></a>
                <a name="//apple_ref/swift/Section/DataRequest / UploadRequest" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DataRequest%20%2F%20UploadRequest"></a>
                  <h3 class="section-name"><span>DataRequest / UploadRequest</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF"></a>
                    <a name="//apple_ref/swift/Method/publishResponse(using:on:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0E10SerializerRz16SerializedObjectQzRs_r0_lF">publishResponse(using:<wbr>on:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance using the given <code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> and <code>DispatchQueue</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishResponse</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="p">,</span> <span class="kt">T</span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span> <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span>
    <span class="k">where</span> <span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span> <span class="o">==</span> <span class="kt">T</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> used to serialize the response <code>Data</code> from disk.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DownloadResponse.html">DownloadResponse</a></code> will be published.<code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0bE18SerializerProtocolRz16SerializedObjectQzRs_r0_lF"></a>
                    <a name="//apple_ref/swift/Method/publishResponse(using:on:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC15publishResponse5using2onAA0bE9PublisherVyq_Gx_So17OS_dispatch_queueCtAA0bE18SerializerProtocolRz16SerializedObjectQzRs_r0_lF">publishResponse(using:<wbr>on:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance using the given <code><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></code> and
<code>DispatchQueue</code>.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishResponse</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></span><span class="p">,</span> <span class="kt">T</span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span> <span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span>
    <span class="k">where</span> <span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span> <span class="o">==</span> <span class="kt">T</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DownloadResponseSerializer</code> used to serialize the response <code>Data</code> from disk.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DownloadResponse.html">DownloadResponse</a></code> will be published.<code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC10publishURL5queueAA0B17ResponsePublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_tF"></a>
                    <a name="//apple_ref/swift/Method/publishURL(queue:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC10publishURL5queueAA0B17ResponsePublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_tF">publishURL(queue:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href="../Structs/URLResponseSerializer.html">URLResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishURL</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">URL</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DownloadResponse.html">DownloadResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC11publishData5queue12preprocessor18emptyResponseCodes0hC7MethodsAA0bI9PublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_AA0E12Preprocessor_pShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/publishData(queue:preprocessor:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC11publishData5queue12preprocessor18emptyResponseCodes0hC7MethodsAA0bI9PublisherVy10Foundation0E0VGSo012OS_dispatch_F0C_AA0E12Preprocessor_pShySiGShyAA10HTTPMethodVGtF">publishData(queue:<wbr>preprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishData</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                        <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                        <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                        <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DownloadResponse.html">DownloadResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization. <code>PassthroughPreprocessor()</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless of
                 status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA16DataPreprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/publishString(queue:preprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC13publishString5queue12preprocessor8encoding18emptyResponseCodes0iC7MethodsAA0bJ9PublisherVySSGSo012OS_dispatch_F0C_AA16DataPreprocessor_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF">publishString(queue:<wbr>preprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></code> to serialize the
response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishString</span><span class="p">(</span><span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                          <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                          <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                          <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                          <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization. <code>PassthroughPreprocessor()</code>
                 by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>String.Encoding</code> to parse the response. <code>nil</code> by default, in which case the encoding
                 will be determined by the server response, falling back to the default HTTP character
                 set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless of
                 status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyResponseMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jK7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">publishDecodable(type:<wbr>queue:<wbr>preprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyResponseMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Undocumented</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@_disfavoredOverload</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                           <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                           <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                           <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                           <span class="nv">emptyResponseMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/publishDecodable(type:queue:preprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC16publishDecodable4type5queue12preprocessor7decoder18emptyResponseCodes0jC7MethodsAA0bK9PublisherVyxGxm_So012OS_dispatch_G0CAA16DataPreprocessor_pAA0Q7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">publishDecodable(type:<wbr>queue:<wbr>preprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance and uses a <code><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></code> to serialize
the response.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">publishDecodable</span><span class="o">&lt;</span><span class="kt">T</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="nv">type</span><span class="p">:</span> <span class="kt">T</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">T</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                           <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">,</span>
                                           <span class="nv">preprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                           <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                           <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                           <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">T</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to which to decode response <code>Data</code>. Inferred from the context by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DataResponse.html">DataResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>preprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which filters the <code>Data</code> before serialization.
                 <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> instance used to decode response <code>Data</code>. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;Int&gt;</code> of HTTP status codes for which empty responses are allowed. <code>[204, 205]</code> by
                 default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Set&lt;HTTPMethod&gt;</code> of <code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are allowed, regardless
                 of status code. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC19publishUnserialized2onAA0B17ResponsePublisherVy10Foundation3URLVSgGSo17OS_dispatch_queueC_tF"></a>
                    <a name="//apple_ref/swift/Method/publishUnserialized(on:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC19publishUnserialized2onAA0B17ResponsePublisherVy10Foundation3URLVSgGSo17OS_dispatch_queueC_tF">publishUnserialized(on:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code> for this instance which does not serialize the response before publishing.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@available</span><span class="p">(</span><span class="n">macOS</span> <span class="mf">10.15</span><span class="p">,</span> <span class="n">iOS</span> <span class="mi">13</span><span class="p">,</span> <span class="n">watchOS</span> <span class="mi">6</span><span class="p">,</span> <span class="n">tvOS</span> <span class="mi">13</span><span class="p">,</span> <span class="o">*</span><span class="p">)</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">publishUnserialized</span><span class="p">(</span><span class="n">on</span> <span class="nv">queue</span><span class="p">:</span> <span class="kt">DispatchQueue</span> <span class="o">=</span> <span class="o">.</span><span class="n">main</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></span><span class="o">&lt;</span><span class="kt">URL</span><span class="p">?</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>queue</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>DispatchQueue</code> on which the <code><a href="../Structs/DownloadResponse.html">DownloadResponse</a></code> will be published. <code>.main</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadResponsePublisher.html">DownloadResponsePublisher</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <div class="task-name-container">
                <a name="/DownloadTask"></a>
                <a name="//apple_ref/swift/Section/DownloadTask" class="dashAnchor"></a>
                <div class="section-name-container">
                  <a class="section-name-link" href="#/DownloadTask"></a>
                  <h3 class="section-name"><span>DownloadTask</span>
</h3>
                </div>
              </div>
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC15serializingData23automaticallyCancelling16dataPreprocessor18emptyResponseCodes0jC7MethodsAA0B4TaskVy10Foundation0E0VGSb_AA0eI0_pShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/serializingData(automaticallyCancelling:dataPreprocessor:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC15serializingData23automaticallyCancelling16dataPreprocessor18emptyResponseCodes0jC7MethodsAA0B4TaskVy10Foundation0E0VGSb_AA0eI0_pShySiGShyAA10HTTPMethodVGtF">serializingData(automaticallyCancelling:<wbr>dataPreprocessor:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> a <code>Data</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">serializingData</span><span class="p">(</span><span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                            <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                            <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                            <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DataResponseSerializer.html">DataResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">Data</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before completion.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP response codes for which empty responses are allowed. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA04DataI0_pAA0P7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingDecodable(_:automaticallyCancelling:dataPreprocessor:decoder:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC20serializingDecodable_23automaticallyCancelling16dataPreprocessor7decoder18emptyResponseCodes0kC7MethodsAA0B4TaskVyxGxm_SbAA04DataI0_pAA0P7Decoder_pShySiGShyAA10HTTPMethodVGtSeRzs8SendableRzlF">serializingDecodable(_:<wbr>automaticallyCancelling:<wbr>dataPreprocessor:<wbr>decoder:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> serialization of a <code>Decodable</code> value.</p>
<div class="aside aside-note">
    <p class="aside-title">Note</p>
    <p>This serializer reads the entire response into memory before parsing.</p>

</div>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingDecodable</span><span class="o">&lt;</span><span class="kt">Value</span><span class="p">:</span> <span class="kt">Decodable</span><span class="o">&gt;</span><span class="p">(</span><span class="n">_</span> <span class="nv">type</span><span class="p">:</span> <span class="kt">Value</span><span class="o">.</span><span class="k">Type</span> <span class="o">=</span> <span class="kt">Value</span><span class="o">.</span><span class="k">self</span><span class="p">,</span>
                                                   <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                                                   <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                                                   <span class="nv">decoder</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataDecoder.html">DataDecoder</a></span> <span class="o">=</span> <span class="kt">JSONDecoder</span><span class="p">(),</span>
                                                   <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                                                   <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/DecodableResponseSerializer.html">DecodableResponseSerializer</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">Value</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>type</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Decodable</code> type to decode from response data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the serializer.
                       <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>decoder</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataDecoder.html">DataDecoder</a></code> to use to decode the response. <code>JSONDecoder()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC28serializingDownloadedFileURL23automaticallyCancellingAA0B4TaskVy10Foundation0G0VGSb_tF"></a>
                    <a name="//apple_ref/swift/Method/serializingDownloadedFileURL(automaticallyCancelling:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC28serializingDownloadedFileURL23automaticallyCancellingAA0B4TaskVy10Foundation0G0VGSb_tF">serializingDownloadedFileURL(automaticallyCancelling:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> serialization of the downloaded file&rsquo;s <code>URL</code> on disk.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">serializingDownloadedFileURL</span><span class="p">(</span><span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">URL</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA04DataI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF"></a>
                    <a name="//apple_ref/swift/Method/serializingString(automaticallyCancelling:dataPreprocessor:encoding:emptyResponseCodes:emptyRequestMethods:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC17serializingString23automaticallyCancelling16dataPreprocessor8encoding18emptyResponseCodes0kC7MethodsAA0B4TaskVySSGSb_AA04DataI0_pSS10FoundationE8EncodingVSgShySiGShyAA10HTTPMethodVGtF">serializingString(automaticallyCancelling:<wbr>dataPreprocessor:<wbr>encoding:<wbr>emptyResponseCodes:<wbr>emptyRequestMethods:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> serialization of a <code>String</code> value.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="nf">serializingString</span><span class="p">(</span><span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">,</span>
                              <span class="nv">dataPreprocessor</span><span class="p">:</span> <span class="kd">any</span> <span class="kt"><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultDataPreprocessor</span><span class="p">,</span>
                              <span class="nv">encoding</span><span class="p">:</span> <span class="kt">String</span><span class="o">.</span><span class="kt">Encoding</span><span class="p">?</span> <span class="o">=</span> <span class="kc">nil</span><span class="p">,</span>
                              <span class="nv">emptyResponseCodes</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt">Int</span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyResponseCodes</span><span class="p">,</span>
                              <span class="nv">emptyRequestMethods</span><span class="p">:</span> <span class="kt">Set</span><span class="o">&lt;</span><span class="kt"><a href="../Structs/HTTPMethod.html">HTTPMethod</a></span><span class="o">&gt;</span> <span class="o">=</span> <span class="kt"><a href="../Classes/StringResponseSerializer.html">StringResponseSerializer</a></span><span class="o">.</span><span class="n">defaultEmptyRequestMethods</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">String</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>dataPreprocessor</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DataPreprocessor.html">DataPreprocessor</a></code> which processes the received <code>Data</code> before calling the
                       serializer. <code>PassthroughPreprocessor()</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>encoding</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>String.Encoding</code> to use during serialization. Defaults to <code>nil</code>, in which case
                       the encoding will be determined from the server response, falling back to the
                       default HTTP character set, <code>ISO-8859-1</code>.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyResponseCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>HTTP status codes for which empty responses are always valid. <code>[204, 205]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>emptyRequestMethods</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Structs/HTTPMethod.html">HTTPMethod</a></code>s for which empty responses are always valid. <code>[.head]</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA18ResponseSerializerRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingDownload(using:automaticallyCancelling:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA18ResponseSerializerRzlF">serializingDownload(using:<wbr>automaticallyCancelling:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> serialization using the provided <code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingDownload</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/ResponseSerializer.html">ResponseSerializer</a></code> responsible for serializing the request, response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0B26ResponseSerializerProtocolRzlF"></a>
                    <a name="//apple_ref/swift/Method/serializingDownload(using:automaticallyCancelling:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC011serializingB05using23automaticallyCancellingAA0B4TaskVy16SerializedObjectQzGx_SbtAA0B26ResponseSerializerProtocolRzlF">serializingDownload(using:<wbr>automaticallyCancelling:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Creates a <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code> to <code>await</code> serialization using the provided <code><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></code>
instance.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">func</span> <span class="n">serializingDownload</span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="p">:</span> <span class="kt"><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></span><span class="o">&gt;</span><span class="p">(</span><span class="n">using</span> <span class="nv">serializer</span><span class="p">:</span> <span class="kt">Serializer</span><span class="p">,</span>
                                                                                <span class="n">automaticallyCancelling</span> <span class="nv">shouldAutomaticallyCancel</span><span class="p">:</span> <span class="kt">Bool</span> <span class="o">=</span> <span class="kc">true</span><span class="p">)</span>
    <span class="o">-&gt;</span> <span class="kt"><a href="../Structs/DownloadTask.html">DownloadTask</a></span><span class="o">&lt;</span><span class="kt">Serializer</span><span class="o">.</span><span class="kt">SerializedObject</span><span class="o">&gt;</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>serializer</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code><a href="../Protocols/DownloadResponseSerializerProtocol.html">DownloadResponseSerializerProtocol</a></code> responsible for serializing the request,
                       response, and data.</p>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td>
                                <code>
                                <em>shouldAutomaticallyCancel</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Bool</code> determining whether or not the request should be cancelled when the
                       enclosing async context is cancelled. Only applies to <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>&lsquo;s async
                       properties. <code>true</code> by default.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The <code><a href="../Structs/DownloadTask.html">DownloadTask</a></code>.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
            <div class="task-group">
              <ul class="item-container">
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC10Validationa"></a>
                    <a name="//apple_ref/swift/Alias/Validation" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC10Validationa">Validation</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>A closure used to validate a request that takes a URL request, a URL response, a temporary URL and a
destination URL, and returns whether the request was valid.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">public</span> <span class="kd">typealias</span> <span class="kt">Validation</span> <span class="o">=</span> <span class="kd">@Sendable</span> <span class="p">(</span><span class="n">_</span> <span class="nv">request</span><span class="p">:</span> <span class="kt">URLRequest</span><span class="p">?,</span>
                                         <span class="n">_</span> <span class="nv">response</span><span class="p">:</span> <span class="kt">HTTPURLResponse</span><span class="p">,</span>
                                         <span class="n">_</span> <span class="nv">fileURL</span><span class="p">:</span> <span class="kt">URL</span><span class="p">?)</span>
    <span class="o">-&gt;</span> <span class="kt">ValidationResult</span></code></pre>

                        </div>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(statusCode:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8validate10statusCodeACXDx_tSTRzSi7ElementRtzlF">validate(statusCode:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">statusCode</span> <span class="nv">acceptableStatusCodes</span><span class="p">:</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">Int</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>acceptableStatusCodes</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p><code>Sequence</code> of acceptable response status codes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The instance.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF"></a>
                    <a name="//apple_ref/swift/Method/validate(contentType:)" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8validate11contentTypeACXDxyXA_tSTRzSS7ElementRtzlF">validate(contentType:<wbr>)</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a <code>Content-Type</code> in the specified sequence.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">@preconcurrency</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="n">validate</span><span class="o">&lt;</span><span class="kt">S</span><span class="o">&gt;</span><span class="p">(</span><span class="n">contentType</span> <span class="nv">acceptableContentTypes</span><span class="p">:</span> <span class="kd">@escaping</span> <span class="kd">@Sendable</span> <span class="kd">@autoclosure</span> <span class="p">()</span> <span class="o">-&gt;</span> <span class="kt">S</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="k">Self</span> <span class="k">where</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sendable</span><span class="p">,</span> <span class="kt">S</span> <span class="p">:</span> <span class="kt">Sequence</span><span class="p">,</span> <span class="kt">S</span><span class="o">.</span><span class="kt">Element</span> <span class="o">==</span> <span class="kt">String</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Parameters</h4>
                        <table class="graybox">
                          <tbody>
                            <tr>
                              <td>
                                <code>
                                <em>contentType</em>
                                </code>
                              </td>
                              <td>
                                <div>
                                  <p>The acceptable content types, which may specify wildcard types and/or subtypes.</p>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
                <li class="item">
                  <div>
                    <code>
                    <a name="/s:9Alamofire15DownloadRequestC8validateACXDyF"></a>
                    <a name="//apple_ref/swift/Method/validate()" class="dashAnchor"></a>
                    <a class="token" href="#/s:9Alamofire15DownloadRequestC8validateACXDyF">validate()</a>
                    </code>
                  </div>
                  <div class="height-container">
                    <div class="pointer-container"></div>
                    <section class="section">
                      <div class="pointer"></div>
                      <div class="abstract">
                        <p>Validates that the response has a status code in the default acceptable range of 200&hellip;299, and that the content
type matches any specified in the Accept HTTP header field.</p>

<p>If validation fails, subsequent calls to response handlers will have an associated error.</p>

                      </div>
                      <div class="declaration">
                        <h4>Declaration</h4>
                        <div class="language">
                          <p class="aside-title">Swift</p>
                          <pre class="highlight swift"><code><span class="kd">@discardableResult</span>
<span class="kd">public</span> <span class="kd">func</span> <span class="nf">validate</span><span class="p">()</span> <span class="o">-&gt;</span> <span class="k">Self</span></code></pre>

                        </div>
                      </div>
                      <div>
                        <h4>Return Value</h4>
                        <p>The request.</p>
                      </div>
                    </section>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </section>

      </article>
    </div>
    <section class="footer">
      <p>&copy; 2024 <a class="link" href="http://alamofire.org/" target="_blank" rel="external noopener">Alamofire Software Foundation</a>. All rights reserved. (Last updated: 2024-11-24)</p>
      <p>Generated by <a class="link" href="https://github.com/realm/jazzy" target="_blank" rel="external noopener">jazzy ♪♫ v0.15.3</a>, a <a class="link" href="https://realm.io" target="_blank" rel="external noopener">Realm</a> project.</p>
    </section>
  </body>
</html>
