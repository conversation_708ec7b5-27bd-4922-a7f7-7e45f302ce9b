# file options

--symlinks ignore
--swiftversion 5.9

# format options

--commas inline
--comments indent
--decimalgrouping 3,5
--exponentcase lowercase
--exponentgrouping disabled
--extensionacl on-declarations
--fractiongrouping disabled
--ifdef no-indent
--importgrouping testable-top
--operatorfunc no-space
--nospaceoperators ..<, ...
--selfrequired validate
--someAny false
--stripunusedargs closure-only
--wraparguments preserve
--wrapcollections preserve
--wrapparameters preserve


# rules

--enable isEmpty
--disable andOperator
--disable opaqueGenericParameters
--disable wrapMultilineStatementBraces
