---
name: Bug Report
about: Create a report to help us improve
---

> ℹ Please fill out this template when filing an issue.
> All lines beginning with an ℹ symbol instruct you with what info we expect. You can delete those lines once you've filled in the info.
>
> Per our [*CONTRIBUTING guidelines](https://github.com/Alamofire/Alamofire/blob/master/CONTRIBUTING.md), we use GitHub for
> bugs and feature requests, not general support. Other issues should be opened on Stack Overflow with the tag `alamofire`.
>
> Please remove this line and everything above it before submitting.

* [ ] I've read, understood, and done my best to follow the [*CONTRIBUTING guidelines](https://github.com/Alamofire/Alamofire/blob/master/CONTRIBUTING.md).

## What did you do?

ℹ Please replace this with what you did.  

## What did you expect to happen?

ℹ Please replace this with what you expected to happen.  

## What happened instead?

ℹ Please replace this with of what happened instead.  

## Alamofire Environment

**Alamofire Version:**
**Dependency Manager:**
**Xcode Version:**
**Swift Version:**
**Platform(s) Running Alamofire:**
**macOS Version Running Xcode:**

## Demo Project

ℹ Please link to or upload a project we can download that reproduces the issue.
