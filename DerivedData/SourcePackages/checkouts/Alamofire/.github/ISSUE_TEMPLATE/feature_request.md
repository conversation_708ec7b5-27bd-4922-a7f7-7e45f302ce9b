---
name: Feature Request
about: Suggest an idea for this project

---

> ℹ Please fill out this template when filing a feature request.
> All lines beginning with an ℹ symbol instruct you with what info we expect. You can delete those lines once you've filled in the info.
>
> Feature requests should code examples whenever possible.
>
> Per our [*CONTRIBUTING guidelines](https://github.com/Alamofire/Alamofire/blob/master/CONTRIBUTING.md), we use GitHub for
> bugs and feature requests, not general support. Other issues should be opened on Stack Overflow with the tag `alamofire`.
>
> Please remove this line and everything above it before submitting.

* [ ] I've read, understood, and done my best to follow the [*CONTRIBUTING guidelines](https://github.com/Alamofire/Alamofire/blob/master/CONTRIBUTING.md).

## Problem

ℹ Please replace this with a summary of the problem you're trying to solve.

## Feature Request

ℹ Please replace this with a summary of the feature you're requesting.

## Value to Alamofire

ℹ Please replace this with a summary of why you think this is something Alamofire should have, rather than it existing in your codebase or as a separate library.
