{"configurations": [{"id": "06BDBD92-3173-4395-90BF-851B80FF1162", "name": "<PERSON><PERSON><PERSON>", "options": {}}], "defaultOptions": {"codeCoverage": false}, "testTargets": [{"skippedTests": ["ClosureAPIConcurrencyTests", "CombineTestCase", "DataRequestCombineTests", "DataRequestConcurrencyTests", "DataStreamConcurrencyTests", "DataStreamRequestCombineTests", "DownloadConcurrencyTests", "DownloadRequestCombineTests", "WebSocketConcurrencyTests", "WebSocketTests"], "target": {"containerPath": "container:Alamofire.xcodeproj", "identifier": "4CF626F71BA7CB3E0011A099", "name": "Alamofire tvOS Tests"}}], "version": 1}