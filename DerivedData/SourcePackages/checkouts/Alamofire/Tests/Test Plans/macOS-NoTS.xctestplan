{"configurations": [{"id": "06BDBD92-3173-4395-90BF-851B80FF1162", "name": "<PERSON><PERSON><PERSON>", "options": {}}], "defaultOptions": {"codeCoverage": false}, "testTargets": [{"skippedTests": ["ClosureAPIConcurrencyTests", "DataRequestConcurrencyTests", "DataStreamConcurrencyTests", "DownloadConcurrencyTests", "WebSocketConcurrencyTests"], "target": {"containerPath": "container:Alamofire.xcodeproj", "identifier": "F829C6B11A7A94F100A2CD59", "name": "Alamofire macOS Tests"}}], "version": 1}