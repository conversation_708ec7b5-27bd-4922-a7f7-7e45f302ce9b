// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		3129D57622F230AE009C145F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 3129D57422F230AE009C145F /* LaunchScreen.storyboard */; };
		31E476821C55DE6D00968569 /* Alamofire.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 31E4766A1C55DD5900968569 /* Alamofire.framework */; };
		31E476831C55DE6D00968569 /* Alamofire.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 31E4766A1C55DD5900968569 /* Alamofire.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		4C6D2C801C67EFE100846168 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C6D2C7D1C67EFE100846168 /* AppDelegate.swift */; };
		4C6D2C811C67EFE100846168 /* DetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C6D2C7E1C67EFE100846168 /* DetailViewController.swift */; };
		4C6D2C821C67EFE100846168 /* MasterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C6D2C7F1C67EFE100846168 /* MasterViewController.swift */; };
		4C6D2C8F1C67EFEC00846168 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 4C6D2C8C1C67EFEC00846168 /* Images.xcassets */; };
		4C6D2C981C67F03B00846168 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4C6D2C961C67F03B00846168 /* Main.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		31AB09122733010700986A70 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 31293065263E17D600473CEA;
			remoteInfo = "Alamofire watchOS Tests";
		};
		31E476691C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F8111E3319A95C8B0040E7D1;
			remoteInfo = "Alamofire iOS";
		};
		31E4766B1C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F8111E3E19A95C8B0040E7D1;
			remoteInfo = "Alamofire iOS Tests";
		};
		31E4766D1C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 4DD67C0B1A5C55C900ED2280;
			remoteInfo = "Alamofire OSX";
		};
		31E4766F1C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F829C6B21A7A94F100A2CD59;
			remoteInfo = "Alamofire OSX Tests";
		};
		31E476711C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 4CF626EF1BA7CB3E0011A099;
			remoteInfo = "Alamofire tvOS";
		};
		31E476731C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 4CF626F81BA7CB3E0011A099;
			remoteInfo = "Alamofire tvOS Tests";
		};
		31E476751C55DD5900968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = E4202FE01B667AA100C997FB;
			remoteInfo = "Alamofire watchOS";
		};
		31E476841C55DE6D00968569 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = F8111E3219A95C8B0040E7D1;
			remoteInfo = "Alamofire iOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		F818D0E519CA8D15006034B1 /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				31E476831C55DE6D00968569 /* Alamofire.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		3129D57522F230AE009C145F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Resources/Base.lproj/LaunchScreen.storyboard; sourceTree = SOURCE_ROOT; };
		31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = Alamofire.xcodeproj; path = ../Alamofire.xcodeproj; sourceTree = "<group>"; };
		4C6D2C7D1C67EFE100846168 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = Source/AppDelegate.swift; sourceTree = SOURCE_ROOT; };
		4C6D2C7E1C67EFE100846168 /* DetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = DetailViewController.swift; path = Source/DetailViewController.swift; sourceTree = SOURCE_ROOT; };
		4C6D2C7F1C67EFE100846168 /* MasterViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; name = MasterViewController.swift; path = Source/MasterViewController.swift; sourceTree = SOURCE_ROOT; };
		4C6D2C8C1C67EFEC00846168 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Resources/Images.xcassets; sourceTree = SOURCE_ROOT; };
		4C6D2C8D1C67EFEC00846168 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Resources/Info.plist; sourceTree = SOURCE_ROOT; };
		4C6D2C971C67F03B00846168 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Main.storyboard; sourceTree = "<group>"; };
		F8111E0519A951050040E7D1 /* iOS Example.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "iOS Example.app"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F8111E0219A951050040E7D1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				31E476821C55DE6D00968569 /* Alamofire.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		31E476601C55DD5900968569 /* Products */ = {
			isa = PBXGroup;
			children = (
				31E4766A1C55DD5900968569 /* Alamofire.framework */,
				31E4766C1C55DD5900968569 /* Alamofire iOS Tests.xctest */,
				31E4766E1C55DD5900968569 /* Alamofire.framework */,
				31E476701C55DD5900968569 /* Alamofire macOS Tests.xctest */,
				31E476721C55DD5900968569 /* Alamofire.framework */,
				31E476741C55DD5900968569 /* Alamofire tvOS Tests.xctest */,
				31E476761C55DD5900968569 /* Alamofire.framework */,
				31AB09132733010700986A70 /* Alamofire watchOS Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		4C6D2C951C67F03B00846168 /* Base.lproj */ = {
			isa = PBXGroup;
			children = (
				3129D57422F230AE009C145F /* LaunchScreen.storyboard */,
				4C6D2C961C67F03B00846168 /* Main.storyboard */,
			);
			name = Base.lproj;
			path = Resources/Base.lproj;
			sourceTree = SOURCE_ROOT;
		};
		F8111DFC19A951050040E7D1 = {
			isa = PBXGroup;
			children = (
				F8111E0719A951050040E7D1 /* Source */,
				F8111E0619A951050040E7D1 /* Products */,
				31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */,
			);
			indentWidth = 4;
			sourceTree = "<group>";
			tabWidth = 4;
			usesTabs = 0;
		};
		F8111E0619A951050040E7D1 /* Products */ = {
			isa = PBXGroup;
			children = (
				F8111E0519A951050040E7D1 /* iOS Example.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F8111E0719A951050040E7D1 /* Source */ = {
			isa = PBXGroup;
			children = (
				4C6D2C7D1C67EFE100846168 /* AppDelegate.swift */,
				4C6D2C7E1C67EFE100846168 /* DetailViewController.swift */,
				4C6D2C7F1C67EFE100846168 /* MasterViewController.swift */,
				F8111E0819A951050040E7D1 /* Supporting Files */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		F8111E0819A951050040E7D1 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				4C6D2C8D1C67EFEC00846168 /* Info.plist */,
				4C6D2C8C1C67EFEC00846168 /* Images.xcassets */,
				4C6D2C951C67F03B00846168 /* Base.lproj */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F8111E0419A951050040E7D1 /* iOS Example */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8111E2319A951050040E7D1 /* Build configuration list for PBXNativeTarget "iOS Example" */;
			buildPhases = (
				F8111E0119A951050040E7D1 /* Sources */,
				F8111E0219A951050040E7D1 /* Frameworks */,
				F8111E0319A951050040E7D1 /* Resources */,
				F818D0E519CA8D15006034B1 /* Copy Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				31E476851C55DE6D00968569 /* PBXTargetDependency */,
			);
			name = "iOS Example";
			productName = Alamofire;
			productReference = F8111E0519A951050040E7D1 /* iOS Example.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F8111DFD19A951050040E7D1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0720;
				LastUpgradeCheck = 1400;
				ORGANIZATIONNAME = Alamofire;
				TargetAttributes = {
					F8111E0419A951050040E7D1 = {
						CreatedOnToolsVersion = 6.0;
						LastSwiftMigration = 0900;
					};
				};
			};
			buildConfigurationList = F8111E0019A951050040E7D1 /* Build configuration list for PBXProject "iOS Example" */;
			compatibilityVersion = "Xcode 10.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F8111DFC19A951050040E7D1;
			productRefGroup = F8111E0619A951050040E7D1 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 31E476601C55DD5900968569 /* Products */;
					ProjectRef = 31E4765F1C55DD5900968569 /* Alamofire.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				F8111E0419A951050040E7D1 /* iOS Example */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		31AB09132733010700986A70 /* Alamofire watchOS Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = "Alamofire watchOS Tests.xctest";
			remoteRef = 31AB09122733010700986A70 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E4766A1C55DD5900968569 /* Alamofire.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Alamofire.framework;
			remoteRef = 31E476691C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E4766C1C55DD5900968569 /* Alamofire iOS Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = "Alamofire iOS Tests.xctest";
			remoteRef = 31E4766B1C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E4766E1C55DD5900968569 /* Alamofire.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Alamofire.framework;
			remoteRef = 31E4766D1C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E476701C55DD5900968569 /* Alamofire macOS Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = "Alamofire macOS Tests.xctest";
			remoteRef = 31E4766F1C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E476721C55DD5900968569 /* Alamofire.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Alamofire.framework;
			remoteRef = 31E476711C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E476741C55DD5900968569 /* Alamofire tvOS Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = "Alamofire tvOS Tests.xctest";
			remoteRef = 31E476731C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		31E476761C55DD5900968569 /* Alamofire.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = Alamofire.framework;
			remoteRef = 31E476751C55DD5900968569 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		F8111E0319A951050040E7D1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3129D57622F230AE009C145F /* LaunchScreen.storyboard in Resources */,
				4C6D2C8F1C67EFEC00846168 /* Images.xcassets in Resources */,
				4C6D2C981C67F03B00846168 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F8111E0119A951050040E7D1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4C6D2C801C67EFE100846168 /* AppDelegate.swift in Sources */,
				4C6D2C821C67EFE100846168 /* MasterViewController.swift in Sources */,
				4C6D2C811C67EFE100846168 /* DetailViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		31E476851C55DE6D00968569 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = "Alamofire iOS";
			targetProxy = 31E476841C55DE6D00968569 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		3129D57422F230AE009C145F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				3129D57522F230AE009C145F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		4C6D2C961C67F03B00846168 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				4C6D2C971C67F03B00846168 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		F8111E2119A951050040E7D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Off;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TVOS_DEPLOYMENT_TARGET = 10.0;
				WATCHOS_DEPLOYMENT_TARGET = 3.0;
			};
			name = Debug;
		};
		F8111E2219A951050040E7D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_SWIFT3_OBJC_INFERENCE = Off;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TVOS_DEPLOYMENT_TARGET = 10.0;
				VALIDATE_PRODUCT = YES;
				WATCHOS_DEPLOYMENT_TARGET = 3.0;
			};
			name = Release;
		};
		F8111E2419A951050040E7D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				INFOPLIST_FILE = Resources/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.iOS-Example";
				PRODUCT_NAME = "iOS Example";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F8111E2519A951050040E7D1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				INFOPLIST_FILE = Resources/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.alamofire.iOS-Example";
				PRODUCT_NAME = "iOS Example";
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F8111E0019A951050040E7D1 /* Build configuration list for PBXProject "iOS Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8111E2119A951050040E7D1 /* Debug */,
				F8111E2219A951050040E7D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8111E2319A951050040E7D1 /* Build configuration list for PBXNativeTarget "iOS Example" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8111E2419A951050040E7D1 /* Debug */,
				F8111E2519A951050040E7D1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F8111DFD19A951050040E7D1 /* Project object */;
}
