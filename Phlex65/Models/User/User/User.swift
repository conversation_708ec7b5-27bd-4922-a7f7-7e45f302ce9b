//
//  User.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - User Model
struct User: Codable, Identifiable, Equatable {
    let id: String
    let email: String
    let firstName: String
    let lastName: String
    let phoneNumber: String?
    let dateOfBirth: Date?
    let gender: Gender?
    let profileImageURL: String?
    let address: Address?
    let isEmailVerified: Bool
    let isPhoneVerified: Bool
    let createdAt: Date
    let updatedAt: Date
    
    // MARK: - Coding Keys
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case firstName
        case lastName
        case phoneNumber
        case dateOfBirth
        case gender
        case profileImageURL = "profileImageUrl"
        case address
        case isEmailVerified
        case isPhoneVerified
        case createdAt
        case updatedAt
    }
    
    // MARK: - Computed Properties
    var fullName: String {
        return "\(firstName) \(lastName)"
    }
    
    var initials: String {
        let firstInitial = firstName.first?.uppercased() ?? ""
        let lastInitial = lastName.first?.uppercased() ?? ""
        return "\(firstInitial)\(lastInitial)"
    }
    
    var isProfileComplete: Bool {
        return !firstName.isEmpty &&
               !lastName.isEmpty &&
               phoneNumber != nil &&
               dateOfBirth != nil &&
               gender != nil
    }
}
