//
//  Address.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Address Model
struct Address: Codable, Equatable {
    let street: String
    let city: String
    let state: String
    let zipCode: String
    let country: String
    
    // MARK: - Computed Properties
    var formattedAddress: String {
        return "\(street), \(city), \(state) \(zipCode), \(country)"
    }
    
    var shortAddress: String {
        return "\(city), \(state)"
    }
}
