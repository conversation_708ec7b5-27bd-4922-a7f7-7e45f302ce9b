//
//  VerificationResponse.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Verification Response
struct VerificationResponse: Codable {
    let success: Bool
    let message: String?
    
    // For authentication verification (signup/login)
    let user: User?
    let accessToken: String?
    let refreshToken: String?
    let expiresIn: Int?
    
    // For password reset verification
    let resetToken: String?
    
    // MARK: - Coding Keys
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case user
        case accessToken
        case refreshToken
        case expiresIn
        case resetToken
    }
    
    // MARK: - Computed Properties
    var isAuthenticationResponse: Bool {
        return user != nil && accessToken != nil && refreshToken != nil && expiresIn != nil
    }
    
    var isResetTokenResponse: Bool {
        return resetToken != nil
    }
    
    // MARK: - Convert to AuthenticationResponse
    func toAuthenticationResponse() -> AuthenticationResponse? {
        guard let user = user,
              let accessToken = accessToken,
              let refreshToken = refreshToken,
              let expiresIn = expiresIn else {
            return nil
        }

        return AuthenticationResponse(
            user: user,
            accessToken: accessToken,
            refreshToken: refreshToken,
            expiresIn: expiresIn
        )
    }
}
