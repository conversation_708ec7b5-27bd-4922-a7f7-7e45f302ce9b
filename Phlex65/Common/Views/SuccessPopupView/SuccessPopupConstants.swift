//
//  SuccessPopupConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Success Popup Constants (EXACT Figma Specifications)
struct SuccessPopupConstants {
    
    // MARK: - Layout Constants (EXACT Figma: CSS Properties)
    struct Layout {
        // Popup container (EXACT Figma: "width": "343px", "padding": "24px")
        static let popupWidth: CGFloat = 343.0 // EXACT: 343px
        static let popupPadding: CGFloat = 24.0 // EXACT: 24px
        static let popupCornerRadius: CGFloat = 16.0 // EXACT: "border-radius": "16px"
        
        // Spacing (EXACT Figma: "gap": "24px", "gap": "8px")
        static let mainSpacing: CGFloat = 24.0 // EXACT: "gap": "24px"
        static let textSpacing: CGFloat = 8.0 // EXACT: "gap": "8px"
        
        // Icon container (EXACT Figma: "width": "80px", "height": "80px")
        static let iconContainerSize: CGFloat = 80.0 // EXACT: 80x80
        static let iconBorderWidth: CGFloat = 12.0 // EXACT: "border": "12px solid"
        static let iconSize: CGFloat = 28.0 // Equivalent to 56px icon in 80px container
        
        // Button (EXACT Figma: "height": "48px", "border-radius": "50px")
        static let buttonHeight: CGFloat = 48.0 // EXACT: 48px
        static let buttonCornerRadius: CGFloat = 50.0 // EXACT: 50px
        
        // Shadow (EXACT Figma: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.06)")
        static let shadowRadius: CGFloat = 1.0
        static let shadowOffsetX: CGFloat = 0.0
        static let shadowOffsetY: CGFloat = 1.0
        
        // Button shadow (EXACT Figma: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)")
        static let buttonShadowRadius: CGFloat = 1.0
        static let buttonShadowOffsetX: CGFloat = 0.0
        static let buttonShadowOffsetY: CGFloat = 1.0
    }
    
    // MARK: - Colors (EXACT Figma: CSS Variables)
    struct Colors {
        // Popup colors (EXACT Figma)
        static let popupBackground = Color(hex: "#FFFFFF") // EXACT: "var(--Base-White, #FFF)"
        static let popupBorder = Color(hex: "#DFE1E7") // EXACT: "var(--Greyscale-100, #DFE1E7)"
        static let popupShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.06) // EXACT: rgba(16, 24, 40, 0.06)
        
        // Icon colors (EXACT Figma)
        static let iconBackground = Color(hex: "#89C226") // EXACT: "var(--Primary-Primary-500, #89C226)"
        static let iconBorder = Color(hex: "#F2F8EA") // EXACT: "var(--Primary-Primary-25, #F2F8EA)"
        static let iconColor = Color.white // EXACT: White
        
        // Button colors (EXACT Figma)
        static let buttonBackground = Color(hex: "#89C226") // EXACT: "var(--Primary-Primary-500, #89C226)"
        static let buttonShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.05) // EXACT: rgba(16, 24, 40, 0.05)
    }
    
    // MARK: - Typography (EXACT Figma: Font Properties)
    struct Typography {
        // Title (EXACT Figma: "font-family": "Inter", "font-size": "18px", "font-weight": "600")
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 18.0 // EXACT: 18px
        static let titleColor = Color(hex: "#0D0D12") // EXACT: "var(--Greyscale-900, #0D0D12)"
        
        // Description (EXACT Figma: "font-family": "Inter", "font-size": "14px", "font-weight": "400")
        static let descriptionFontName = "Inter-Regular"
        static let descriptionSize: CGFloat = 14.0 // EXACT: 14px
        static let descriptionColor = Color(hex: "#818898") // EXACT: "var(--Greyscale-400, #818898)"
        static let descriptionLineSpacing: CGFloat = 6.0 // EXACT: line-height 20px (20-14=6)
        
        // Button text (EXACT Figma: "font-family": "Inter", "font-size": "16px", "font-weight": "500")
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0 // EXACT: 16px
        static let buttonTextColor = Color.white // EXACT: "var(--White, #FFF)"
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let springResponse: Double = 0.3
        static let springDamping: Double = 0.8
        static let scaleFrom: CGFloat = 0.8
        static let scaleTo: CGFloat = 1.0
        static let opacityFrom: Double = 0.0
        static let opacityTo: Double = 1.0
    }
    
    // MARK: - Background Overlay Constants
    struct Overlay {
        static let backgroundOpacity: Double = 0.4
        static let backgroundColor = Color.black
    }
}
