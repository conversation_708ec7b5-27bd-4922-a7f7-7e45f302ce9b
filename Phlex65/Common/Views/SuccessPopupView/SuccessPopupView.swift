//
//  SuccessPopupView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Success Popup View
struct SuccessPopupView: View {
    
    // MARK: - Properties
    let title: String
    let message: String
    let buttonText: String
    let onButtonTap: () -> Void
    let onDismiss: (() -> Void)?
    
    // MARK: - State
    @State private var isVisible: Bool = false
    @State private var scale: CGFloat = 0.8
    @State private var opacity: Double = 0.0
    
    // MARK: - Initialization
    init(
        title: String,
        message: String,
        buttonText: String,
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.buttonText = buttonText
        self.onButtonTap = onButtonTap
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background overlay
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .opacity(opacity)
                .onTapGesture {
                    onDismiss?()
                }
            
            // Popup card
            VStack(spacing: 24) {
                // Success Icon
                ZStack {
                    Circle()
                        .fill(Color(hex: "#8BC34A").opacity(0.1))
                        .frame(width: 80, height: 80)
                    
                    Image(systemName: "checkmark")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(Color(hex: "#8BC34A"))
                }
                .scaleEffect(isVisible ? 1.0 : 0.5)
                .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(0.1), value: isVisible)
                
                VStack(spacing: 12) {
                    // Title
                    Text(title)
                        .font(.custom("Inter-SemiBold", size: 20))
                        .fontWeight(.semibold)
                        .foregroundColor(Color(hex: "#1F2937"))
                        .multilineTextAlignment(.center)
                    
                    // Message
                    Text(message)
                        .font(.custom("Inter-Regular", size: 16))
                        .fontWeight(.regular)
                        .foregroundColor(Color(hex: "#6B7280"))
                        .multilineTextAlignment(.center)
                        .lineLimit(nil)
                        .fixedSize(horizontal: false, vertical: true)
                }
                .opacity(isVisible ? 1.0 : 0.0)
                .offset(y: isVisible ? 0 : 10)
                .animation(.easeOut(duration: 0.4).delay(0.2), value: isVisible)
                
                // Button
                Button(action: onButtonTap) {
                    Text(buttonText)
                        .font(.custom("Inter-SemiBold", size: 16))
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 56)
                        .background(Color(hex: "#8BC34A"))
                        .cornerRadius(100)
                }
                .opacity(isVisible ? 1.0 : 0.0)
                .offset(y: isVisible ? 0 : 10)
                .animation(.easeOut(duration: 0.4).delay(0.3), value: isVisible)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white)
                    .shadow(color: Color.black.opacity(0.1), radius: 20, x: 0, y: 10)
            )
            .padding(.horizontal, 24)
            .scaleEffect(scale)
            .opacity(opacity)
        }
        .onAppear {
            withAnimation(.easeOut(duration: 0.3)) {
                opacity = 1.0
                scale = 1.0
            }
            
            // Delay the content animation slightly
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isVisible = true
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        SuccessPopupView(
            title: "Password Reset Successful",
            message: "Your password has been reset successfully.\nYou can now log in with your new password",
            buttonText: "Go to Login",
            onButtonTap: {
                print("Go to Login tapped")
            },
            onDismiss: {
                print("Popup dismissed")
            }
        )
    }
}
