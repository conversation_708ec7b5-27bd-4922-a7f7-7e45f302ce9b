//
//  SuccessPopupView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Success Popup View (Generic Reusable Component - EXACT Figma Design)
struct SuccessPopupView: View {

    // MARK: - Properties
    let title: String
    let message: String
    let buttonText: String
    let onButtonTap: () -> Void
    let onDismiss: (() -> Void)?

    // MARK: - State
    @State private var isVisible: Bool = false

    // MARK: - Initialization
    init(
        title: String,
        message: String,
        buttonText: String,
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.buttonText = buttonText
        self.onButtonTap = onButtonTap
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background Overlay (EXACT Figma: Semi-transparent background)
            SuccessPopupConstants.Overlay.backgroundColor
                .opacity(SuccessPopupConstants.Overlay.backgroundOpacity)
                .ignoresSafeArea()
                .onTapGesture {
                    onDismiss?()
                }

            // Success Popup Container (EXACT Figma Design)
            VStack(spacing: 0) {
                successPopupContent
            }
            .frame(width: SuccessPopupConstants.Layout.popupWidth) // EXACT: 343px
            .background(SuccessPopupConstants.Colors.popupBackground)
            .cornerRadius(SuccessPopupConstants.Layout.popupCornerRadius) // EXACT: 16px
            .overlay(
                RoundedRectangle(cornerRadius: SuccessPopupConstants.Layout.popupCornerRadius)
                    .stroke(SuccessPopupConstants.Colors.popupBorder, lineWidth: 1)
            )
            .shadow(
                color: SuccessPopupConstants.Colors.popupShadow,
                radius: SuccessPopupConstants.Layout.shadowRadius,
                x: SuccessPopupConstants.Layout.shadowOffsetX,
                y: SuccessPopupConstants.Layout.shadowOffsetY
            )
            .scaleEffect(isVisible ? SuccessPopupConstants.Animation.scaleTo : SuccessPopupConstants.Animation.scaleFrom)
            .opacity(isVisible ? SuccessPopupConstants.Animation.opacityTo : SuccessPopupConstants.Animation.opacityFrom)
            .animation(
                .spring(
                    response: SuccessPopupConstants.Animation.springResponse,
                    dampingFraction: SuccessPopupConstants.Animation.springDamping
                ),
                value: isVisible
            )
        }
        .onAppear {
            withAnimation {
                isVisible = true
            }
        }
    }

    // MARK: - Success Popup Content (EXACT Figma Design)
    private var successPopupContent: some View {
        VStack(spacing: SuccessPopupConstants.Layout.mainSpacing) { // EXACT: 24px
            // Success Icon Section
            successIconSection

            // Text Content Section
            textContentSection

            // Button Section
            buttonSection
        }
        .padding(SuccessPopupConstants.Layout.popupPadding) // EXACT: 24px
    }

    // MARK: - Success Icon Section (EXACT Figma Design)
    private var successIconSection: some View {
        ZStack {
            // Icon Background Circle (EXACT Figma: 80x80, border-radius 80px, border 12px)
            Circle()
                .fill(SuccessPopupConstants.Colors.iconBackground) // EXACT: #89C226
                .frame(
                    width: SuccessPopupConstants.Layout.iconContainerSize,
                    height: SuccessPopupConstants.Layout.iconContainerSize
                ) // EXACT: 80x80
                .overlay(
                    Circle()
                        .stroke(
                            SuccessPopupConstants.Colors.iconBorder, // EXACT: #F2F8EA
                            lineWidth: SuccessPopupConstants.Layout.iconBorderWidth // EXACT: 12px
                        )
                )

            // Success Check Icon (EXACT Figma: 56x56)
            Image(systemName: "checkmark")
                .font(.system(size: SuccessPopupConstants.Layout.iconSize, weight: .bold)) // EXACT: 56px equivalent
                .foregroundColor(SuccessPopupConstants.Colors.iconColor) // EXACT: White
        }
    }

    // MARK: - Text Content Section (EXACT Figma Design)
    private var textContentSection: some View {
        VStack(spacing: SuccessPopupConstants.Layout.textSpacing) { // EXACT: 8px
            // Title (EXACT Figma: Inter 18px/600 weight)
            Text(title)
                .font(.custom(SuccessPopupConstants.Typography.titleFontName, size: SuccessPopupConstants.Typography.titleSize))
                .fontWeight(.semibold) // EXACT: 600 weight
                .foregroundColor(SuccessPopupConstants.Typography.titleColor) // EXACT: #0D0D12
                .multilineTextAlignment(.center)
                .lineLimit(nil)

            // Description (EXACT Figma: Inter 14px/400 weight, line-height 20px)
            Text(message)
                .font(.custom(SuccessPopupConstants.Typography.descriptionFontName, size: SuccessPopupConstants.Typography.descriptionSize))
                .fontWeight(.regular) // EXACT: 400 weight
                .foregroundColor(SuccessPopupConstants.Typography.descriptionColor) // EXACT: #818898
                .multilineTextAlignment(.center)
                .lineSpacing(SuccessPopupConstants.Typography.descriptionLineSpacing) // EXACT: 20px line-height
                .lineLimit(nil)
        }
    }

    // MARK: - Button Section (EXACT Figma Design)
    private var buttonSection: some View {
        Button(action: onButtonTap) {
            Text(buttonText)
                .font(.custom(SuccessPopupConstants.Typography.buttonFontName, size: SuccessPopupConstants.Typography.buttonSize))
                .fontWeight(.medium) // EXACT: 500 weight
                .foregroundColor(SuccessPopupConstants.Typography.buttonTextColor) // EXACT: White
                .frame(maxWidth: .infinity)
                .frame(height: SuccessPopupConstants.Layout.buttonHeight) // EXACT: 48px
                .background(SuccessPopupConstants.Colors.buttonBackground) // EXACT: #89C226
                .cornerRadius(SuccessPopupConstants.Layout.buttonCornerRadius) // EXACT: 50px
                .shadow(
                    color: SuccessPopupConstants.Colors.buttonShadow,
                    radius: SuccessPopupConstants.Layout.buttonShadowRadius,
                    x: SuccessPopupConstants.Layout.buttonShadowOffsetX,
                    y: SuccessPopupConstants.Layout.buttonShadowOffsetY
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()

        SuccessPopupView(
            title: "Password Reset Successful",
            message: "Your password has been reset successfully. You can now log in with your new password",
            buttonText: "Go to Login",
            onButtonTap: {
                print("Go to Login tapped")
            },
            onDismiss: {
                print("Popup dismissed")
            }
        )
    }
}
