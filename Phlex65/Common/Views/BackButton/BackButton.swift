//
//  BackButton.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Back Button
struct BackButton: View {
    
    // MARK: - Properties
    let action: () -> Void
    
    // MARK: - State
    @State private var isPressed: Bool = false
    
    // MARK: - Initialization
    init(action: @escaping () -> Void) {
        self.action = action
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: action) {
            // Use custom SVG back button from Figma - 24x24px
            Image("BackButton")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)
                .foregroundColor(ForgotPasswordConstants.Colors.backButtonIcon)
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .animation(.easeInOut(duration: ForgotPasswordConstants.Animation.backButtonPressDuration), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.backButtonIdentifier)
        .accessibilityLabel("Back")
        .accessibilityHint("Go back to previous screen")
    }
    
    // MARK: - Computed Properties
    private var backgroundColor: Color {
        if isPressed {
            return ForgotPasswordConstants.Colors.backButtonBackgroundPressed
        } else {
            return ForgotPasswordConstants.Colors.backButtonBackground
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        BackButton(action: {
            print("Back button tapped")
        })
        
        HStack {
            BackButton(action: {})
            Spacer()
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    .padding()
}
