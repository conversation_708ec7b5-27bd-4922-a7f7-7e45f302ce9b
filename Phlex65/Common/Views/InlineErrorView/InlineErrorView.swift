//
//  InlineErrorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Inline Error View
struct InlineErrorView: View {

    // MARK: - Properties
    let message: String
    let showIcon: Bool

    // MARK: - Initialization
    init(message: String, showIcon: Bool = true) {
        self.message = message
        self.showIcon = showIcon
    }

    // MARK: - Body
    var body: some View {
        HStack(spacing: AppTheme.Spacing.sm) {
            if showIcon {
                Image(systemName: AppConstants.exclamationCircleFillImage)
                    .font(.caption)
                    .foregroundColor(AppTheme.Colors.error)
            }

            Text(message)
                .font(AppTheme.Typography.caption)
                .foregroundColor(AppTheme.Colors.error)
                .multilineTextAlignment(.leading)

            Spacer()
        }
        .padding(.horizontal, AppTheme.Spacing.sm)
        .padding(.vertical, AppTheme.Spacing.xs)
        .background(
            AppTheme.Colors.error.opacity(0.1)
        )
        .cornerRadius(AppTheme.CornerRadius.sm)
        .overlay(
            RoundedRectangle(cornerRadius: AppTheme.CornerRadius.sm)
                .stroke(AppTheme.Colors.error.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Preview
#Preview {
    VStack {
        InlineErrorView(message: requiredFieldMessage)
        InlineErrorView(message: invalidEmailMessage, showIcon: false)
    }
    .padding()
}
