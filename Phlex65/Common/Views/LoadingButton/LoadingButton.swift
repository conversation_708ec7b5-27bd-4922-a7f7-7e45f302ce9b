//
//  LoadingButton.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Loading Button Style
enum LoadingButtonStyle {
    case primary
    case secondary
    case outline
}

// MARK: - Loading Button
struct LoadingButton: View {
    
    // MARK: - Properties
    let title: String
    let isLoading: Bool
    let style: LoadingButtonStyle
    let action: () -> Void
    
    // MARK: - Initialization
    init(
        title: String,
        isLoading: Bool = false,
        style: LoadingButtonStyle = .primary,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.style = style
        self.action = action
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: action) {
            HStack(spacing: AppTheme.Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: textColor))
                }
                
                Text(title)
                    .font(AppTheme.Typography.labelLarge.weight(.semibold))
                    .foregroundColor(textColor)
                    .opacity(isLoading ? 0.7 : 1.0)
            }
            .frame(height: 44)
            .frame(maxWidth: .infinity)
            .background(backgroundColor)
            .cornerRadius(AppTheme.CornerRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.CornerRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
        }
        .disabled(isLoading)
        .animation(AppTheme.Animation.fast, value: isLoading)
    }
    
    // MARK: - Computed Properties
    private var textColor: Color {
        switch style {
        case .primary:
            return AppTheme.Colors.textInverse
        case .secondary, .outline:
            return AppTheme.Colors.textPrimary
        }
    }
    
    private var backgroundColor: Color {
        switch style {
        case .primary:
            return AppTheme.Colors.primary
        case .secondary:
            return AppTheme.Colors.backgroundSecondary
        case .outline:
            return Color.clear
        }
    }
    
    private var borderColor: Color {
        switch style {
        case .primary, .secondary:
            return Color.clear
        case .outline:
            return AppTheme.Colors.border
        }
    }
    
    private var borderWidth: CGFloat {
        switch style {
        case .primary, .secondary:
            return 0
        case .outline:
            return 1
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        LoadingButton(title: saveText, isLoading: false) {}
        LoadingButton(title: loadingText, isLoading: true) {}
        LoadingButton(title: cancelText, style: .secondary) {}
        LoadingButton(title: retryText, style: .outline) {}
    }
    .padding()
}
