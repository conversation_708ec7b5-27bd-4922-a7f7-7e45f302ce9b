//
//  EmptyStateView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Empty State View
struct EmptyStateView: View {

    // MARK: - Properties
    let title: String
    let message: String
    let systemImage: String
    let actionTitle: String?
    let action: (() -> Void)?

    // MARK: - Initialization
    init(
        title: String,
        message: String,
        systemImage: String = AppConstants.trayImage,
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.systemImage = systemImage
        self.actionTitle = actionTitle
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        VStack(spacing: AppTheme.Spacing.xl) {
            VStack(spacing: AppTheme.Spacing.lg) {
                // Empty State Icon
                Image(systemName: systemImage)
                    .font(.system(size: 64))
                    .foregroundColor(AppTheme.Colors.textSecondary)

                // Title and Message
                VStack(spacing: AppTheme.Spacing.sm) {
                    Text(title)
                        .font(AppTheme.Typography.headlineMedium)
                        .foregroundColor(AppTheme.Colors.textPrimary)
                        .multilineTextAlignment(.center)

                    Text(message)
                        .font(AppTheme.Typography.bodyMedium)
                        .foregroundColor(AppTheme.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }

            // Action Button (if provided)
            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle) {
                    action()
                }
                .buttonStyle(PrimaryButtonStyle())
                .padding(.horizontal, AppTheme.Spacing.xl)
            }
        }
        .padding(AppTheme.Spacing.xl)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Preview
#Preview {
    EmptyStateView(
        title: noItemsFoundTitle,
        message: noItemsFoundMessage,
        systemImage: AppConstants.trayImage,
        actionTitle: retryText,
        action: {}
    )
}
