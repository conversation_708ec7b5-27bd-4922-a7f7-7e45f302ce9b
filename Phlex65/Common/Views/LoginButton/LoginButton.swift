//
//  LoginButton.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Login Button
struct LoginButton: View {
    
    // MARK: - Properties
    let title: String
    let isLoading: Bool
    let isEnabled: Bool
    let action: () -> Void
    
    // MARK: - State
    @State private var isPressed: Bool = false
    
    // MARK: - Initialization
    init(
        title: String,
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.isLoading = isLoading
        self.isEnabled = isEnabled
        self.action = action
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: {
            if isEnabled && !isLoading {
                action()
            }
        }) {
            HStack(spacing: 8) {
                // Loading Indicator
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: LoginConstants.Colors.primaryButtonText))
                        .scaleEffect(0.8)
                }
                
                // Button Text
                Text(title)
                    .font(.custom(LoginConstants.Typography.buttonFontName, size: LoginConstants.Typography.buttonSize))
                    .fontWeight(LoginConstants.Typography.buttonWeight)
                    .foregroundColor(textColor)
                    .opacity(isLoading ? 0.7 : 1.0)
            }
            .frame(maxWidth: .infinity)
            .frame(height: LoginConstants.Layout.buttonHeight)
            .background(backgroundColor)
            .cornerRadius(LoginConstants.Layout.buttonCornerRadius)
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: LoginConstants.Animation.buttonPressDuration), value: isPressed)
            .disabled(!isEnabled || isLoading)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityIdentifier(LoginConstants.Accessibility.signInButtonIdentifier)
        .accessibilityLabel(title)
        .accessibilityHint(isLoading ? "Signing in, please wait" : "Tap to sign in")
    }
    
    // MARK: - Computed Properties
    private var backgroundColor: Color {
        if !isEnabled {
            return LoginConstants.Colors.primaryButtonBackground.opacity(0.5)
        } else if isPressed {
            return LoginConstants.Colors.primaryButtonBackgroundPressed
        } else {
            return LoginConstants.Colors.primaryButtonBackground
        }
    }
    
    private var textColor: Color {
        return LoginConstants.Colors.primaryButtonText
    }
}

// MARK: - Convenience Initializers
extension LoginButton {
    
    // Sign In Button
    static func signIn(
        isLoading: Bool = false,
        isEnabled: Bool = true,
        action: @escaping () -> Void
    ) -> LoginButton {
        LoginButton(
            title: signInText,
            isLoading: isLoading,
            isEnabled: isEnabled,
            action: action
        )
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        // Normal state
        LoginButton.signIn(
            isLoading: false,
            isEnabled: true,
            action: {}
        )
        
        // Loading state
        LoginButton.signIn(
            isLoading: true,
            isEnabled: true,
            action: {}
        )
        
        // Disabled state
        LoginButton.signIn(
            isLoading: false,
            isEnabled: false,
            action: {}
        )
    }
    .padding()
}
