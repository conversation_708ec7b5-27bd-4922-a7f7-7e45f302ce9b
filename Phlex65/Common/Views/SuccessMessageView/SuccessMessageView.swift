//
//  SuccessMessageView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Success Message View
struct SuccessMessageView: View {
    
    // MARK: - Properties
    let title: String
    let message: String
    let buttonText: String
    let onButtonTap: () -> Void
    
    // MARK: - State
    @State private var isVisible: Bool = false
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 24) {
            // Success Icon
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 64))
                .foregroundColor(ForgotPasswordConstants.Colors.primaryButtonBackground)
                .scaleEffect(isVisible ? 1.0 : 0.5)
                .opacity(isVisible ? 1.0 : 0.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: isVisible)
            
            VStack(spacing: 12) {
                // Title
                Text(title)
                    .font(.custom(ForgotPasswordConstants.Typography.titleFontName, size: 24))
                    .fontWeight(.semibold)
                    .foregroundColor(ForgotPasswordConstants.Typography.titleColor)
                    .multilineTextAlignment(.center)
                
                // Message
                Text(message)
                    .font(.custom(ForgotPasswordConstants.Typography.subtitleFontName, size: ForgotPasswordConstants.Typography.subtitleSize))
                    .fontWeight(ForgotPasswordConstants.Typography.subtitleWeight)
                    .foregroundColor(ForgotPasswordConstants.Typography.subtitleColor)
                    .multilineTextAlignment(.center)
                    .lineLimit(nil)
            }
            .opacity(isVisible ? 1.0 : 0.0)
            .offset(y: isVisible ? 0 : 20)
            .animation(.easeOut(duration: 0.5).delay(0.2), value: isVisible)
            
            // Button
            LoginButton(
                title: buttonText,
                isLoading: false,
                isEnabled: true,
                action: onButtonTap
            )
            .opacity(isVisible ? 1.0 : 0.0)
            .offset(y: isVisible ? 0 : 20)
            .animation(.easeOut(duration: 0.5).delay(0.4), value: isVisible)
        }
        .padding(.horizontal, ForgotPasswordConstants.Layout.screenPadding)
        .onAppear {
            withAnimation {
                isVisible = true
            }
        }
    }
}

// MARK: - Preview
#Preview {
    SuccessMessageView(
        title: resetLinkSentTitle,
        message: resetLinkSentMessage,
        buttonText: backToLoginText,
        onButtonTap: {
            print("Button tapped")
        }
    )
}
