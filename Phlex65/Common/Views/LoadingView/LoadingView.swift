//
//  LoadingView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Loading View
struct LoadingView: View {

    // MARK: - Properties
    let message: String
    let showBackground: Bool

    // MARK: - Initialization
    init(
        message: String = loadingText,
        showBackground: Bool = true
    ) {
        self.message = message
        self.showBackground = showBackground
    }

    // MARK: - Body
    var body: some View {
        VStack(spacing: AppTheme.Spacing.lg) {
            // Loading Indicator
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: AppTheme.Colors.primary))

            // Loading Message
            Text(message)
                .font(AppTheme.Typography.bodyMedium)
                .foregroundColor(AppTheme.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(AppTheme.Spacing.xl)
        .background(
            showBackground ? AppTheme.Colors.background : Color.clear
        )
        .cornerRadius(showBackground ? AppTheme.CornerRadius.lg : 0)
        .shadow(
            color: showBackground ? AppTheme.Shadow.medium.color : Color.clear,
            radius: showBackground ? AppTheme.Shadow.medium.radius : 0,
            x: showBackground ? AppTheme.Shadow.medium.x : 0,
            y: showBackground ? AppTheme.Shadow.medium.y : 0
        )
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        LoadingView()
        LoadingView(message: "Custom loading message", showBackground: false)
    }
    .padding()
}
