//
//  CustomTextField.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Custom Text Field Style
enum CustomTextFieldStyle {
    case email
    case password
    case standard
    
    var keyboardType: UIKeyboardType {
        switch self {
        case .email:
            return .emailAddress
        case .password, .standard:
            return .default
        }
    }
    
    var textContentType: UITextContentType? {
        switch self {
        case .email:
            return .emailAddress
        case .password:
            return .password
        case .standard:
            return nil
        }
    }
    
    var autocapitalization: UITextAutocapitalizationType {
        switch self {
        case .email:
            return .none
        case .password:
            return .none
        case .standard:
            return .sentences
        }
    }
}

// MARK: - Custom Text Field
struct CustomTextField: View {
    
    // MARK: - Properties
    let label: String
    let placeholder: String
    @Binding var text: String
    @Binding var isFocused: Bool
    let style: CustomTextFieldStyle
    let errorMessage: String?
    let isSecure: Bool
    let showPasswordToggle: Bool
    @Binding var showPassword: Bool
    
    // MARK: - Callbacks
    let onTextChanged: ((String) -> Void)?
    let onFocusChanged: ((Bool) -> Void)?
    let onSubmit: (() -> Void)?
    
    // MARK: - State
    @FocusState private var isTextFieldFocused: Bool
    
    // MARK: - Initialization
    init(
        label: String,
        placeholder: String,
        text: Binding<String>,
        isFocused: Binding<Bool>,
        style: CustomTextFieldStyle = .standard,
        errorMessage: String? = nil,
        isSecure: Bool = false,
        showPasswordToggle: Bool = false,
        showPassword: Binding<Bool> = .constant(false),
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) {
        self.label = label
        self.placeholder = placeholder
        self._text = text
        self._isFocused = isFocused
        self.style = style
        self.errorMessage = errorMessage
        self.isSecure = isSecure
        self.showPasswordToggle = showPasswordToggle
        self._showPassword = showPassword
        self.onTextChanged = onTextChanged
        self.onFocusChanged = onFocusChanged
        self.onSubmit = onSubmit
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.labelToFieldSpacing) {
            // Label (only show if not empty)
            if !label.isEmpty {
                Text(label)
                    .font(SignupConstants.Step1.Typography.labelFont)
                    .foregroundColor(SignupConstants.Step1.Colors.labelColor)
            }

            // Text Field Container - Figma: height 48px, border-radius 10px, padding 8px 12px
            ZStack {
                // Background
                RoundedRectangle(cornerRadius: SignupConstants.Step1.Layout.fieldCornerRadius)
                    .fill(SignupConstants.Step1.Colors.fieldBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: SignupConstants.Step1.Layout.fieldCornerRadius)
                            .stroke(borderColor, lineWidth: 1)
                    )

                // Text Field
                HStack {
                    textFieldContent
                        .font(SignupConstants.Step1.Typography.placeholderFont)
                        .foregroundColor(text.isEmpty ? SignupConstants.Step1.Colors.placeholderColor : SignupConstants.Step1.Colors.labelColor)
                        .keyboardType(style.keyboardType)
                        .textContentType(style.textContentType)
                        .disableAutocorrection(style == .email || style == .password)
                        .focused($isTextFieldFocused)
                        .onChange(of: text) { newValue in
                            onTextChanged?(newValue)
                        }
                        .onChange(of: isTextFieldFocused) { newValue in
                            isFocused = newValue
                            onFocusChanged?(newValue)
                        }
                        .onSubmit {
                            onSubmit?()
                        }

                    // Password Toggle Button
                    if showPasswordToggle {
                        Button(action: {
                            showPassword.toggle()
                        }) {
                            Image(systemName: showPassword ? "eye.slash" : "eye")
                                .foregroundColor(SignupConstants.Step1.Colors.placeholderColor)
                                .frame(width: 24, height: 24)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, SignupConstants.Step1.Layout.fieldPaddingHorizontal)
                .padding(.vertical, SignupConstants.Step1.Layout.fieldPaddingVertical)
            }
            .frame(height: SignupConstants.Step1.Layout.fieldHeight)
            .animation(.easeInOut(duration: 0.2), value: isFocused)

            // Error Message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.custom("Inter", size: 12))
                    .fontWeight(.regular)
                    .foregroundColor(.red)
                    .transition(.opacity)
            }
        }
        .onAppear {
            // Sync focus state
            isTextFieldFocused = isFocused
        }
        .onChange(of: isFocused) { newValue in
            isTextFieldFocused = newValue
        }
    }
    
    // MARK: - Computed Properties
    private var borderColor: Color {
        if let _ = errorMessage {
            return .red
        } else if isFocused {
            return SignupConstants.Step1.Colors.progressActive
        } else {
            return SignupConstants.Step1.Colors.fieldBorder
        }
    }
    
    @ViewBuilder
    private var textFieldContent: some View {
        if isSecure && !showPassword {
            SecureField(placeholder, text: $text)
        } else {
            TextField(placeholder, text: $text)
        }
    }
}

// MARK: - Convenience Initializers
extension CustomTextField {

    // Text Field
    static func text(
        text: Binding<String>,
        placeholder: String,
        isFocused: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        return CustomTextField(
            label: "",
            placeholder: placeholder,
            text: text,
            isFocused: isFocused,
            style: .standard,
            errorMessage: errorMessage,
            isSecure: false,
            showPasswordToggle: false,
            showPassword: .constant(false),
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }

    // Email Field
    static func email(
        text: Binding<String>,
        isFocused: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        CustomTextField(
            label: emailAddressLabel,
            placeholder: emailPlaceholder,
            text: text,
            isFocused: isFocused,
            style: .email,
            errorMessage: errorMessage,
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }
    
    // Password Field
    static func password(
        text: Binding<String>,
        isFocused: Binding<Bool>,
        showPassword: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        CustomTextField(
            label: passwordLabel,
            placeholder: passwordPlaceholder,
            text: text,
            isFocused: isFocused,
            style: .password,
            errorMessage: errorMessage,
            isSecure: true,
            showPasswordToggle: true,
            showPassword: showPassword,
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }

    // Phone Field
    static func phone(
        text: Binding<String>,
        isFocused: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        return CustomTextField(
            label: "Phone Number",
            placeholder: "Enter Phone Number",
            text: text,
            isFocused: isFocused,
            style: .standard,
            errorMessage: errorMessage,
            isSecure: false,
            showPasswordToggle: false,
            showPassword: .constant(false),
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }

    // MARK: - Fields WITHOUT Labels (for Signup screens)

    // Text Field WITHOUT Label
    static func textWithoutLabel(
        text: Binding<String>,
        placeholder: String,
        isFocused: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        return CustomTextField(
            label: "", // NO LABEL
            placeholder: placeholder,
            text: text,
            isFocused: isFocused,
            style: .standard,
            errorMessage: errorMessage,
            isSecure: false,
            showPasswordToggle: false,
            showPassword: .constant(false),
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }

    // Email Field WITHOUT Label
    static func emailWithoutLabel(
        text: Binding<String>,
        placeholder: String,
        isFocused: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        CustomTextField(
            label: "", // NO LABEL
            placeholder: placeholder,
            text: text,
            isFocused: isFocused,
            style: .email,
            errorMessage: errorMessage,
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }

    // Password Field WITHOUT Label
    static func passwordWithoutLabel(
        text: Binding<String>,
        placeholder: String,
        isFocused: Binding<Bool>,
        showPassword: Binding<Bool>,
        errorMessage: String? = nil,
        onTextChanged: ((String) -> Void)? = nil,
        onFocusChanged: ((Bool) -> Void)? = nil,
        onSubmit: (() -> Void)? = nil
    ) -> CustomTextField {
        CustomTextField(
            label: "", // NO LABEL
            placeholder: placeholder,
            text: text,
            isFocused: isFocused,
            style: .password,
            errorMessage: errorMessage,
            isSecure: true,
            showPasswordToggle: true,
            showPassword: showPassword,
            onTextChanged: onTextChanged,
            onFocusChanged: onFocusChanged,
            onSubmit: onSubmit
        )
    }
}

// MARK: - Constants for CustomTextField
private extension CustomTextField {
    static let emailAddressLabel = "Email Address"
    static let emailPlaceholder = "Enter Email Address"
    static let passwordLabel = "Password"
    static let passwordPlaceholder = "****************"
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        CustomTextField.email(
            text: .constant("<EMAIL>"),
            isFocused: .constant(false),
            errorMessage: nil
        )

        CustomTextField.password(
            text: .constant("password123"),
            isFocused: .constant(false),
            showPassword: .constant(false),
            errorMessage: "Password is too short"
        )

        CustomTextField.phone(
            text: .constant("****** 567 8900"),
            isFocused: .constant(false),
            errorMessage: nil
        )
    }
    .padding()
}
