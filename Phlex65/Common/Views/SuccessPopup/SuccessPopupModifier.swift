import SwiftUI

// MARK: - Success Popup View Modifier
struct SuccessPopupModifier: ViewModifier {
    @Binding var isPresented: Bool
    let title: String
    let description: String
    let buttonText: String
    let onButtonTap: () -> Void
    let onDismiss: (() -> Void)?
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Group {
                    if isPresented {
                        SuccessPopup(
                            title: title,
                            description: description,
                            buttonText: buttonText,
                            onButtonTap: {
                                onButtonTap()
                                isPresented = false
                            },
                            onDismiss: {
                                onDismiss?()
                                isPresented = false
                            }
                        )
                        .transition(.opacity)
                        .zIndex(999)
                    }
                }
            )
    }
}

// MARK: - View Extension for Easy Usage
extension View {
    /// Shows a success popup with customizable title, description, and button text
    /// - Parameters:
    ///   - isPresented: Binding to control popup visibility
    ///   - title: The main title text (e.g., "Success!", "Password Reset Successful")
    ///   - description: The description text explaining what happened
    ///   - buttonText: The button text (default: "Continue")
    ///   - onButtonTap: Action to perform when button is tapped
    ///   - onDismiss: Optional action to perform when popup is dismissed by tapping background
    func successPopup(
        isPresented: Binding<Bool>,
        title: String,
        description: String,
        buttonText: String = "Continue",
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> some View {
        self.modifier(
            SuccessPopupModifier(
                isPresented: isPresented,
                title: title,
                description: description,
                buttonText: buttonText,
                onButtonTap: onButtonTap,
                onDismiss: onDismiss
            )
        )
    }
}

// MARK: - Predefined Success Popup Configurations
extension SuccessPopup {
    
    // MARK: - Password Reset Success
    static func passwordResetSuccess(
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> SuccessPopup {
        SuccessPopup(
            title: "Password Reset Successful",
            description: "Your password has been reset successfully. You can now log in with your new password",
            buttonText: "Go to Login",
            onButtonTap: onButtonTap,
            onDismiss: onDismiss
        )
    }
    
    // MARK: - Account Created Success
    static func accountCreatedSuccess(
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> SuccessPopup {
        SuccessPopup(
            title: "Account Created Successfully",
            description: "Your account has been created successfully. Welcome to Phlex65!",
            buttonText: "Get Started",
            onButtonTap: onButtonTap,
            onDismiss: onDismiss
        )
    }
    
    // MARK: - Email Verified Success
    static func emailVerifiedSuccess(
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> SuccessPopup {
        SuccessPopup(
            title: "Email Verified Successfully",
            description: "Your email has been verified successfully. You can now access all features.",
            buttonText: "Continue",
            onButtonTap: onButtonTap,
            onDismiss: onDismiss
        )
    }
    
    // MARK: - Profile Updated Success
    static func profileUpdatedSuccess(
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> SuccessPopup {
        SuccessPopup(
            title: "Profile Updated Successfully",
            description: "Your profile information has been updated successfully.",
            buttonText: "Done",
            onButtonTap: onButtonTap,
            onDismiss: onDismiss
        )
    }
    
    // MARK: - Generic Success
    static func genericSuccess(
        title: String = "Success!",
        description: String = "Operation completed successfully.",
        buttonText: String = "Continue",
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) -> SuccessPopup {
        SuccessPopup(
            title: title,
            description: description,
            buttonText: buttonText,
            onButtonTap: onButtonTap,
            onDismiss: onDismiss
        )
    }
}

// MARK: - Usage Examples in Comments
/*
 
 // EXAMPLE 1: Using the view modifier
 struct MyView: View {
     @State private var showSuccessPopup = false
     
     var body: some View {
         VStack {
             Button("Show Success") {
                 showSuccessPopup = true
             }
         }
         .successPopup(
             isPresented: $showSuccessPopup,
             title: "Success!",
             description: "Your action was completed successfully.",
             buttonText: "Continue",
             onButtonTap: {
                 // Handle button tap
                 print("Success button tapped")
             },
             onDismiss: {
                 // Handle dismiss (optional)
                 print("Popup dismissed")
             }
         )
     }
 }
 
 // EXAMPLE 2: Using predefined configurations
 struct ResetPasswordView: View {
     @State private var showSuccessPopup = false
     
     var body: some View {
         VStack {
             // Your reset password form
         }
         .overlay(
             Group {
                 if showSuccessPopup {
                     SuccessPopup.passwordResetSuccess(
                         onButtonTap: {
                             // Navigate to login
                             navigateToLogin()
                         }
                     )
                 }
             }
         )
     }
 }
 
 // EXAMPLE 3: Using the component directly
 struct CustomView: View {
     var body: some View {
         ZStack {
             // Your main content
             
             SuccessPopup(
                 title: "Custom Success",
                 description: "This is a custom success message.",
                 buttonText: "Got it",
                 onButtonTap: {
                     // Handle action
                 }
             )
         }
     }
 }
 
 */
