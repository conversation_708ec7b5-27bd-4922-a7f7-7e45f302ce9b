import SwiftUI

// MARK: - Success Popup (Generic Reusable Component)
struct SuccessPopup: View {
    // MARK: - Properties
    let title: String
    let description: String
    let buttonText: String
    let onButtonTap: () -> Void
    let onDismiss: (() -> Void)?
    
    @State private var isVisible = false
    
    // MARK: - Initializer
    init(
        title: String,
        description: String,
        buttonText: String = "Continue",
        onButtonTap: @escaping () -> Void,
        onDismiss: (() -> Void)? = nil
    ) {
        self.title = title
        self.description = description
        self.buttonText = buttonText
        self.onButtonTap = onButtonTap
        self.onDismiss = onDismiss
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background Overlay (EXACT Figma: Semi-transparent background)
            Color.black.opacity(0.4)
                .ignoresSafeArea()
                .onTapGesture {
                    onDismiss?()
                }
            
            // Success Popup Container (EXACT Figma Design)
            VStack(spacing: 0) {
                successPopupContent
            }
            .frame(width: SuccessPopupConstants.Layout.popupWidth) // EXACT: 343px
            .background(SuccessPopupConstants.Colors.popupBackground)
            .cornerRadius(SuccessPopupConstants.Layout.popupCornerRadius) // EXACT: 16px
            .overlay(
                RoundedRectangle(cornerRadius: SuccessPopupConstants.Layout.popupCornerRadius)
                    .stroke(SuccessPopupConstants.Colors.popupBorder, lineWidth: 1)
            )
            .shadow(
                color: SuccessPopupConstants.Colors.popupShadow,
                radius: SuccessPopupConstants.Layout.shadowRadius,
                x: SuccessPopupConstants.Layout.shadowOffsetX,
                y: SuccessPopupConstants.Layout.shadowOffsetY
            )
            .scaleEffect(isVisible ? 1.0 : 0.8)
            .opacity(isVisible ? 1.0 : 0.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isVisible)
        }
        .onAppear {
            withAnimation {
                isVisible = true
            }
        }
    }
    
    // MARK: - Success Popup Content (EXACT Figma Design)
    private var successPopupContent: some View {
        VStack(spacing: SuccessPopupConstants.Layout.mainSpacing) { // EXACT: 24px
            // Success Icon Section
            successIconSection
            
            // Text Content Section
            textContentSection
            
            // Button Section
            buttonSection
        }
        .padding(SuccessPopupConstants.Layout.popupPadding) // EXACT: 24px
    }
    
    // MARK: - Success Icon Section (EXACT Figma Design)
    private var successIconSection: some View {
        ZStack {
            // Icon Background Circle (EXACT Figma: 80x80, border-radius 80px, border 12px)
            Circle()
                .fill(SuccessPopupConstants.Colors.iconBackground) // EXACT: #89C226
                .frame(
                    width: SuccessPopupConstants.Layout.iconContainerSize,
                    height: SuccessPopupConstants.Layout.iconContainerSize
                ) // EXACT: 80x80
                .overlay(
                    Circle()
                        .stroke(
                            SuccessPopupConstants.Colors.iconBorder, // EXACT: #F2F8EA
                            lineWidth: SuccessPopupConstants.Layout.iconBorderWidth // EXACT: 12px
                        )
                )
            
            // Success Check Icon (EXACT Figma: 56x56)
            Image(systemName: "checkmark")
                .font(.system(size: SuccessPopupConstants.Layout.iconSize, weight: .bold)) // EXACT: 56px equivalent
                .foregroundColor(SuccessPopupConstants.Colors.iconColor) // EXACT: White
        }
    }
    
    // MARK: - Text Content Section (EXACT Figma Design)
    private var textContentSection: some View {
        VStack(spacing: SuccessPopupConstants.Layout.textSpacing) { // EXACT: 8px
            // Title (EXACT Figma: Inter 18px/600 weight)
            Text(title)
                .font(.custom(SuccessPopupConstants.Typography.titleFontName, size: SuccessPopupConstants.Typography.titleSize))
                .fontWeight(.semibold) // EXACT: 600 weight
                .foregroundColor(SuccessPopupConstants.Typography.titleColor) // EXACT: #0D0D12
                .multilineTextAlignment(.center)
                .lineLimit(nil)
            
            // Description (EXACT Figma: Inter 14px/400 weight, line-height 20px)
            Text(description)
                .font(.custom(SuccessPopupConstants.Typography.descriptionFontName, size: SuccessPopupConstants.Typography.descriptionSize))
                .fontWeight(.regular) // EXACT: 400 weight
                .foregroundColor(SuccessPopupConstants.Typography.descriptionColor) // EXACT: #818898
                .multilineTextAlignment(.center)
                .lineSpacing(SuccessPopupConstants.Typography.descriptionLineSpacing) // EXACT: 20px line-height
                .lineLimit(nil)
        }
    }
    
    // MARK: - Button Section (EXACT Figma Design)
    private var buttonSection: some View {
        Button(action: onButtonTap) {
            Text(buttonText)
                .font(.custom(SuccessPopupConstants.Typography.buttonFontName, size: SuccessPopupConstants.Typography.buttonSize))
                .fontWeight(.medium) // EXACT: 500 weight
                .foregroundColor(SuccessPopupConstants.Typography.buttonTextColor) // EXACT: White
                .frame(maxWidth: .infinity)
                .frame(height: SuccessPopupConstants.Layout.buttonHeight) // EXACT: 48px
                .background(SuccessPopupConstants.Colors.buttonBackground) // EXACT: #89C226
                .cornerRadius(SuccessPopupConstants.Layout.buttonCornerRadius) // EXACT: 50px
                .shadow(
                    color: SuccessPopupConstants.Colors.buttonShadow,
                    radius: SuccessPopupConstants.Layout.buttonShadowRadius,
                    x: SuccessPopupConstants.Layout.buttonShadowOffsetX,
                    y: SuccessPopupConstants.Layout.buttonShadowOffsetY
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Success Popup Constants (EXACT Figma Specifications)
struct SuccessPopupConstants {
    
    // MARK: - Layout Constants (EXACT Figma: CSS Properties)
    struct Layout {
        // Popup container (EXACT Figma: "width": "343px", "padding": "24px")
        static let popupWidth: CGFloat = 343.0 // EXACT: 343px
        static let popupPadding: CGFloat = 24.0 // EXACT: 24px
        static let popupCornerRadius: CGFloat = 16.0 // EXACT: "border-radius": "16px"
        
        // Spacing (EXACT Figma: "gap": "24px", "gap": "8px")
        static let mainSpacing: CGFloat = 24.0 // EXACT: "gap": "24px"
        static let textSpacing: CGFloat = 8.0 // EXACT: "gap": "8px"
        
        // Icon container (EXACT Figma: "width": "80px", "height": "80px")
        static let iconContainerSize: CGFloat = 80.0 // EXACT: 80x80
        static let iconBorderWidth: CGFloat = 12.0 // EXACT: "border": "12px solid"
        static let iconSize: CGFloat = 28.0 // Equivalent to 56px icon in 80px container
        
        // Button (EXACT Figma: "height": "48px", "border-radius": "50px")
        static let buttonHeight: CGFloat = 48.0 // EXACT: 48px
        static let buttonCornerRadius: CGFloat = 50.0 // EXACT: 50px
        
        // Shadow (EXACT Figma: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.06)")
        static let shadowRadius: CGFloat = 1.0
        static let shadowOffsetX: CGFloat = 0.0
        static let shadowOffsetY: CGFloat = 1.0
        
        // Button shadow (EXACT Figma: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)")
        static let buttonShadowRadius: CGFloat = 1.0
        static let buttonShadowOffsetX: CGFloat = 0.0
        static let buttonShadowOffsetY: CGFloat = 1.0
    }
    
    // MARK: - Colors (EXACT Figma: CSS Variables)
    struct Colors {
        // Popup colors (EXACT Figma)
        static let popupBackground = Color(hex: "#FFFFFF") // EXACT: "var(--Base-White, #FFF)"
        static let popupBorder = Color(hex: "#DFE1E7") // EXACT: "var(--Greyscale-100, #DFE1E7)"
        static let popupShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.06) // EXACT: rgba(16, 24, 40, 0.06)
        
        // Icon colors (EXACT Figma)
        static let iconBackground = Color(hex: "#89C226") // EXACT: "var(--Primary-Primary-500, #89C226)"
        static let iconBorder = Color(hex: "#F2F8EA") // EXACT: "var(--Primary-Primary-25, #F2F8EA)"
        static let iconColor = Color.white // EXACT: White
        
        // Button colors (EXACT Figma)
        static let buttonBackground = Color(hex: "#89C226") // EXACT: "var(--Primary-Primary-500, #89C226)"
        static let buttonShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.05) // EXACT: rgba(16, 24, 40, 0.05)
    }
    
    // MARK: - Typography (EXACT Figma: Font Properties)
    struct Typography {
        // Title (EXACT Figma: "font-family": "Inter", "font-size": "18px", "font-weight": "600")
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 18.0 // EXACT: 18px
        static let titleColor = Color(hex: "#0D0D12") // EXACT: "var(--Greyscale-900, #0D0D12)"
        
        // Description (EXACT Figma: "font-family": "Inter", "font-size": "14px", "font-weight": "400")
        static let descriptionFontName = "Inter-Regular"
        static let descriptionSize: CGFloat = 14.0 // EXACT: 14px
        static let descriptionColor = Color(hex: "#818898") // EXACT: "var(--Greyscale-400, #818898)"
        static let descriptionLineSpacing: CGFloat = 6.0 // EXACT: line-height 20px (20-14=6)
        
        // Button text (EXACT Figma: "font-family": "Inter", "font-size": "16px", "font-weight": "500")
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0 // EXACT: 16px
        static let buttonTextColor = Color.white // EXACT: "var(--White, #FFF)"
    }
}



// MARK: - Preview
#Preview {
    SuccessPopup(
        title: "Password Reset Successful",
        description: "Your password has been reset successfully. You can now log in with your new password",
        buttonText: "Go to Login",
        onButtonTap: {
            print("Button tapped")
        },
        onDismiss: {
            print("Popup dismissed")
        }
    )
}
