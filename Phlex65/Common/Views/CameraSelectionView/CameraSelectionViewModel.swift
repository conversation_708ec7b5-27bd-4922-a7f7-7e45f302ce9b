//
//  CameraSelectionViewModel.swift
//  Phlex65
//
//  Created by Augment Agent on 18/06/2025.
//

import SwiftUI
import UIKit

// MARK: - Camera Selection View Model
@MainActor
class CameraSelectionViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var showImagePicker = false
    @Published var imagePickerSourceType: UIImagePickerController.SourceType = .camera
    @Published var showPermissionAlert = false
    @Published var permissionAlertMessage = ""
    
    // MARK: - Private Properties
    private let cameraService: CameraServiceProtocol
    
    // MARK: - Callbacks
    var onImageSelected: ((UIImage) -> Void)?
    var onDismiss: (() -> Void)?
    var onDeleteImage: (() -> Void)?
    
    // MARK: - Initialization
    init(cameraService: CameraServiceProtocol = CameraService.shared) {
        self.cameraService = cameraService
    }
    
    // MARK: - Public Methods
    func cameraButtonTapped() {
        Task {
            await handleCameraSelection()
        }
    }
    
    func galleryButtonTapped() {
        Task {
            await handleGallerySelection()
        }
    }
    
    func closeButtonTapped() {
        onDismiss?()
    }
    
    func deleteButtonTapped() {
        onDeleteImage?()
        onDismiss?()
    }
    
    func imagePickerImageSelected(_ image: UIImage) {
        onImageSelected?(image)
        showImagePicker = false
        onDismiss?()
    }
    
    func imagePickerCancelled() {
        showImagePicker = false
    }
    
    // MARK: - Private Methods
    private func handleCameraSelection() async {
        isLoading = true
        
        let hasPermission = await cameraService.requestCameraPermission()
        
        isLoading = false
        
        if hasPermission {
            imagePickerSourceType = .camera
            showImagePicker = true
        } else {
            showCameraPermissionAlert()
        }
    }
    
    private func handleGallerySelection() async {
        isLoading = true
        
        let hasPermission = await cameraService.requestPhotoLibraryPermission()
        
        isLoading = false
        
        if hasPermission {
            imagePickerSourceType = .photoLibrary
            showImagePicker = true
        } else {
            showPhotoLibraryPermissionAlert()
        }
    }
    
    private func showCameraPermissionAlert() {
        permissionAlertMessage = "\(PermissionsConfiguration.Descriptions.camera) Please enable camera access in Settings."
        showPermissionAlert = true
    }

    private func showPhotoLibraryPermissionAlert() {
        permissionAlertMessage = "\(PermissionsConfiguration.Descriptions.photoLibrary) Please enable photo access in Settings."
        showPermissionAlert = true
    }
}
