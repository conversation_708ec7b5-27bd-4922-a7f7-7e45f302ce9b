//
//  CameraSelectionView.swift
//  Phlex65
//
//  Created by Augment Agent on 18/06/2025.
//

import SwiftUI
import UIKit

// MARK: - Camera Selection View (EXACT Figma JSON Implementation)
struct CameraSelectionView: View {
    
    // MARK: - Properties
    @StateObject private var viewModel = CameraSelectionViewModel()
    
    // Callbacks
    let onImageSelected: (UIImage) -> Void
    let onDeleteImage: (() -> Void)?
    let onDismiss: () -> Void
    let showDeleteOption: Bool
    
    // MARK: - Initialization
    init(
        onImageSelected: @escaping (UIImage) -> Void,
        onDismiss: @escaping () -> Void,
        onDeleteImage: (() -> Void)? = nil,
        showDeleteOption: Bool = false
    ) {
        self.onImageSelected = onImageSelected
        self.onDismiss = onDismiss
        self.onDeleteImage = onDeleteImage
        self.showDeleteOption = showDeleteOption
    }
    
    // MARK: - Body (EXACT Figma JSON Structure)
    var body: some View {
        // Container: width: 390px, padding: 10px 16px 36px 16px, background: #FFF
        VStack(alignment: .center, spacing: CameraSelectionConstants.Layout.containerGap) {
            // Tab indicator: width: 50px, height: 5px, border-radius: 100px, background: #5B616E
            tabIndicator
            
            // Top Bar: padding: 10px 0px, justify-content: space-between
            topBar
            
            // Icon Group: gap: 32px
            iconGroup
        }
        .padding(.horizontal, CameraSelectionConstants.Layout.containerPaddingHorizontal)
        .padding(.top, CameraSelectionConstants.Layout.containerPaddingTop)
        .padding(.bottom, CameraSelectionConstants.Layout.containerPaddingBottom)
        .background(CameraSelectionConstants.Colors.backgroundColor)
        .accessibilityIdentifier(CameraSelectionConstants.Accessibility.containerIdentifier)
        .onAppear {
            setupCallbacks()
        }
        .sheet(isPresented: $viewModel.showImagePicker) {
            ImagePicker(
                sourceType: viewModel.imagePickerSourceType,
                onImagePicked: viewModel.imagePickerImageSelected,
                onCancel: viewModel.imagePickerCancelled
            )
        }
        .alert("Permission Required", isPresented: $viewModel.showPermissionAlert) {
            Button("Settings") {
                openSettings()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text(viewModel.permissionAlertMessage)
        }
    }
    
    // MARK: - Tab Indicator (EXACT Figma JSON)
    private var tabIndicator: some View {
        RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.tabCornerRadius)
            .fill(CameraSelectionConstants.Colors.tabColor)
            .frame(
                width: CameraSelectionConstants.Layout.tabWidth,
                height: CameraSelectionConstants.Layout.tabHeight
            )
    }
    
    // MARK: - Top Bar (EXACT Figma JSON)
    private var topBar: some View {
        HStack {
            // Close Icon Container: width: 48px, height: 48px, padding: 10px, border-radius: 9999px
            Button(action: viewModel.closeButtonTapped) {
                RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.closeIconContainerCornerRadius)
                    .fill(CameraSelectionConstants.Colors.iconContainerBackground)
                    .frame(
                        width: CameraSelectionConstants.Layout.closeIconContainerSize,
                        height: CameraSelectionConstants.Layout.closeIconContainerSize
                    )
                    .overlay(
                        Image(systemName: "xmark")
                            .font(.system(size: CameraSelectionConstants.Layout.closeIconSize * 0.6, weight: .medium))
                            .foregroundColor(.black)
                    )
            }
            .accessibilityIdentifier(CameraSelectionConstants.Accessibility.closeButtonIdentifier)
            
            Spacer()
            
            // Title: Inter, 20px, 600 weight, 30px line-height, color: #101219
            Text(CameraSelectionConstants.Text.title)
                .font(CameraSelectionConstants.Typography.titleFont)
                .foregroundColor(CameraSelectionConstants.Colors.titleColor)
                .lineLimit(1)
            
            Spacer()
            
            // Delete Icon Container (conditional): width: 40px, height: 40px, padding: 8.333px
            if showDeleteOption {
                Button(action: viewModel.deleteButtonTapped) {
                    RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.deleteIconContainerCornerRadius)
                        .fill(CameraSelectionConstants.Colors.iconContainerBackground)
                        .frame(
                            width: CameraSelectionConstants.Layout.deleteIconContainerSize,
                            height: CameraSelectionConstants.Layout.deleteIconContainerSize
                        )
                        .overlay(
                            Image(systemName: "trash")
                                .font(.system(size: CameraSelectionConstants.Layout.deleteIconSize * 0.6, weight: .medium))
                                .foregroundColor(.red)
                        )
                }
                .accessibilityIdentifier(CameraSelectionConstants.Accessibility.deleteButtonIdentifier)
            } else {
                // Placeholder to maintain spacing
                RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.deleteIconContainerCornerRadius)
                    .fill(Color.clear)
                    .frame(
                        width: CameraSelectionConstants.Layout.deleteIconContainerSize,
                        height: CameraSelectionConstants.Layout.deleteIconContainerSize
                    )
            }
        }
        .padding(.vertical, CameraSelectionConstants.Layout.topBarPaddingVertical)
        .padding(.horizontal, CameraSelectionConstants.Layout.topBarPaddingHorizontal)
    }

    // MARK: - Icon Group (EXACT Figma JSON)
    private var iconGroup: some View {
        HStack(alignment: .center, spacing: CameraSelectionConstants.Layout.iconGroupGap) {
            // Camera Icon
            cameraIconButton

            // Gallery Icon
            galleryIconButton

            Spacer() // Push icons to the left
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Camera Icon Button (EXACT Figma JSON)
    private var cameraIconButton: some View {
        VStack(alignment: .center, spacing: CameraSelectionConstants.Layout.iconItemGap) {
            // Button: height: 64px, padding: 16px, border-radius: 9999px, background: #0292D9, border: #196AA6
            Button(action: viewModel.cameraButtonTapped) {
                RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.buttonCornerRadius)
                    .fill(CameraSelectionConstants.Colors.buttonBackground)
                    .frame(
                        width: CameraSelectionConstants.Layout.buttonSize,
                        height: CameraSelectionConstants.Layout.buttonSize
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.buttonCornerRadius)
                            .stroke(CameraSelectionConstants.Colors.buttonBorder, lineWidth: 0.5)
                    )
                    .overlay(
                        Image("CameraIcon")
                            .resizable()
                            .frame(width: CameraSelectionConstants.Layout.buttonIconSize * 0.75, height: CameraSelectionConstants.Layout.buttonIconSize * 0.75)
                            .foregroundColor(CameraSelectionConstants.Colors.buttonIconColor)
                    )
            }
            .disabled(viewModel.isLoading)
            .accessibilityIdentifier(CameraSelectionConstants.Accessibility.cameraButtonIdentifier)

            // Label: Inter, 14px, 500 weight, 20px line-height, color: #25272C
            Text(CameraSelectionConstants.Text.cameraLabel)
                .font(CameraSelectionConstants.Typography.labelFont)
                .foregroundColor(CameraSelectionConstants.Colors.labelColor)
                .lineLimit(1)
        }
        .frame(
            width: CameraSelectionConstants.Layout.iconContainerWidth,
            height: CameraSelectionConstants.Layout.iconContainerHeight
        )
    }

    // MARK: - Gallery Icon Button (EXACT Figma JSON)
    private var galleryIconButton: some View {
        VStack(alignment: .center, spacing: CameraSelectionConstants.Layout.iconItemGap) {
            // Button: height: 64px, padding: 16px, border-radius: 9999px, background: #0292D9, border: #196AA6
            Button(action: viewModel.galleryButtonTapped) {
                RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.buttonCornerRadius)
                    .fill(CameraSelectionConstants.Colors.buttonBackground)
                    .frame(
                        width: CameraSelectionConstants.Layout.buttonSize,
                        height: CameraSelectionConstants.Layout.buttonSize
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: CameraSelectionConstants.Layout.buttonCornerRadius)
                            .stroke(CameraSelectionConstants.Colors.buttonBorder, lineWidth: 0.5)
                    )
                    .overlay(
                        Image("GalleryIcon")
                            .resizable()
                            .frame(width: CameraSelectionConstants.Layout.buttonIconSize * 0.75, height: CameraSelectionConstants.Layout.buttonIconSize * 0.75)
                            .foregroundColor(CameraSelectionConstants.Colors.buttonIconColor)
                    )
            }
            .disabled(viewModel.isLoading)
            .accessibilityIdentifier(CameraSelectionConstants.Accessibility.galleryButtonIdentifier)

            // Label: Inter, 14px, 500 weight, 20px line-height, color: #25272C
            Text(CameraSelectionConstants.Text.galleryLabel)
                .font(CameraSelectionConstants.Typography.labelFont)
                .foregroundColor(CameraSelectionConstants.Colors.labelColor)
                .lineLimit(1)
        }
        .frame(
            width: CameraSelectionConstants.Layout.iconContainerWidth,
            height: CameraSelectionConstants.Layout.iconContainerHeight
        )
    }

    // MARK: - Private Methods
    private func setupCallbacks() {
        viewModel.onImageSelected = onImageSelected
        viewModel.onDeleteImage = onDeleteImage
        viewModel.onDismiss = onDismiss
    }

    private func openSettings() {
        if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(settingsUrl)
        }
    }
}
