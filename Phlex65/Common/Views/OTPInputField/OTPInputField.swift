//
//  OTPInputField.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - OTP Input Field
struct OTPInputField: View {
    
    // MARK: - Properties
    @Binding var code: String
    @Binding var isError: Bool
    let codeLength: Int
    let onCodeComplete: (String) -> Void
    
    // MARK: - State
    @State private var focusedIndex: Int? = 0
    @State private var digits: [String]
    @FocusState private var isFieldFocused: Bool
    
    // MARK: - Initialization
    init(
        code: Binding<String>,
        isError: Binding<Bool> = .constant(false),
        codeLength: Int = VerifyCodeConstants.Layout.codeLength,
        onCodeComplete: @escaping (String) -> Void
    ) {
        self._code = code
        self._isError = isError
        self.codeLength = codeLength
        self.onCodeComplete = onCodeComplete
        self._digits = State(initialValue: Array(repeating: "", count: codeLength))
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // Code Input Fields
            HStack(spacing: VerifyCodeConstants.Layout.codeFieldSpacing) {
                ForEach(0..<codeLength, id: \.self) { index in
                    codeField(for: index)
                }
            }
            
            // Hidden TextField for keyboard input
            TextField("", text: $code)
                .keyboardType(.numberPad)
                .textContentType(.oneTimeCode)
                .focused($isFieldFocused)
                .opacity(0)
                .frame(height: 0)
                .onChange(of: code) { newValue in
                    handleCodeChange(newValue)
                }
                .onChange(of: isFieldFocused) { focused in
                    if focused {
                        focusedIndex = digits.firstIndex(where: { $0.isEmpty }) ?? 0
                    }
                }
        }
        .onTapGesture {
            isFieldFocused = true
        }
        .onAppear {
            // Auto-focus on appear
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isFieldFocused = true
            }
        }
    }
    
    // MARK: - Code Field
    private func codeField(for index: Int) -> some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: VerifyCodeConstants.Layout.codeFieldCornerRadius)
                .fill(VerifyCodeConstants.Colors.codeFieldBackground)
                .frame(
                    width: VerifyCodeConstants.Layout.codeFieldSize,
                    height: VerifyCodeConstants.Layout.codeFieldSize
                )
            
            // Border
            RoundedRectangle(cornerRadius: VerifyCodeConstants.Layout.codeFieldCornerRadius)
                .stroke(borderColor(for: index), lineWidth: VerifyCodeConstants.Layout.codeFieldBorderWidth)
                .frame(
                    width: VerifyCodeConstants.Layout.codeFieldSize,
                    height: VerifyCodeConstants.Layout.codeFieldSize
                )
            
            // Text
            Text(digits[index])
                .font(.custom(VerifyCodeConstants.Typography.codeTextFontName, size: VerifyCodeConstants.Typography.codeTextSize))
                .fontWeight(VerifyCodeConstants.Typography.codeTextWeight)
                .foregroundColor(VerifyCodeConstants.Typography.codeTextColor)
        }
        .scaleEffect(focusedIndex == index ? 1.05 : 1.0)
        .animation(.easeInOut(duration: VerifyCodeConstants.Animation.codeFieldFocusDuration), value: focusedIndex)
        .onTapGesture {
            focusedIndex = index
            isFieldFocused = true
        }
    }
    
    // MARK: - Helper Methods
    private func borderColor(for index: Int) -> Color {
        if isError {
            return VerifyCodeConstants.Colors.codeFieldBorderError
        } else if focusedIndex == index {
            return VerifyCodeConstants.Colors.codeFieldBorderFocused
        } else if !digits[index].isEmpty {
            return VerifyCodeConstants.Colors.codeFieldBorderFilled
        } else {
            return VerifyCodeConstants.Colors.codeFieldBorderDefault
        }
    }
    
    private func handleCodeChange(_ newCode: String) {
        // Filter only digits
        let filteredCode = String(newCode.filter { $0.isNumber })
        
        // Limit to code length
        let limitedCode = String(filteredCode.prefix(codeLength))
        
        // Update digits array
        digits = Array(repeating: "", count: codeLength)
        for (index, character) in limitedCode.enumerated() {
            if index < codeLength {
                digits[index] = String(character)
            }
        }
        
        // Update focused index
        focusedIndex = min(limitedCode.count, codeLength - 1)
        
        // Update binding
        code = limitedCode
        
        // Clear error state when user starts typing
        if isError && !limitedCode.isEmpty {
            isError = false
        }
        
        // Call completion handler when code is complete
        if limitedCode.count == codeLength {
            onCodeComplete(limitedCode)
        }
    }
    
    // MARK: - Public Methods
    func clearCode() {
        code = ""
        digits = Array(repeating: "", count: codeLength)
        focusedIndex = 0
        isFieldFocused = true
    }
    
    func setError() {
        isError = true
        // Shake animation could be added here
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        // Normal state
        OTPInputField(
            code: .constant(""),
            onCodeComplete: { code in
                print("Code completed: \(code)")
            }
        )
        
        // Partially filled
        OTPInputField(
            code: .constant("12"),
            onCodeComplete: { code in
                print("Code completed: \(code)")
            }
        )
        
        // Error state
        OTPInputField(
            code: .constant("1234"),
            isError: .constant(true),
            onCodeComplete: { code in
                print("Code completed: \(code)")
            }
        )
    }
    .padding()
}
