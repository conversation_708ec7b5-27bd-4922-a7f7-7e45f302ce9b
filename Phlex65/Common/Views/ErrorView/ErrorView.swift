//
//  ErrorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Error View
struct ErrorView: View {

    // MARK: - Properties
    let message: String
    let retryAction: (() -> Void)?
    let dismissAction: () -> Void

    // MARK: - Initialization
    init(
        message: String,
        retryAction: (() -> Void)? = nil,
        dismissAction: @escaping () -> Void
    ) {
        self.message = message
        self.retryAction = retryAction
        self.dismissAction = dismissAction
    }

    // MARK: - Body
    var body: some View {
        VStack(spacing: AppTheme.Spacing.lg) {
            VStack(spacing: AppTheme.Spacing.md) {
                // Error Icon
                Image(systemName: AppConstants.exclamationTriangleFillImage)
                    .font(.system(size: 48))
                    .foregroundColor(AppTheme.Colors.error)

                // Error Message
                Text(message)
                    .font(AppTheme.Typography.bodyLarge)
                    .foregroundColor(AppTheme.Colors.textPrimary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, AppTheme.Spacing.lg)
            }

            // Action Buttons
            VStack(spacing: AppTheme.Spacing.md) {
                HStack(spacing: AppTheme.Spacing.md) {
                    // Dismiss Button
                    Button(okText) {
                        dismissAction()
                    }
                    .buttonStyle(SecondaryButtonStyle())

                    // Retry Button (if available)
                    if let retryAction = retryAction {
                        Button(retryText) {
                            retryAction()
                        }
                        .buttonStyle(PrimaryButtonStyle())
                    }
                }
            }
            .padding(.horizontal, AppTheme.Spacing.lg)
        }
        .padding(AppTheme.Spacing.xl)
        .background(AppTheme.Colors.background)
        .cornerRadius(AppTheme.CornerRadius.lg)
        .shadow(
            color: AppTheme.Shadow.medium.color,
            radius: AppTheme.Shadow.medium.radius,
            x: AppTheme.Shadow.medium.x,
            y: AppTheme.Shadow.medium.y
        )
        .padding(AppTheme.Spacing.lg)
    }
}

// MARK: - Preview
#Preview("Error View") {
    ErrorView(
        message: genericErrorMessage,
        retryAction: {},
        dismissAction: {}
    )
}
