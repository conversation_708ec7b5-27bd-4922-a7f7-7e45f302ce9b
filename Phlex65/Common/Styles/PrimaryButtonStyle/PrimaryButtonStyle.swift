//
//  PrimaryButtonStyle.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Primary Button Style
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTypography.labelLarge.weight(.semibold))
            .foregroundColor(AppColors.textInverse)
            .frame(height: 44)
            .frame(maxWidth: .infinity)
            .background(AppColors.primary)
            .cornerRadius(AppCornerRadius.md)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(AppAnimation.fast, value: configuration.isPressed)
    }
}
