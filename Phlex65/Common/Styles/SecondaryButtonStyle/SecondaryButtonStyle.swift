//
//  SecondaryButtonStyle.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Secondary Button Style
struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(AppTypography.labelLarge.weight(.semibold))
            .foregroundColor(AppColors.textPrimary)
            .frame(height: 44)
            .frame(maxWidth: .infinity)
            .background(AppColors.backgroundSecondary)
            .cornerRadius(AppCornerRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: AppCornerRadius.md)
                    .stroke(AppColors.border, lineWidth: 1)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(AppAnimation.fast, value: configuration.isPressed)
    }
}
