//
//  MainCoordinatorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Main Coordinator View
struct MainCoordinatorView: View {
    var body: some View {
        TabView {
            HomeView()
                .tabItem {
                    Image(systemName: AppConstants.houseImage)
                    Text(homeText)
                }

            SearchView()
                .tabItem {
                    Image(systemName: AppConstants.magnifyingGlassImage)
                    Text(searchText)
                }

            BookingsView()
                .tabItem {
                    Image(systemName: AppConstants.calendarImage)
                    Text(bookingsText)
                }

            ProfileView()
                .tabItem {
                    Image(systemName: AppConstants.personImage)
                    Text(profileText)
                }
        }
        .accentColor(AppTheme.Colors.primary)
    }
}

// MARK: - Preview
#Preview {
    MainCoordinatorView()
}
