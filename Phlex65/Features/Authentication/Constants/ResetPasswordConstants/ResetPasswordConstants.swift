//
//  ResetPasswordConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Reset Password Constants (EXACT Figma Design)
struct ResetPasswordConstants {

    // MARK: - Typography (EXACT Figma Specifications)
    struct Typography {
        // Title - "New Password" (EXACT Figma: Inter 20px/500, #242424)
        static let titleFontName = "Inter-Medium"
        static let titleSize: CGFloat = 20.0 // EXACT: "font-size": "20px"
        static let titleWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let titleColor = Color(hex: "242424") // EXACT: "color": "var(--main-black, #242424)"

        // Subtitle - "Your new password..." (EXACT Figma: Inter 12px/400, #6B7280)
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 12.0 // EXACT: "font-size": "12px"
        static let subtitleWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let subtitleColor = Color(hex: "6B7280") // EXACT: "color": "var(--Gray-500, #6B7280)"

        // Field Labels - "Password", "Confirm Password" (EXACT Figma: Inter 14px/500, #101828)
        static let labelFontName = "Inter-Medium"
        static let labelSize: CGFloat = 14.0 // EXACT: "font-size": "14px"
        static let labelWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let labelColor = Color(hex: "101828") // EXACT: "color": "var(--Grayscale-Gray-900, #101828)"

        // Field Text - Placeholder text (EXACT Figma: Inter 14px/400, #667085)
        static let fieldTextFontName = "Inter-Regular"
        static let fieldTextSize: CGFloat = 14.0 // EXACT: "font-size": "14px"
        static let fieldTextWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let fieldTextColor = Color(hex: "667085") // EXACT: "color": "var(--Grayscale-Gray-500, #667085)"

        // Button Text - "Create New Password" (EXACT Figma: Inter 16px/500, #FFF)
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0 // EXACT: "font-size": "16px"
        static let buttonWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let buttonColor = Color.white // EXACT: "color": "var(--White, #FFF)"
    }

    // MARK: - Colors (EXACT Figma Design)
    struct Colors {
        static let screenBackground = Color(hex: "FFFFFF") // EXACT: "background": "#FFF"

        // Button colors (EXACT Figma)
        static let primaryButtonBackground = Color(hex: "89C226") // EXACT: "background": "var(--Primary-Primary-500, #89C226)"
        static let primaryButtonText = Color.white // EXACT: "color": "var(--White, #FFF)"
        static let primaryButtonDisabled = Color(hex: "E5E7EB")

        // Field colors (EXACT Figma)
        static let fieldBackground = Color(hex: "FFFFFF") // EXACT: "background": "var(--Greyscale-0, #FFF)"
        static let fieldBorder = Color(hex: "D8DBDF") // EXACT: "border": "1px solid var(--Gray-200, #D8DBDF)"

        // System colors
        static let errorColor = Color(hex: "EF4444")
        static let successColor = Color(hex: "89C226")
    }

    // MARK: - Layout (EXACT Figma Design)
    struct Layout {
        // Screen layout (EXACT same as VerifyCodeView)
        static let screenPadding: CGFloat = 16.0 // EXACT: 16px horizontal padding
        static let topPadding: CGFloat = 32.0 // EXACT: same as VerifyCodeView
        static let bottomPadding: CGFloat = 40.0

        // Back button spacing (EXACT same as VerifyCodeView)
        static let backButtonToTitleSpacing: CGFloat = 24.0 // EXACT: same as VerifyCodeView

        // Title and subtitle (EXACT Figma: "gap": "3.948px")
        static let titleToSubtitleSpacing: CGFloat = 3.948 // EXACT: "gap": "3.948px"
        static let subtitleWidth: CGFloat = 241.0 // EXACT: "width": "241px"

        // Main content spacing (EXACT Figma: "gap": "36px", "gap": "32px")
        static let subtitleToFormSpacing: CGFloat = 36.0 // EXACT: "gap": "36px" (subtitle to Password label)
        static let formToButtonSpacing: CGFloat = 32.0 // EXACT: "gap": "32px" (form to button)

        // Form fields (EXACT Figma: "gap": "18.753px", "gap": "6px")
        static let fieldSpacing: CGFloat = 18.753 // EXACT: "gap": "18.753px" (between Password and Confirm Password fields)
        static let labelToFieldSpacing: CGFloat = 6.0 // EXACT: "gap": "6px" (label to input field)

        // Field internal spacing (EXACT Figma: "gap": "8px")
        static let fieldInternalSpacing: CGFloat = 8.0 // EXACT: "gap": "8px" (inside field between text and eye icon)

        // Field dimensions (EXACT Figma)
        static let fieldHeight: CGFloat = 48.0 // EXACT: "height": "48px"
        static let fieldCornerRadius: CGFloat = 10.0 // EXACT: "border-radius": "10px"
        static let fieldPadding: CGFloat = 12.0 // EXACT: "padding": "8px 12px"

        // Button (EXACT Figma)
        static let buttonHeight: CGFloat = 48.0 // EXACT: "height": "48px"
        static let buttonCornerRadius: CGFloat = 50.0 // EXACT: "border-radius": "50px"
        static let buttonPadding: CGFloat = 20.0 // EXACT: "padding": "12px 20px"
    }
    
    // MARK: - Validation
    struct Validation {
        static let minPasswordLength = 8
        static let maxPasswordLength = 128
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let titleIdentifier = "resetPasswordTitle"
        static let subtitleIdentifier = "resetPasswordSubtitle"
        static let passwordFieldIdentifier = "resetPasswordField"
        static let confirmPasswordFieldIdentifier = "resetConfirmPasswordField"
        static let resetButtonIdentifier = "resetPasswordButton"
        static let backButtonIdentifier = "resetPasswordBackButton"
    }
}

// MARK: - Localized Strings
let resetPasswordTitle = "New Password"
let resetPasswordSubtitle = "Your new password must be different from previously used passwords."
let resetPasswordLabel = "Password"
let resetConfirmPasswordLabel = "Confirm Password"
let createNewPasswordText = "Create New Password"
let resetPasswordSuccessTitle = "Password Reset Successful"
let resetPasswordSuccessMessage = "Your password has been reset successfully.\nYou can now log in with your new password"
let resetGoToLoginText = "Go to Login"

// MARK: - Error Messages
let resetPasswordTooShortError = "Password must be at least 8 characters long"
let resetPasswordTooLongError = "Password must be less than 128 characters"
let resetPasswordsDoNotMatchError = "Passwords do not match"
let resetPasswordRequiredError = "Password is required"
let resetConfirmPasswordRequiredError = "Please confirm your password"
let resetPasswordFailedError = "Failed to reset password. Please try again."
let resetNetworkErrorMessage = "Network error. Please check your connection and try again."
