//
//  ResetPasswordConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Reset Password Constants
struct ResetPasswordConstants {
    
    // MARK: - Typography
    struct Typography {
        // Title
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 32
        static let titleWeight: Font.Weight = .semibold
        static let titleColor = Color(hex: "#1F2937")
        
        // Subtitle
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 16
        static let subtitleWeight: Font.Weight = .regular
        static let subtitleColor = Color(hex: "#6B7280")
        
        // Field Labels
        static let labelFontName = "Inter-Medium"
        static let labelSize: CGFloat = 16
        static let labelWeight: Font.Weight = .medium
        static let labelColor = Color(hex: "#374151")
        
        // Button Text
        static let buttonFontName = "Inter-SemiBold"
        static let buttonSize: CGFloat = 16
        static let buttonWeight: Font.Weight = .semibold
    }
    
    // MARK: - Colors
    struct Colors {
        static let screenBackground = Color(hex: "#FFFFFF")
        static let primaryButtonBackground = Color(hex: "#8BC34A")
        static let primaryButtonText = Color(hex: "#FFFFFF")
        static let primaryButtonDisabled = Color(hex: "#E5E7EB")
        static let errorColor = Color(hex: "#EF4444")
        static let successColor = Color(hex: "#8BC34A")
    }
    
    // MARK: - Layout
    struct Layout {
        static let screenPadding: CGFloat = 24
        static let topPadding: CGFloat = 60
        static let bottomPadding: CGFloat = 40
        
        // Spacing
        static let titleToSubtitleSpacing: CGFloat = 12
        static let subtitleToFormSpacing: CGFloat = 32
        static let fieldSpacing: CGFloat = 24
        static let labelToFieldSpacing: CGFloat = 8
        static let fieldToButtonSpacing: CGFloat = 32
        
        // Button
        static let buttonHeight: CGFloat = 56
        static let buttonCornerRadius: CGFloat = 100
    }
    
    // MARK: - Validation
    struct Validation {
        static let minPasswordLength = 8
        static let maxPasswordLength = 128
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let titleIdentifier = "resetPasswordTitle"
        static let subtitleIdentifier = "resetPasswordSubtitle"
        static let passwordFieldIdentifier = "resetPasswordField"
        static let confirmPasswordFieldIdentifier = "resetConfirmPasswordField"
        static let resetButtonIdentifier = "resetPasswordButton"
        static let backButtonIdentifier = "resetPasswordBackButton"
    }
}

// MARK: - Localized Strings
let resetPasswordTitle = "New Password"
let resetPasswordSubtitle = "Your new password must be different from previously used passwords."
let resetPasswordLabel = "Password"
let resetConfirmPasswordLabel = "Confirm Password"
let createNewPasswordText = "Create New Password"
let resetPasswordSuccessTitle = "Password Reset Successful"
let resetPasswordSuccessMessage = "Your password has been reset successfully.\nYou can now log in with your new password"
let resetGoToLoginText = "Go to Login"

// MARK: - Error Messages
let resetPasswordTooShortError = "Password must be at least 8 characters long"
let resetPasswordTooLongError = "Password must be less than 128 characters"
let resetPasswordsDoNotMatchError = "Passwords do not match"
let resetPasswordRequiredError = "Password is required"
let resetConfirmPasswordRequiredError = "Please confirm your password"
let resetPasswordFailedError = "Failed to reset password. Please try again."
let resetNetworkErrorMessage = "Network error. Please check your connection and try again."
