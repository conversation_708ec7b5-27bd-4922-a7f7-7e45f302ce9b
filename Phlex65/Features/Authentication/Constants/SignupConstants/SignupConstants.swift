//
//  SignupConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

struct SignupConstants {
    
    // MARK: - Step 1 Constants (Create Account) - EXACT Figma Specifications
    struct Step1 {

        // MARK: - Text Content
        struct Text {
            static let title = "Create Account"
            static let subtitle = "Create your account to book professional caregivers easily."
            static let fullNameLabel = "Full Name"
            static let fullNamePlaceholder = "Enter Email Address" // Figma shows this placeholder for Full Name field
            static let emailLabel = "Email Address"
            static let emailPlaceholder = "Enter Email Address"
            static let passwordLabel = "Password"
            static let passwordPlaceholder = "****************"
            static let confirmPasswordLabel = "Confirm Password"
            static let confirmPasswordPlaceholder = "****************"
            static let termsText = "Agree with Terms & Condition"
            static let nextButtonText = "Next"
            static let alreadyHaveAccountText = "Already  have an account? Sign In" // EXACT Figma text with double space
            static let progressText = "1/2"
        }

        // MARK: - Typography (EXACT Figma Specifications)
        struct Typography {
            // Title: Inter 24px/600 (semibold), line-height: 140% (33.6px)
            static let titleFont = Font.custom("Inter-SemiBold", size: 24)
            // Subtitle: Inter 14px/400, line-height: 20px (142.857%)
            static let subtitleFont = Font.custom("Inter-Regular", size: 14)
            // Labels: Inter 14px/500, line-height: 20px (142.857%), letter-spacing: -0.14px
            static let labelFont = Font.custom("Inter-Medium", size: 14)
            // Placeholder: Inter 14px/400, line-height: 20px (142.857%), letter-spacing: -0.14px
            static let placeholderFont = Font.custom("Inter-Regular", size: 14)
            // Button: Inter 16px/500, line-height: 24px (150%)
            static let buttonFont = Font.custom("Inter-Medium", size: 16)
            // Footer: Inter 14px/500, line-height: 20px
            static let footerFont = Font.custom("Inter-Medium", size: 14)
            // Progress: Inter 14px/500, line-height: 20px (142.857%)
            static let progressFont = Font.custom("Inter-Medium", size: 14)
            // Terms: Inter 12px/500, line-height: 18px
            static let termsFont = Font.custom("Inter-Medium", size: 12)
        }

        // MARK: - Colors (EXACT Figma Specifications)
        struct Colors {
            // Background: #FFF
            static let screenBackground = Color(hex: "#FFFFFF")
            // Title: #0C1523
            static let titleColor = Color(hex: "#0C1523")
            // Subtitle: #6B7280
            static let subtitleColor = Color(hex: "#6B7280")
            // Labels: #101828
            static let labelColor = Color(hex: "#101828")
            // Placeholder: #667085
            static let placeholderColor = Color(hex: "#667085")
            // Button Background: #89C226
            static let buttonBackground = Color(hex: "#89C226")
            // Button Text: #FFF
            static let buttonText = Color(hex: "#FFFFFF")
            // Footer Text: #0292D9 (entire footer text is blue in Figma)
            static let footerText = Color(hex: "#0292D9")
            // Progress Active: #0292D9
            static let progressActive = Color(hex: "#0292D9")
            // Progress Inactive: #EDEDED
            static let progressInactive = Color(hex: "#EDEDED")
            // Progress Text: #383A42
            static let progressTextColor = Color(hex: "#383A42")
            // Terms Text: #25272C
            static let termsTextColor = Color(hex: "#25272C")
            // Checkbox Active: #89C226 (same green as Next button when selected)
            static let checkboxActive = Color(hex: "#89C226")
            // Field Border: #D8DBDF
            static let fieldBorder = Color(hex: "#D8DBDF")
            // Field Background: #FFF
            static let fieldBackground = Color(hex: "#FFFFFF")
        }

        // MARK: - Layout (EXACT Figma Specifications)
        struct Layout {
            // Main container: padding 0px 16px, gap 32px
            static let screenPadding: CGFloat = 16
            static let mainContainerGap: CGFloat = 32
            // Title + desc gap: 4px
            static let titleToSubtitleSpacing: CGFloat = 4
            // Frame 1618872389 gap: 12px
            static let subtitleToProgressSpacing: CGFloat = 12
            // Body gap: 28px
            static let progressToFormSpacing: CGFloat = 28
            // List gap: 14px (between form fields)
            static let fieldSpacing: CGFloat = 14
            // Label to field gap: 6px
            static let labelToFieldSpacing: CGFloat = 6
            // Frame 1618872167 gap: 32px (form to CTA section)
            static let formToButtonSpacing: CGFloat = 32
            // CTA gap: 16px (button to footer)
            static let buttonToFooterSpacing: CGFloat = 16

            // Progress Bar: height 4px, border-radius 8px
            static let progressBarHeight: CGFloat = 4
            static let progressBarCornerRadius: CGFloat = 8
            // Progress gap: 16px
            static let progressBarToTextSpacing: CGFloat = 16

            // Button: height 48px, border-radius 50px, padding 12px 20px
            static let buttonHeight: CGFloat = 48
            static let buttonCornerRadius: CGFloat = 50
            static let buttonPaddingVertical: CGFloat = 12
            static let buttonPaddingHorizontal: CGFloat = 20

            // Input Fields: height 48px, border-radius 10px, padding 8px 12px
            static let fieldHeight: CGFloat = 48
            static let fieldCornerRadius: CGFloat = 10
            static let fieldPaddingVertical: CGFloat = 8
            static let fieldPaddingHorizontal: CGFloat = 12

            // Checkbox: EXACT 24px x 24px as per Figma JSON, gap 6px
            static let checkboxSize: CGFloat = 24
            static let checkboxToTextSpacing: CGFloat = 6

            // EXACT spacing between form fields and checkbox
            static let fieldsToCheckboxSpacing: CGFloat = 16
            // EXACT spacing between checkbox and button
            static let checkboxToButtonSpacing: CGFloat = 24
        }
    }
    
    // MARK: - Step 2 Constants (Complete Profile) - EXACT Figma JSON
    struct Step2 {

        // MARK: - Text Content (EXACT Figma JSON)
        struct Text {
            static let title = "Complete Your Profile"
            static let subtitle = "Create your account to book professional caregivers easily."
            static let phoneNumberLabel = "Phone Number"
            static let phoneNumberPlaceholder = "Enter Phone Number"
            static let genderLabel = "Gender"
            static let genderPlaceholder = "Select"
            static let signUpButtonText = "Sign Up"
            static let alreadyHaveAccountText = "Already have an account?"
            static let signInText = "Sign In"
            static let progressText = "2/2"
        }

        // MARK: - Typography (EXACT Figma JSON)
        struct Typography {
            // Title: Inter 24px/600, line-height: 140% (33.6px), color: #0C1523
            static let titleFont = Font.custom("Inter-SemiBold", size: 24)
            // Subtitle: Inter 14px/400, line-height: 20px (142.857%), color: #6B7280
            static let subtitleFont = Font.custom("Inter-Regular", size: 14)
            // Labels: Inter 14px/500, line-height: 20px (142.857%), letter-spacing: -0.14px, color: #101828
            static let labelFont = Font.custom("Inter-Medium", size: 14)
            // Placeholders: Inter 14px/400, line-height: 20px (142.857%), letter-spacing: -0.14px, color: #667085
            static let placeholderFont = Font.custom("Inter-Regular", size: 14)
            // Button: Inter 16px/500, line-height: 24px (150%), color: #FFF
            static let buttonFont = Font.custom("Inter-Medium", size: 16)
            // Footer: Inter 14px/500, line-height: 20px, color: #0292D9
            static let footerFont = Font.custom("Inter-Medium", size: 14)
            // Progress: Inter 14px/500, line-height: 20px (142.857%), color: #383A42
            static let progressFont = Font.custom("Inter-Medium", size: 14)
        }
        
        // MARK: - Colors (EXACT Figma JSON)
        struct Colors {
            // Screen: background: #FFF
            static let screenBackground = Color(hex: "#FFF")
            // Title: color: #0C1523
            static let titleColor = Color(hex: "#0C1523")
            // Subtitle: color: #6B7280
            static let subtitleColor = Color(hex: "#6B7280")
            // Labels: color: #101828
            static let labelColor = Color(hex: "#101828")
            // Placeholders: color: #667085
            static let placeholderColor = Color(hex: "#667085")
            // Button: background: #89C226, color: #FFF
            static let buttonBackground = Color(hex: "#89C226")
            static let buttonText = Color(hex: "#FFF")
            // Footer: color: #0292D9
            static let footerText = Color(hex: "#0292D9")
            // Progress: active: #0292D9, text: #383A42
            static let progressActive = Color(hex: "#0292D9")
            static let progressTextColor = Color(hex: "#383A42")
            // Profile Image: background with gradient overlay
            static let profileImageBackground = Color(hex: "#F3F4F6")
            static let profileImageIcon = Color(hex: "#6B7280")
            // Camera Icon: background: #0292D9, border: #FFF
            static let cameraIconBackground = Color(hex: "#0292D9")
            static let cameraIconColor = Color(hex: "#FFF")
            static let cameraIconBorder = Color(hex: "#FFF")
            // Field borders: #D8DBDF
            static let fieldBorder = Color(hex: "#D8DBDF")
            static let fieldBackground = Color(hex: "#FFF")
        }
        
        // MARK: - Layout (EXACT Figma JSON)
        struct Layout {
            // Main container: padding: 0px 16px, gap: 32px
            static let screenPadding: CGFloat = 16
            static let mainContainerGap: CGFloat = 32
            // Title + desc gap: 4px
            static let titleToSubtitleSpacing: CGFloat = 4
            // Frame 1618872389 gap: 12px
            static let subtitleToProgressSpacing: CGFloat = 12
            // Body gap: 28px
            static let progressToFormSpacing: CGFloat = 28
            // Frame 1000001306 gap: 32px
            static let formContainerGap: CGFloat = 32
            // Frame 1000001205 gap: 24px
            static let profileToFieldsSpacing: CGFloat = 24
            // Frame 1000001307 gap: 18.753px (rounded to 19px)
            static let fieldSpacing: CGFloat = 19
            // Label to field gap: 6px
            static let labelToFieldSpacing: CGFloat = 6
            // Frame 1618872167 gap: 32px
            static let formToButtonSpacing: CGFloat = 32
            // CTA gap: 16px
            static let buttonToFooterSpacing: CGFloat = 16

            // Progress Bar: height: 4px, border-radius: 8px, gap: 4px
            static let progressBarHeight: CGFloat = 4
            static let progressBarCornerRadius: CGFloat = 8
            static let progressBarGap: CGFloat = 4
            // Progress gap: 16px
            static let progressBarToTextSpacing: CGFloat = 16

            // Button: height: 48px, border-radius: 50px, padding: 12px 20px
            static let buttonHeight: CGFloat = 48
            static let buttonCornerRadius: CGFloat = 50
            static let buttonPaddingVertical: CGFloat = 12
            static let buttonPaddingHorizontal: CGFloat = 20

            // Profile Image: 85px circle
            static let profileImageSize: CGFloat = 85
            // Camera Icon: 28px container, 18.667px icon
            static let cameraIconContainerSize: CGFloat = 28
            static let cameraIconSize: CGFloat = 18.667
            static let cameraIconBorderWidth: CGFloat = 1.167

            // Input Fields: height: 48px, border-radius: 10px, padding: 8px 12px
            static let fieldHeight: CGFloat = 48
            static let fieldCornerRadius: CGFloat = 10
            static let fieldPaddingVertical: CGFloat = 8
            static let fieldPaddingHorizontal: CGFloat = 12

            // Back button and title spacing
            static let backButtonToTitleSpacing: CGFloat = 12
            static let titleSectionGap: CGFloat = 6
        }
    }
    
    // MARK: - Validation Constants
    struct Validation {
        static let minPasswordLength: Int = 8
        static let maxPasswordLength: Int = 128
        static let minNameLength: Int = 2
        static let maxNameLength: Int = 50
        static let phoneNumberPattern = "^[+]?[1-9]\\d{1,14}$"
        static let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let defaultDuration: Double = 0.3
        static let springResponse: Double = 0.6
        static let springDamping: Double = 0.8
        static let progressAnimationDuration: Double = 0.5
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        // Step 1
        static let step1TitleIdentifier = "signup_step1_title"
        static let step1SubtitleIdentifier = "signup_step1_subtitle"
        static let fullNameFieldIdentifier = "signup_full_name_field"
        static let emailFieldIdentifier = "signup_email_field"
        static let passwordFieldIdentifier = "signup_password_field"
        static let confirmPasswordFieldIdentifier = "signup_confirm_password_field"
        static let termsCheckboxIdentifier = "signup_terms_checkbox"
        static let nextButtonIdentifier = "signup_next_button"
        static let signInLinkIdentifier = "signup_sign_in_link"
        
        // Step 2
        static let step2TitleIdentifier = "signup_step2_title"
        static let step2SubtitleIdentifier = "signup_step2_subtitle"
        static let profileImageIdentifier = "signup_profile_image"
        static let phoneNumberFieldIdentifier = "signup_phone_number_field"
        static let genderFieldIdentifier = "signup_gender_field"
        static let signUpButtonIdentifier = "signup_sign_up_button"
    }
}

// MARK: - Localized Strings
let signupStep1Title = "Create Account"
let signupStep1Subtitle = "Create your account to book professional caregivers easily."
let signupStep2Title = "Complete Your Profile"
let signupStep2Subtitle = "Create your account to book professional caregivers easily."
let signupFullNameLabel = "Full Name"
let signupEmailLabel = "Email Address"
let signupPasswordLabel = "Password"
let signupConfirmPasswordLabel = "Confirm Password"
let signupPhoneNumberLabel = "Phone Number"
let signupGenderLabel = "Gender"
let signupTermsText = "Agree with Terms & Condition"
let signupNextButtonText = "Next"
let signupSignUpButtonText = "Sign Up"
let signupAlreadyHaveAccountText = "Already have an account?"
let signupSignInText = "Sign In"
