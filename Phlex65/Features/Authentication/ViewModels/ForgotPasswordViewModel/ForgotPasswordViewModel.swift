//
//  ForgotPasswordViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Forgot Password View Model
@MainActor
class ForgotPasswordViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var email: String = ""
    @Published var isLoading: Bool = false
    @Published var showSuccessMessage: Bool = false
    @Published var shouldNavigateToVerifyCode: Bool = false
    
    // MARK: - Error States
    @Published var emailError: String?
    @Published var generalError: String?
    
    // MARK: - Focus States
    @Published var isEmailFocused: Bool = false
    
    // MARK: - Services
    private let authenticationService = AuthenticationService.shared
    private let validationService = ValidationService.shared
    
    // MARK: - Computed Properties
    var isFormValid: Bool {
        return !email.isEmpty && emailError == nil
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    // MARK: - Public Methods
    func sendResetLink() async {
        guard canSubmit else { return }
        
        // Clear previous errors
        clearErrors()
        
        // Validate email
        let emailValidation = validationService.validateEmail(email)
        guard emailValidation.isValid else {
            emailError = emailValidation.errorMessage
            return
        }
        
        // Start loading
        isLoading = true
        
        do {
            // Attempt to send reset link
            try await authenticationService.sendPasswordResetLink(email: email)

            // Navigate to verify code screen
            shouldNavigateToVerifyCode = true

        } catch {
            // Handle error
            handleResetError(error)
        }
        
        // Stop loading
        isLoading = false
    }
    
    func validateEmailField() {
        let validation = validationService.validateEmail(email)
        emailError = validation.errorMessage
    }
    
    func clearErrors() {
        emailError = nil
        generalError = nil
    }
    
    func clearForm() {
        email = ""
        clearErrors()
        showSuccessMessage = false
    }
    
    func resetSuccessState() {
        showSuccessMessage = false
        clearForm()
    }
    
    // MARK: - Focus Management
    func focusEmail() {
        isEmailFocused = true
    }
    
    func clearFocus() {
        isEmailFocused = false
    }
    
    // MARK: - Private Methods
    private func handleResetError(_ error: Error) {
        // Map different error types to user-friendly messages
        if let networkError = error as? URLError {
            switch networkError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                generalError = networkErrorMessage
            case .timedOut:
                generalError = "Request timed out. Please try again."
            default:
                generalError = networkErrorMessage
            }
        } else {
            // For other errors, show a generic message
            generalError = "Unable to send reset link. Please try again later."
        }
        
        print("❌ Password reset error: \(error.localizedDescription)")
    }
}

// MARK: - Field Validation on Change
extension ForgotPasswordViewModel {
    
    func onEmailChanged(_ newValue: String) {
        email = newValue
        
        // Clear error when user starts typing
        if emailError != nil && !newValue.isEmpty {
            emailError = nil
        }
        
        // Validate on blur or when field is complete
        if !isEmailFocused && !newValue.isEmpty {
            validateEmailField()
        }
    }
    
    func onEmailFocusChanged(_ isFocused: Bool) {
        isEmailFocused = isFocused
        
        // Validate when field loses focus
        if !isFocused && !email.isEmpty {
            validateEmailField()
        }
    }
}
