//
//  SignupStep1ViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Signup Step 1 View Model
@MainActor
class SignupStep1ViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var fullName: String = ""
    @Published var email: String = ""
    @Published var password: String = ""
    @Published var confirmPassword: String = ""
    @Published var agreeToTerms: Bool = false
    @Published var isLoading: Bool = false
    
    // MARK: - Error States
    @Published var fullNameError: String?
    @Published var emailError: String?
    @Published var passwordError: String?
    @Published var confirmPasswordError: String?
    @Published var termsError: String?
    @Published var generalError: String?
    
    // MARK: - Focus States
    @Published var isFullNameFocused: Bool = false
    @Published var isEmailFocused: Bool = false
    @Published var isPasswordFocused: Bool = false
    @Published var isConfirmPasswordFocused: Bool = false
    
    // MARK: - Show Password States
    @Published var showPassword: Bool = false
    @Published var showConfirmPassword: Bool = false
    
    // MARK: - Navigation States
    @Published var shouldNavigateToStep2: Bool = false
    
    // MARK: - Services
    private let validationService = ValidationService.shared
    
    // MARK: - Computed Properties
    var firstName: String {
        let components = fullName.trimmingCharacters(in: .whitespacesAndNewlines).components(separatedBy: " ")
        return components.first ?? ""
    }
    
    var lastName: String {
        let components = fullName.trimmingCharacters(in: .whitespacesAndNewlines).components(separatedBy: " ")
        return components.dropFirst().joined(separator: " ")
    }
    
    var isFormValid: Bool {
        return !fullName.isEmpty &&
               !email.isEmpty &&
               !password.isEmpty &&
               !confirmPassword.isEmpty &&
               agreeToTerms &&
               fullNameError == nil &&
               emailError == nil &&
               passwordError == nil &&
               confirmPasswordError == nil
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    // MARK: - Public Methods
    func proceedToStep2() {
        print("🔄 proceedToStep2() called")
        print("📊 canSubmit: \(canSubmit)")
        print("📊 isFormValid: \(isFormValid)")
        print("📊 isLoading: \(isLoading)")
        print("📊 fullName: '\(fullName)'")
        print("📊 email: '\(email)'")
        print("📊 password: '\(password)'")
        print("📊 confirmPassword: '\(confirmPassword)'")
        print("📊 agreeToTerms: \(agreeToTerms)")

        guard canSubmit else {
            print("❌ canSubmit is false, returning early")
            return
        }

        // Clear previous errors
        clearErrors()
        print("🧹 Errors cleared")

        // Validate all fields
        print("🔍 Starting field validation...")
        guard validateAllFields() else {
            print("❌ validateAllFields() failed")
            print("❌ fullNameError: \(fullNameError ?? "nil")")
            print("❌ emailError: \(emailError ?? "nil")")
            print("❌ passwordError: \(passwordError ?? "nil")")
            print("❌ confirmPasswordError: \(confirmPasswordError ?? "nil")")
            print("❌ termsError: \(termsError ?? "nil")")
            return
        }

        print("✅ All validations passed!")
        // Navigate to step 2
        shouldNavigateToStep2 = true
        print("🚀 shouldNavigateToStep2 set to true")
    }
    
    func clearErrors() {
        fullNameError = nil
        emailError = nil
        passwordError = nil
        confirmPasswordError = nil
        termsError = nil
        generalError = nil
    }
    
    func clearForm() {
        fullName = ""
        email = ""
        password = ""
        confirmPassword = ""
        agreeToTerms = false
        clearFocus()
    }
    
    func clearFocus() {
        isFullNameFocused = false
        isEmailFocused = false
        isPasswordFocused = false
        isConfirmPasswordFocused = false
    }
    
    // MARK: - Field Change Handlers
    func onFullNameChanged(_ newValue: String) {
        fullName = newValue
        if !newValue.isEmpty && fullNameError != nil {
            fullNameError = nil
        }
    }
    
    func onEmailChanged(_ newValue: String) {
        email = newValue
        if !newValue.isEmpty && emailError != nil {
            emailError = nil
        }
    }
    
    func onPasswordChanged(_ newValue: String) {
        password = newValue
        if !newValue.isEmpty && passwordError != nil {
            passwordError = nil
        }
        // Re-validate confirm password if it's already filled
        if !confirmPassword.isEmpty {
            validatePasswordMatch()
        }
    }
    
    func onConfirmPasswordChanged(_ newValue: String) {
        confirmPassword = newValue
        if !newValue.isEmpty && confirmPasswordError != nil {
            confirmPasswordError = nil
        }
        // Validate password match in real-time
        if !password.isEmpty {
            validatePasswordMatch()
        }
    }
    
    // MARK: - Focus Change Handlers
    func onFullNameFocusChanged(_ isFocused: Bool) {
        isFullNameFocused = isFocused
        if !isFocused && !fullName.isEmpty {
            validateFullName()
        }
    }
    
    func onEmailFocusChanged(_ isFocused: Bool) {
        isEmailFocused = isFocused
        if !isFocused && !email.isEmpty {
            validateEmail()
        }
    }
    
    func onPasswordFocusChanged(_ isFocused: Bool) {
        isPasswordFocused = isFocused
        if !isFocused && !password.isEmpty {
            validatePassword()
        }
    }
    
    func onConfirmPasswordFocusChanged(_ isFocused: Bool) {
        isConfirmPasswordFocused = isFocused
        if !isFocused && !confirmPassword.isEmpty {
            validatePasswordMatch()
        }
    }
    
    // MARK: - Terms & Conditions
    func toggleTermsAgreement() {
        agreeToTerms.toggle()
        if agreeToTerms && termsError != nil {
            termsError = nil
        }
    }
    
    // MARK: - Private Methods
    private func validateAllFields() -> Bool {
        var isValid = true
        
        // Validate full name
        if !validateFullName() {
            isValid = false
        }
        
        // Validate email
        if !validateEmail() {
            isValid = false
        }
        
        // Validate password
        if !validatePassword() {
            isValid = false
        }
        
        // Validate password match
        if !validatePasswordMatch() {
            isValid = false
        }
        
        // Validate terms agreement
        if !validateTermsAgreement() {
            isValid = false
        }
        
        return isValid
    }
    
    private func validateFullName() -> Bool {
        let validation = validationService.validateFullName(fullName)
        if !validation.isValid {
            fullNameError = validation.errorMessage
            return false
        }
        fullNameError = nil
        return true
    }
    
    private func validateEmail() -> Bool {
        let validation = validationService.validateEmail(email)
        if !validation.isValid {
            emailError = validation.errorMessage
            return false
        }
        emailError = nil
        return true
    }
    
    private func validatePassword() -> Bool {
        let validation = validationService.validatePassword(password)
        if !validation.isValid {
            passwordError = validation.errorMessage
            return false
        }
        passwordError = nil
        return true
    }
    
    private func validatePasswordMatch() -> Bool {
        if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
            return false
        }
        confirmPasswordError = nil
        return true
    }
    
    private func validateTermsAgreement() -> Bool {
        if !agreeToTerms {
            termsError = "Please agree to the Terms & Conditions"
            return false
        }
        termsError = nil
        return true
    }
}
