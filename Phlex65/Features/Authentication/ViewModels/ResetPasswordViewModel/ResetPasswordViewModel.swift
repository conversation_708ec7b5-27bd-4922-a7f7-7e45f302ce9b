//
//  ResetPasswordViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Reset Password View Model
@MainActor
class ResetPasswordViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var password: String = ""
    @Published var confirmPassword: String = ""
    @Published var isLoading: Bool = false
    @Published var showSuccessPopup: Bool = false
    
    // MARK: - Focus States
    @Published var isPasswordFocused: Bool = false
    @Published var isConfirmPasswordFocused: Bool = false

    // MARK: - Show Password States
    @Published var showPassword: Bool = false
    @Published var showConfirmPassword: Bool = false
    
    // MARK: - Error States
    @Published var passwordError: String?
    @Published var confirmPasswordError: String?
    @Published var generalError: String?
    
    // MARK: - Token
    let resetToken: String
    
    // MARK: - Services
    private let authenticationService = AuthenticationService.shared
    private let validationService = ValidationService.shared
    
    // MARK: - Initialization
    init(resetToken: String) {
        self.resetToken = resetToken
    }
    
    // MARK: - Computed Properties
    var isFormValid: Bool {
        return !password.isEmpty &&
               !confirmPassword.isEmpty &&
               passwordError == nil &&
               confirmPasswordError == nil
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    // MARK: - Public Methods
    func resetPassword() async {
        guard canSubmit else { return }
        
        // Clear previous errors
        clearErrors()
        
        // Validate passwords
        guard validatePasswords() else { return }
        
        // Start loading
        isLoading = true
        
        do {
            // Attempt to reset password
            try await authenticationService.resetPassword(token: resetToken, newPassword: password)
            
            // Show success popup
            showSuccessPopup = true
            
        } catch {
            // Handle reset error
            handleResetError(error)
        }
        
        // Stop loading
        isLoading = false
    }
    
    func clearErrors() {
        passwordError = nil
        confirmPasswordError = nil
        generalError = nil
    }
    
    func clearForm() {
        password = ""
        confirmPassword = ""
        isPasswordFocused = false
        isConfirmPasswordFocused = false
    }
    
    func resetSuccessState() {
        showSuccessPopup = false
        clearForm()
    }
    
    // MARK: - Validation
    private func validatePasswords() -> Bool {
        var isValid = true
        
        // Validate password
        let passwordValidation = validationService.validatePassword(password)
        if !passwordValidation.isValid {
            passwordError = passwordValidation.errorMessage
            isValid = false
        }
        
        // Validate confirm password
        if confirmPassword.isEmpty {
            confirmPasswordError = resetConfirmPasswordRequiredError
            isValid = false
        } else if password != confirmPassword {
            confirmPasswordError = resetPasswordsDoNotMatchError
            isValid = false
        }
        
        return isValid
    }
    
    // MARK: - Private Methods
    private func handleResetError(_ error: Error) {
        // Map different error types to user-friendly messages
        if let networkError = error as? URLError {
            switch networkError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                generalError = resetNetworkErrorMessage
            case .timedOut:
                generalError = "Request timed out. Please try again."
            default:
                generalError = resetNetworkErrorMessage
            }
        } else {
            // For other errors, show a generic message
            generalError = resetPasswordFailedError
        }
        
        print("❌ Password reset error: \(error.localizedDescription)")
    }
}

// MARK: - Field Validation on Change
extension ResetPasswordViewModel {
    
    func onPasswordChanged(_ newValue: String) {
        password = newValue
        
        // Clear error when user starts typing
        if passwordError != nil && !newValue.isEmpty {
            passwordError = nil
        }
        
        // Re-validate confirm password if it was entered
        if !confirmPassword.isEmpty && confirmPasswordError == resetPasswordsDoNotMatchError {
            if newValue == confirmPassword {
                confirmPasswordError = nil
            }
        }
    }
    
    func onConfirmPasswordChanged(_ newValue: String) {
        confirmPassword = newValue
        
        // Clear error when user starts typing
        if confirmPasswordError != nil && !newValue.isEmpty {
            confirmPasswordError = nil
        }
        
        // Check if passwords match
        if !password.isEmpty && !newValue.isEmpty {
            if password != newValue {
                confirmPasswordError = resetPasswordsDoNotMatchError
            } else {
                confirmPasswordError = nil
            }
        }
    }
    
    func onPasswordFocusChanged(_ isFocused: Bool) {
        isPasswordFocused = isFocused
        
        // Validate when field loses focus
        if !isFocused && !password.isEmpty {
            let validation = validationService.validatePassword(password)
            if !validation.isValid {
                passwordError = validation.errorMessage
            }
        }
    }
    
    func onConfirmPasswordFocusChanged(_ isFocused: Bool) {
        isConfirmPasswordFocused = isFocused
        
        // Validate when field loses focus
        if !isFocused && !confirmPassword.isEmpty {
            if confirmPassword.isEmpty {
                confirmPasswordError = resetConfirmPasswordRequiredError
            } else if password != confirmPassword {
                confirmPasswordError = resetPasswordsDoNotMatchError
            }
        }
    }
}
