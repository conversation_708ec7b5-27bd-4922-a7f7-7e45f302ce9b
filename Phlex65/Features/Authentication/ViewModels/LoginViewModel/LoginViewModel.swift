//
//  LoginViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Login View Model
@MainActor
class LoginViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var email: String = ""
    @Published var password: String = ""
    @Published var isLoading: Bool = false
    @Published var showPassword: Bool = false
    
    // MARK: - Error States
    @Published var emailError: String?
    @Published var passwordError: String?
    @Published var generalError: String?
    
    // MARK: - Focus States
    @Published var isEmailFocused: Bool = false
    @Published var isPasswordFocused: Bool = false
    
    // MARK: - Services
    private let authenticationService = AuthenticationService.shared
    private let validationService = ValidationService.shared
    private let keychainService = KeychainService.shared
    
    // MARK: - Computed Properties
    var isFormValid: Bool {
        return !email.isEmpty && 
               !password.isEmpty && 
               emailError == nil && 
               passwordError == nil
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    // MARK: - Initialization
    init() {
        loadSavedCredentials()
    }
    
    // MARK: - Public Methods
    func signIn() async {
        guard canSubmit else { return }
        
        // Clear previous errors
        clearErrors()
        
        // Validate form
        let validation = validationService.validateLoginForm(email: email, password: password)
        guard validation.isValid else {
            emailError = validation.emailError
            passwordError = validation.passwordError
            return
        }
        
        // Start loading
        isLoading = true
        
        do {
            // Attempt login
            try await authenticationService.login(email: email, password: password)
            
            // Save credentials for future use (optional)
            saveCredentials()
            
            // Clear form on success
            clearForm()
            
        } catch {
            // Handle login error
            handleLoginError(error)
        }
        
        // Stop loading
        isLoading = false
    }
    
    func validateEmailField() {
        let validation = validationService.validateEmail(email)
        emailError = validation.errorMessage
    }
    
    func validatePasswordField() {
        let validation = validationService.validatePassword(password)
        passwordError = validation.errorMessage
    }
    
    func togglePasswordVisibility() {
        showPassword.toggle()
    }
    
    func clearErrors() {
        emailError = nil
        passwordError = nil
        generalError = nil
    }
    
    func clearForm() {
        email = ""
        password = ""
        showPassword = false
        clearErrors()
    }
    
    // MARK: - Focus Management
    func focusEmail() {
        isEmailFocused = true
        isPasswordFocused = false
    }
    
    func focusPassword() {
        isEmailFocused = false
        isPasswordFocused = true
    }
    
    func clearFocus() {
        isEmailFocused = false
        isPasswordFocused = false
    }
    
    // MARK: - Private Methods
    private func handleLoginError(_ error: Error) {
        // Map different error types to user-friendly messages
        if let networkError = error as? URLError {
            switch networkError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                generalError = networkErrorMessage
            case .timedOut:
                generalError = "Request timed out. Please try again."
            default:
                generalError = networkErrorMessage
            }
        } else {
            // For authentication errors, show generic login failed message
            generalError = loginFailedMessage
        }
        
        print("❌ Login error: \(error.localizedDescription)")
    }
    
    private func loadSavedCredentials() {
        // Optionally load saved credentials from keychain
        if let savedCredentials = keychainService.getUserCredentials() {
            email = savedCredentials.email
            // Don't auto-fill password for security reasons
        }
    }
    
    private func saveCredentials() {
        // Optionally save credentials to keychain (email only for security)
        keychainService.saveUserCredentials(email: email, password: "")
    }
}

// MARK: - Field Validation on Change
extension LoginViewModel {
    
    func onEmailChanged(_ newValue: String) {
        email = newValue
        
        // Clear error when user starts typing
        if emailError != nil && !newValue.isEmpty {
            emailError = nil
        }
        
        // Validate on blur or when field is complete
        if !isEmailFocused && !newValue.isEmpty {
            validateEmailField()
        }
    }
    
    func onPasswordChanged(_ newValue: String) {
        password = newValue
        
        // Clear error when user starts typing
        if passwordError != nil && !newValue.isEmpty {
            passwordError = nil
        }
        
        // Validate on blur or when field is complete
        if !isPasswordFocused && !newValue.isEmpty {
            validatePasswordField()
        }
    }
    
    func onEmailFocusChanged(_ isFocused: Bool) {
        isEmailFocused = isFocused
        
        // Validate when field loses focus
        if !isFocused && !email.isEmpty {
            validateEmailField()
        }
    }
    
    func onPasswordFocusChanged(_ isFocused: Bool) {
        isPasswordFocused = isFocused
        
        // Validate when field loses focus
        if !isFocused && !password.isEmpty {
            validatePasswordField()
        }
    }
}
