//
//  VerifyCodeViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Verify Code View Model
@MainActor
class VerifyCodeViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var code: String = ""
    @Published var email: String = ""
    @Published var isLoading: Bool = false
    @Published var isCodeError: Bool = false
    @Published var showSuccessMessage: Bool = false

    // MARK: - Context
    let verificationContext: VerificationContext
    
    // MARK: - Error States
    @Published var generalError: String?
    
    // MARK: - Resend State
    @Published var canResend: Bool = true
    @Published var resendCountdown: Int = 0
    
    // MARK: - Services
    private let authenticationService = AuthenticationService.shared
    private let validationService = ValidationService.shared
    
    // MARK: - Timer
    private var resendTimer: Timer?
    
    // MARK: - Initialization
    init(context: VerificationContext) {
        self.verificationContext = context

        // Extract email from context
        switch context {
        case .signup(let email), .forgotPassword(let email), .unverifiedLogin(let email):
            self.email = email
        }
    }
    
    // MARK: - Computed Properties
    var isFormValid: Bool {
        return code.count == VerifyCodeConstants.Layout.codeLength
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    var formattedEmail: String {
        return email.isEmpty ? "<EMAIL>" : email
    }

    var contextTitle: String {
        switch verificationContext {
        case .signup:
            return "Verify Your Email"
        case .forgotPassword:
            return verifyCodeTitle
        case .unverifiedLogin:
            return "Email Verification Required"
        }
    }

    var contextSubtitle: String {
        switch verificationContext {
        case .signup:
            return "We've sent a verification code to your email address. Please enter it below to complete your registration."
        case .forgotPassword:
            return verifyCodeSubtitle
        case .unverifiedLogin:
            return "Your email address needs to be verified before you can access your account. Please enter the verification code sent to your email."
        }
    }
    
    var resendButtonText: String {
        if canResend {
            return resendCodeText
        } else {
            return "Resend code (\(resendCountdown)s)"
        }
    }
    
    // MARK: - Public Methods
    func verifyCode() async {
        guard canSubmit else { return }
        
        // Clear previous errors
        clearErrors()
        
        // Start loading
        isLoading = true
        
        do {
            // Attempt to verify code
            try await authenticationService.verifyCode(email: email, code: code)

            // Handle success based on context
            await handleVerificationSuccess()

        } catch {
            // Handle verification error
            handleVerificationError(error)
        }
        
        // Stop loading
        isLoading = false
    }
    
    func resendCode() async {
        guard canResend else { return }
        
        // Clear previous errors
        clearErrors()
        
        do {
            // Attempt to resend code
            try await authenticationService.resendVerificationCode(email: email)
            
            // Start countdown
            startResendCountdown()
            
            // Clear current code
            clearCode()
            
            // Show success feedback (optional)
            // Could show a toast or brief message
            
        } catch {
            // Handle resend error
            handleResendError(error)
        }
    }
    
    func clearCode() {
        code = ""
        isCodeError = false
    }
    
    func clearErrors() {
        generalError = nil
        isCodeError = false
    }
    
    func onCodeComplete(_ completedCode: String) {
        code = completedCode
        
        // Auto-verify when code is complete
        Task {
            await verifyCode()
        }
    }
    
    func resetSuccessState() {
        showSuccessMessage = false
        clearCode()
    }
    
    // MARK: - Private Methods
    private func handleVerificationSuccess() async {
        switch verificationContext {
        case .signup, .unverifiedLogin:
            // For signup and unverified login, user should be automatically logged in
            // The backend should return authentication tokens after successful verification
            // For now, just show success message
            showSuccessMessage = true

        case .forgotPassword:
            // For forgot password, show success and proceed to reset password
            // The success callback will handle navigation to reset password screen
            showSuccessMessage = true
        }
    }

    private func handleVerificationError(_ error: Error) {
        isCodeError = true
        
        // Map different error types to user-friendly messages
        if let networkError = error as? URLError {
            switch networkError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                generalError = networkErrorMessage
            case .timedOut:
                generalError = "Request timed out. Please try again."
            default:
                generalError = networkErrorMessage
            }
        } else {
            // For verification errors, show specific messages
            generalError = invalidCodeMessage
        }
        
        print("❌ Code verification error: \(error.localizedDescription)")
    }
    
    private func handleResendError(_ error: Error) {
        // Map different error types to user-friendly messages
        if let networkError = error as? URLError {
            switch networkError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                generalError = networkErrorMessage
            case .timedOut:
                generalError = "Request timed out. Please try again."
            default:
                generalError = networkErrorMessage
            }
        } else {
            generalError = "Unable to resend code. Please try again later."
        }
        
        print("❌ Code resend error: \(error.localizedDescription)")
    }
    
    private func startResendCountdown() {
        canResend = false
        resendCountdown = VerifyCodeConstants.Validation.resendCooldownSeconds

        resendTimer?.invalidate()
        resendTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            Task { @MainActor in
                guard let self = self else {
                    timer.invalidate()
                    return
                }

                self.resendCountdown -= 1

                if self.resendCountdown <= 0 {
                    self.canResend = true
                    self.resendCountdown = 0
                    timer.invalidate()
                }
            }
        }
    }
    
    // MARK: - Cleanup
    deinit {
        resendTimer?.invalidate()
    }
}
