//
//  SignupStep2ViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Signup Step 2 View Model
@MainActor
class SignupStep2ViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var phoneNumber: String = ""
    @Published var selectedGender: Gender?
    @Published var profileImage: UIImage?
    @Published var isLoading: Bool = false
    @Published var showSuccessPopup: Bool = false
    
    // MARK: - Error States
    @Published var phoneNumberError: String?
    @Published var genderError: String?
    @Published var generalError: String?
    
    // MARK: - Focus States
    @Published var isPhoneNumberFocused: Bool = false
    
    // MARK: - UI States
    @Published var showImagePicker: Bool = false
    @Published var showGenderPicker: Bool = false
    
    // MARK: - Step 1 Data (passed from previous step)
    let fullName: String
    let email: String
    let password: String
    
    // MARK: - Services
    private let authenticationService = AuthenticationService.shared
    private let validationService = ValidationService.shared
    
    // MARK: - Initialization
    init(fullName: String, email: String, password: String) {
        self.fullName = fullName
        self.email = email
        self.password = password
    }
    
    // MARK: - Computed Properties
    var firstName: String {
        let components = fullName.trimmingCharacters(in: .whitespacesAndNewlines).components(separatedBy: " ")
        return components.first ?? ""
    }
    
    var lastName: String {
        let components = fullName.trimmingCharacters(in: .whitespacesAndNewlines).components(separatedBy: " ")
        return components.dropFirst().joined(separator: " ")
    }
    
    var isFormValid: Bool {
        return !phoneNumber.isEmpty &&
               selectedGender != nil &&
               phoneNumberError == nil &&
               genderError == nil
    }
    
    var canSubmit: Bool {
        return isFormValid && !isLoading
    }
    
    var genderDisplayText: String {
        return selectedGender?.displayName ?? SignupConstants.Step2.Text.genderPlaceholder
    }
    
    var availableGenders: [Gender] {
        return Gender.allCases
    }
    
    // MARK: - Public Methods
    func signUp() async {
        guard canSubmit else { return }
        
        // Clear previous errors
        clearErrors()
        
        // Validate all fields
        guard validateAllFields() else { return }
        
        // Start loading
        isLoading = true
        
        do {
            // Attempt registration
            try await authenticationService.register(
                email: email,
                password: password,
                firstName: firstName,
                lastName: lastName
            )
            
            // TODO: Update profile with additional information
            // This would typically be done after successful registration
            // try await updateProfile()
            
            // Show success popup
            showSuccessPopup = true
            
        } catch {
            // Handle signup error
            handleSignupError(error)
        }
        
        // Stop loading
        isLoading = false
    }
    
    func clearErrors() {
        phoneNumberError = nil
        genderError = nil
        generalError = nil
    }
    
    func clearForm() {
        phoneNumber = ""
        selectedGender = nil
        profileImage = nil
        clearFocus()
    }
    
    func clearFocus() {
        isPhoneNumberFocused = false
    }
    
    func resetSuccessState() {
        showSuccessPopup = false
        clearForm()
    }
    
    // MARK: - Field Change Handlers
    func onPhoneNumberChanged(_ newValue: String) {
        phoneNumber = newValue
        if !newValue.isEmpty && phoneNumberError != nil {
            phoneNumberError = nil
        }
    }
    
    func onGenderSelected(_ gender: Gender) {
        selectedGender = gender
        showGenderPicker = false
        if genderError != nil {
            genderError = nil
        }
    }
    
    // MARK: - Focus Change Handlers
    func onPhoneNumberFocusChanged(_ isFocused: Bool) {
        isPhoneNumberFocused = isFocused
        if !isFocused && !phoneNumber.isEmpty {
            validatePhoneNumber()
        }
    }
    
    // MARK: - Image Picker
    func selectProfileImage() {
        showImagePicker = true
    }
    
    func onImageSelected(_ image: UIImage) {
        profileImage = image
        showImagePicker = false
    }
    
    // MARK: - Gender Picker
    func showGenderSelection() {
        showGenderPicker = true
    }
    
    // MARK: - Private Methods
    private func validateAllFields() -> Bool {
        var isValid = true
        
        // Validate phone number
        if !validatePhoneNumber() {
            isValid = false
        }
        
        // Validate gender selection
        if !validateGender() {
            isValid = false
        }
        
        return isValid
    }
    
    private func validatePhoneNumber() -> Bool {
        let validation = validationService.validatePhoneNumber(phoneNumber)
        if !validation.isValid {
            phoneNumberError = validation.errorMessage
            return false
        }
        phoneNumberError = nil
        return true
    }
    
    private func validateGender() -> Bool {
        if selectedGender == nil {
            genderError = "Please select your gender"
            return false
        }
        genderError = nil
        return true
    }
    
    private func handleSignupError(_ error: Error) {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .invalidResponse:
                generalError = "Invalid response from server"
            case .decodingError:
                generalError = "Failed to process server response"
            case .networkError(let underlyingError):
                generalError = underlyingError.localizedDescription
            case .serverError(let statusCode):
                if statusCode == 409 {
                    generalError = "An account with this email already exists"
                } else {
                    generalError = "Server error occurred"
                }
            case .clientError(let statusCode):
                if statusCode == 409 {
                    generalError = "An account with this email already exists"
                } else {
                    generalError = "Client error occurred"
                }
            case .unauthorized:
                generalError = "Authentication failed"
            case .timeout:
                generalError = "Request timed out"
            case .noInternetConnection:
                generalError = "No internet connection"
            case .invalidURL:
                generalError = "Invalid URL"
            case .invalidRequest:
                generalError = "Invalid request"
            case .unknown:
                generalError = "Unknown error occurred"
            }
        } else {
            generalError = error.localizedDescription
        }

        print("❌ Signup error: \(error)")
    }
    
    // TODO: Implement profile update after registration
    private func updateProfile() async throws {
        // This would be called after successful registration to update
        // the user's profile with phone number, gender, and profile image
        // Implementation depends on your API structure
    }
}
