//
//  SignupStep2View.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Signup Step 2 View
struct SignupStep2View: View {
    @StateObject private var viewModel: SignupStep2ViewModel
    
    let onNavigateBack: () -> Void
    let onNavigateToLogin: () -> Void
    let onNavigateToVerifyCode: ((String) -> Void)
    
    // MARK: - Initialization
    init(
        fullName: String,
        email: String,
        password: String,
        onNavigateBack: @escaping () -> Void,
        onNavigateToLogin: @escaping () -> Void,
        onNavigateToVerifyCode: @escaping ((String) -> Void)
    ) {
        self._viewModel = StateObject(wrappedValue: SignupStep2ViewModel(
            fullName: fullName,
            email: email,
            password: password
        ))
        self.onNavigateBack = onNavigateBack
        self.onNavigateToLogin = onNavigateToLogin
        self.onNavigateToVerifyCode = onNavigateToVerifyCode
    }
    
    // MARK: - Body (EXACT Figma JSON Structure)
    var body: some View {
        // Figma: Frame "Sign up" - width: 375px, height: 812px, background: #FFF
        VStack(alignment: .leading, spacing: 0) {
            // Main content container - Figma: Frame "Frame 1261151742" - padding: 0px 16px, gap: 32px
            VStack(alignment: .leading, spacing: 0) {
                // Body container - Figma: Frame "body" - gap: 28px
                VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.progressToFormSpacing) {
                    // Title + Description + Progress - Figma: Frame "title + desc" - gap: 4px
                    titleDescriptionProgressSection

                    // Form Container - Figma: Frame "Frame 1000001306" - gap: 32px
                    formContainerSection

                    // CTA Section - Figma: Frame "Frame **********" - gap: 32px, top padding: at least 28px
                    ctaSection
                        .padding(.top, 28) // At least 28px from top as specified
                }
            }
            .padding(.horizontal, SignupConstants.Step2.Layout.screenPadding)
            .padding(.top, 44) // Fixed top padding to match Figma specifications

            // Push content to top, not center
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(SignupConstants.Step2.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            viewModel.clearFocus()
        }
        .sheet(isPresented: $viewModel.showImagePicker) {
            ImagePicker(onImageSelected: viewModel.onImageSelected)
        }
        .overlay(
            // Success Popup
            Group {
                if viewModel.showSuccessPopup {
                    SuccessPopupView(
                        title: "Account Created Successfully",
                        message: "Your account has been created successfully.\nPlease verify your email to continue.",
                        buttonText: "Go to Verification",
                        onButtonTap: {
                            viewModel.resetSuccessState()
                            onNavigateToVerifyCode(viewModel.email)
                        },
                        onDismiss: {
                            viewModel.resetSuccessState()
                            onNavigateToVerifyCode(viewModel.email)
                        }
                    )
                    .transition(.opacity.combined(with: .scale))
                    .animation(.spring(response: 0.6, dampingFraction: 0.8), value: viewModel.showSuccessPopup)
                }
            }
        )

    }
    
    // MARK: - Title + Description + Progress Section (EXACT Figma Structure)
    private var titleDescriptionProgressSection: some View {
        // Figma: Frame "title + desc" - gap: 4px
        VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.titleToSubtitleSpacing) {
            // Figma: Frame "Frame 1618872389" - gap: 12px
            VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.subtitleToProgressSpacing) {
                // Figma: Frame "Frame 1618872388" - gap: 4px
                VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.titleToSubtitleSpacing) {
                    // Figma: Frame "Frame 1618872388" - gap: 6px
                    VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.titleSectionGap) {
                        // Figma: Frame "Frame 1618872390" - gap: 12px
                        HStack(alignment: .center, spacing: SignupConstants.Step2.Layout.backButtonToTitleSpacing) {
                            // Back Button: chevron-left 24px x 24px
                            Button(action: onNavigateBack) {
                                Image(systemName: "chevron.left")
                                    .font(.system(size: 20, weight: .medium))
                                    .foregroundColor(SignupConstants.Step2.Colors.titleColor)
                                    .frame(width: 24, height: 24)
                            }

                            // Title: "Complete Your Profile" - Inter 24px/600, color: #0C1523
                            Text(SignupConstants.Step2.Text.title)
                                .font(SignupConstants.Step2.Typography.titleFont)
                                .foregroundColor(SignupConstants.Step2.Colors.titleColor)
                                .accessibilityIdentifier(SignupConstants.Accessibility.step2TitleIdentifier)
                        }
                    }

                    // Subtitle: Inter 14px/400, color: #6B7280, line-height: 20px
                    Text(SignupConstants.Step2.Text.subtitle)
                        .font(SignupConstants.Step2.Typography.subtitleFont)
                        .foregroundColor(SignupConstants.Step2.Colors.subtitleColor)
                        .accessibilityIdentifier(SignupConstants.Accessibility.step2SubtitleIdentifier)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // Figma: Frame "Progress" - gap: 16px, justify-content: center
                HStack(spacing: SignupConstants.Step2.Layout.progressBarToTextSpacing) {
                    // Figma: Frame "Progress Bar" - height: 4px, border-radius: 8px, gap: 4px
                    HStack(spacing: SignupConstants.Step2.Layout.progressBarGap) {
                        // Progress Chip 1 (Active): background: #0292D9
                        Rectangle()
                            .fill(SignupConstants.Step2.Colors.progressActive)
                            .frame(height: SignupConstants.Step2.Layout.progressBarHeight)
                            .cornerRadius(SignupConstants.Step2.Layout.progressBarCornerRadius)

                        // Progress Chip 2 (Active): background: #0292D9
                        Rectangle()
                            .fill(SignupConstants.Step2.Colors.progressActive)
                            .frame(height: SignupConstants.Step2.Layout.progressBarHeight)
                            .cornerRadius(SignupConstants.Step2.Layout.progressBarCornerRadius)
                    }

                    // Progress Text: "2/2" - Inter 14px/500, color: #383A42
                    Text(SignupConstants.Step2.Text.progressText)
                        .font(SignupConstants.Step2.Typography.progressFont)
                        .foregroundColor(SignupConstants.Step2.Colors.progressTextColor)
                }
                .frame(maxWidth: .infinity, alignment: .center)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Form Container Section (EXACT Figma Structure)
    private var formContainerSection: some View {
        // Figma: Frame "Frame 1000001306" - gap: 32px
        VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.formContainerGap) {
            // Figma: Frame "Frame 1000001205" - gap: 24px
            VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.profileToFieldsSpacing) {
                // Profile Image Section - Figma: Frame "Profile Image" - 85px circle
                profileImageSection

                // Form Fields Section - Figma: Frame "Frame 1000001307" - gap: 18.753px
                formFieldsSection
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Profile Image Section (EXACT Figma Structure)
    private var profileImageSection: some View {
        // Figma: Frame "image" - 85px circle with lightgray background, gradient overlay, and padding: 6.456px
        ZStack {
            // Profile Image or Placeholder with EXACT Figma background
            if let profileImage = viewModel.profileImage {
                Image(uiImage: profileImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: SignupConstants.Step2.Layout.profileImageSize, height: SignupConstants.Step2.Layout.profileImageSize)
                    .clipShape(Circle())
            } else {
                // Figma: background: linear-gradient(0deg, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%), url(<path-to-image>) lightgray 50% / cover no-repeat
                ZStack {
                    // Base lightgray background (Figma: lightgray 50% / cover no-repeat)
                    Image("personPlaceholder")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: SignupConstants.Step2.Layout.profileImageSize, height: SignupConstants.Step2.Layout.profileImageSize) // 85 - (6.456 * 2) = 72.088
                        .foregroundColor(SignupConstants.Step2.Colors.profileImageIcon)
                }
            }

            // Camera Icon: Positioned absolutely at bottom-right of profile image (EXACT Figma positioning)
            Button(action: {
                viewModel.showImagePicker = true
            }) {
                ZStack {
                    // Camera Container: 28px circle, background: #0292D9, border: 1.167px #FFF
                    Image("camera")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 28, height: 28)
                        .foregroundColor(SignupConstants.Step2.Colors.cameraIconColor)
                }
            }
            // Adjusted positioning to align with updated personPlaceholder frame
            // Position camera icon at bottom-right edge, slightly overlapping the profile circle
            .offset(x: 28.5, y: 28.5) // Optimized positioning for 85px profile circle with updated frame
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .accessibilityIdentifier(SignupConstants.Accessibility.profileImageIdentifier)
    }

    // MARK: - Form Fields Section (EXACT Figma Structure)
    private var formFieldsSection: some View {
        // Figma: Frame "Frame 1000001307" - gap: 18.753px (rounded to 19px)
        VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.fieldSpacing) {
            // Phone Number Field - Figma: Text Input Instance
            phoneNumberFieldSection

            // Gender Field - Figma: Text Input Instance
            genderFieldSection
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Phone Number Field Section (EXACT Figma Structure)
    private var phoneNumberFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px
        VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.labelToFieldSpacing) {
            // Label: "Phone Number" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step2.Text.phoneNumberLabel)
                .font(SignupConstants.Step2.Typography.labelFont)
                .foregroundColor(SignupConstants.Step2.Colors.labelColor)

            // Phone Field: height: 48px, border-radius: 10px, padding: 8px 12px
            CustomTextField.textWithoutLabel(
                text: $viewModel.phoneNumber,
                placeholder: SignupConstants.Step2.Text.phoneNumberPlaceholder,
                isFocused: $viewModel.isPhoneNumberFocused,
                errorMessage: viewModel.phoneNumberError,
                onTextChanged: viewModel.onPhoneNumberChanged,
                onFocusChanged: viewModel.onPhoneNumberFocusChanged,
                onSubmit: {
                    Task {
                        await viewModel.signUp()
                    }
                }
            )
            .accessibilityIdentifier(SignupConstants.Accessibility.phoneNumberFieldIdentifier)
        }
    }

    // MARK: - Gender Field Section (EXACT Figma Structure)
    private var genderFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px, positioned 120px from bottom
        VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.labelToFieldSpacing) {
            // Label: "Gender" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step2.Text.genderLabel)
                .font(SignupConstants.Step2.Typography.labelFont)
                .foregroundColor(SignupConstants.Step2.Colors.labelColor)

            // Gender Picker Field: height: 48px, border-radius: 10px, padding: 8px 12px
            genderPickerField
        }
        .padding(.bottom, 120) // 120px from bottom as specified
    }

    // MARK: - CTA Section (EXACT Figma Structure)
    private var ctaSection: some View {
        // Figma: Frame "Frame **********" - gap: 32px
        VStack(alignment: .leading, spacing: 0) {
            // Figma: Frame "cta" - gap: 16px
            VStack(alignment: .leading, spacing: SignupConstants.Step2.Layout.buttonToFooterSpacing) {
                // Sign Up Button: height: 48px, border-radius: 50px, padding: 12px 20px, background: #89C226
                signUpButtonSection

                // Footer Text: "Already have an account? Sign In" - Inter 14px/500, color: #0292D9
                footerSection
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Sign Up Button Section (EXACT Figma Structure)
    private var signUpButtonSection: some View {
        // Figma: Button Instance - height: 48px, border-radius: 50px, padding: 12px 20px, background: #89C226, box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05)
        Button(action: {
            Task {
                await viewModel.signUp()
            }
        }) {
            Text(SignupConstants.Step2.Text.signUpButtonText)
                .font(SignupConstants.Step2.Typography.buttonFont)
                .foregroundColor(SignupConstants.Step2.Colors.buttonText)
                .padding(.horizontal, 20) // Figma: padding: 12px 20px
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity)
                .frame(height: SignupConstants.Step2.Layout.buttonHeight)
                .background(SignupConstants.Step2.Colors.buttonBackground)
                .cornerRadius(SignupConstants.Step2.Layout.buttonCornerRadius)
                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1) // Figma: box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05)
        }
        .disabled(viewModel.isLoading)
        .accessibilityIdentifier(SignupConstants.Accessibility.signUpButtonIdentifier)
    }

    // MARK: - Footer Section (EXACT Figma Structure - Same as Step 1)
    private var footerSection: some View {
        // Footer Text: "Already have an account? Sign In" - Mixed colors like Step 1
        HStack(spacing: 4) {
            Text(SignupConstants.Step2.Text.alreadyHaveAccountText)
                .font(SignupConstants.Step2.Typography.footerFont)
                .foregroundColor(SignupConstants.Step2.Colors.subtitleColor) // Grey color like Step 1

            Button(action: onNavigateToLogin) {
                Text(SignupConstants.Step2.Text.signInText)
                    .font(SignupConstants.Step2.Typography.footerFont)
                    .foregroundColor(SignupConstants.Step2.Colors.footerText) // Blue color #0292D9
            }
        }
        .frame(maxWidth: .infinity, alignment: .center)
        .accessibilityIdentifier(SignupConstants.Accessibility.signInLinkIdentifier)
    }




    
    // MARK: - Gender Picker Field (EXACT Figma Structure)
    private var genderPickerField: some View {
        Button(action: viewModel.showGenderSelection) {
            HStack {
                Text(viewModel.genderDisplayText)
                    .font(SignupConstants.Step2.Typography.placeholderFont)
                    .foregroundColor(viewModel.selectedGender != nil ? SignupConstants.Step2.Colors.labelColor : SignupConstants.Step2.Colors.placeholderColor)

                Spacer()

                Image(systemName: "chevron.down")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(SignupConstants.Step2.Colors.placeholderColor)
            }
            .padding(.horizontal, SignupConstants.Step2.Layout.fieldPaddingHorizontal)
            .padding(.vertical, SignupConstants.Step2.Layout.fieldPaddingVertical)
            .frame(height: SignupConstants.Step2.Layout.fieldHeight)
            .background(SignupConstants.Step2.Colors.fieldBackground)
            .overlay(
                RoundedRectangle(cornerRadius: SignupConstants.Step2.Layout.fieldCornerRadius)
                    .stroke(viewModel.genderError != nil ? Color.red : SignupConstants.Step2.Colors.fieldBorder, lineWidth: 1)
            )
        }
        .accessibilityIdentifier(SignupConstants.Accessibility.genderFieldIdentifier)
        .actionSheet(isPresented: $viewModel.showGenderPicker) {
            ActionSheet(
                title: Text("Select Gender"),
                buttons: viewModel.availableGenders.map { gender in
                    .default(Text(gender.displayName)) {
                        viewModel.onGenderSelected(gender)
                    }
                } + [.cancel()]
            )
        }
    }
    

}

// MARK: - Image Picker
struct ImagePicker: UIViewControllerRepresentable {
    let onImageSelected: (UIImage) -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = true
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(onImageSelected: onImageSelected)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let onImageSelected: (UIImage) -> Void
        
        init(onImageSelected: @escaping (UIImage) -> Void) {
            self.onImageSelected = onImageSelected
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.editedImage] as? UIImage ?? info[.originalImage] as? UIImage {
                onImageSelected(image)
            }
            picker.dismiss(animated: true)
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            picker.dismiss(animated: true)
        }
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        SignupStep2View(
            fullName: "John Doe",
            email: "<EMAIL>",
            password: "password123",
            onNavigateBack: {
                print("Navigate Back")
            },
            onNavigateToLogin: {
                print("Navigate to Login")
            },
            onNavigateToVerifyCode: { email in
                print("Navigate to Verify Code: \(email)")
            }
        )
    }
}
