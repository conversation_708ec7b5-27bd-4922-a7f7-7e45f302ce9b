//
//  ResetPasswordView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Reset Password View
struct ResetPasswordView: View {
    
    // MARK: - Navigation Callbacks
    let onNavigateBack: () -> Void
    let onResetSuccess: () -> Void
    
    // MARK: - View Model
    @StateObject private var viewModel: ResetPasswordViewModel
    
    // MARK: - Initialization
    init(
        resetToken: String,
        onNavigateBack: @escaping () -> Void,
        onResetSuccess: @escaping () -> Void
    ) {
        self.onNavigateBack = onNavigateBack
        self.onResetSuccess = onResetSuccess
        self._viewModel = StateObject(wrappedValue: ResetPasswordViewModel(resetToken: resetToken))
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Main content
            GeometryReader { geometry in
                formView(geometry: geometry)
            }
            .background(ResetPasswordConstants.Colors.screenBackground)
            .navigationBarHidden(true)
            .onTapGesture {
                // Dismiss keyboard when tapping outside
                hideKeyboard()
            }
            .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
                Button("OK") {
                    viewModel.generalError = nil
                }
            } message: {
                if let error = viewModel.generalError {
                    Text(error)
                }
            }
            
            // Success popup overlay
            if viewModel.showSuccessPopup {
                SuccessPopupView(
                    title: resetPasswordSuccessTitle,
                    message: resetPasswordSuccessMessage,
                    buttonText: resetGoToLoginText,
                    onButtonTap: {
                        viewModel.resetSuccessState()
                        onResetSuccess()
                    },
                    onDismiss: {
                        viewModel.resetSuccessState()
                        onResetSuccess()
                    }
                )
                .zIndex(1)
            }
        }
        .accessibilityElement(children: .contain)
    }
    
    // MARK: - Form View (EXACT Figma Layout - Consistent with VerifyCodeView)
    private func formView(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(spacing: 0) {
                // Header Section (includes back button like VerifyCodeView)
                headerSection

                // Main Content Container (EXACT Figma spacing)
                VStack(spacing: 0) {
                    // Spacing between subtitle and form (EXACT Figma: 36px)
                    Spacer()
                        .frame(height: ResetPasswordConstants.Layout.subtitleToFormSpacing)

                    // Form Fields Section
                    formFieldsSection

                    // Spacing between form and button (EXACT Figma: 32px)
                    Spacer()
                        .frame(height: ResetPasswordConstants.Layout.formToButtonSpacing)

                    // Reset Button Section
                    resetButtonSection
                }
                .padding(.horizontal, ResetPasswordConstants.Layout.screenPadding)
                .frame(maxWidth: .infinity)

                Spacer(minLength: 0)
            }
            .padding(.top, ResetPasswordConstants.Layout.topPadding)
            .padding(.bottom, ResetPasswordConstants.Layout.bottomPadding)
            .frame(minHeight: geometry.size.height)
        }
    }

    // MARK: - Header Section (EXACT Figma Design - Consistent with VerifyCodeView)
    private var headerSection: some View {
        VStack(alignment: .center, spacing: 0) {
            // Back Button (left-aligned, consistent with VerifyCodeView)
            HStack {
                BackButton(action: onNavigateBack)
                    .accessibilityIdentifier(ResetPasswordConstants.Accessibility.backButtonIdentifier)
                Spacer()
            }
            .padding(.horizontal, ResetPasswordConstants.Layout.screenPadding)
            .padding(.bottom, ResetPasswordConstants.Layout.backButtonToTitleSpacing)

            // Title and Subtitle Section
            titleSubtitleSection
                .padding(.horizontal, ResetPasswordConstants.Layout.screenPadding)
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - Title and Subtitle Section (EXACT Figma Design)
    private var titleSubtitleSection: some View {
        VStack(spacing: ResetPasswordConstants.Layout.titleToSubtitleSpacing) {
            // Title - "New Password" (EXACT Figma: center-aligned)
            Text(resetPasswordTitle)
                .font(.custom(ResetPasswordConstants.Typography.titleFontName, size: ResetPasswordConstants.Typography.titleSize))
                .fontWeight(ResetPasswordConstants.Typography.titleWeight)
                .foregroundColor(ResetPasswordConstants.Typography.titleColor)
                .multilineTextAlignment(.center)
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.titleIdentifier)
                .accessibilityAddTraits(.isHeader)

            // Subtitle - "Your new password..." (EXACT Figma: center-aligned, 241px width)
            Text(resetPasswordSubtitle)
                .font(.custom(ResetPasswordConstants.Typography.subtitleFontName, size: ResetPasswordConstants.Typography.subtitleSize))
                .fontWeight(ResetPasswordConstants.Typography.subtitleWeight)
                .foregroundColor(ResetPasswordConstants.Typography.subtitleColor)
                .multilineTextAlignment(.center)
                .frame(width: ResetPasswordConstants.Layout.subtitleWidth)
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.subtitleIdentifier)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        }
        .frame(maxWidth: .infinity) // Center the entire section
    }
    
    // MARK: - Form Fields Section (EXACT Figma Design)
    private var formFieldsSection: some View {
        VStack(spacing: ResetPasswordConstants.Layout.fieldSpacing) {
            // Password Field (EXACT Figma: left-aligned labels, specific spacing)
            VStack(alignment: .leading, spacing: ResetPasswordConstants.Layout.labelToFieldSpacing) {
                HStack {
                    Text(resetPasswordLabel)
                        .font(.custom(ResetPasswordConstants.Typography.labelFontName, size: ResetPasswordConstants.Typography.labelSize))
                        .fontWeight(ResetPasswordConstants.Typography.labelWeight)
                        .foregroundColor(ResetPasswordConstants.Typography.labelColor)
                    Spacer()
                }

                CustomTextField.password(
                    text: $viewModel.password,
                    isFocused: $viewModel.isPasswordFocused,
                    showPassword: $viewModel.showPassword,
                    errorMessage: viewModel.passwordError,
                    onTextChanged: viewModel.onPasswordChanged,
                    onFocusChanged: viewModel.onPasswordFocusChanged,
                    onSubmit: {
                        // Move focus to confirm password field
                        viewModel.isConfirmPasswordFocused = true
                    }
                )
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.passwordFieldIdentifier)
            }

            // Confirm Password Field (EXACT Figma: left-aligned labels, specific spacing)
            VStack(alignment: .leading, spacing: ResetPasswordConstants.Layout.labelToFieldSpacing) {
                HStack {
                    Text(resetConfirmPasswordLabel)
                        .font(.custom(ResetPasswordConstants.Typography.labelFontName, size: ResetPasswordConstants.Typography.labelSize))
                        .fontWeight(ResetPasswordConstants.Typography.labelWeight)
                        .foregroundColor(ResetPasswordConstants.Typography.labelColor)
                    Spacer()
                }

                CustomTextField.password(
                    text: $viewModel.confirmPassword,
                    isFocused: $viewModel.isConfirmPasswordFocused,
                    showPassword: $viewModel.showConfirmPassword,
                    errorMessage: viewModel.confirmPasswordError,
                    onTextChanged: viewModel.onConfirmPasswordChanged,
                    onFocusChanged: viewModel.onConfirmPasswordFocusChanged,
                    onSubmit: {
                        Task {
                            await viewModel.resetPassword()
                        }
                    }
                )
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.confirmPasswordFieldIdentifier)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading) // Ensure left alignment
    }
    
    // MARK: - Reset Button Section (EXACT Figma Design)
    private var resetButtonSection: some View {
        Button(action: {
            Task {
                await viewModel.resetPassword()
            }
        }) {
            HStack {
                if viewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: ResetPasswordConstants.Typography.buttonColor))
                        .scaleEffect(0.8)
                } else {
                    Text(createNewPasswordText)
                        .font(.custom(ResetPasswordConstants.Typography.buttonFontName, size: ResetPasswordConstants.Typography.buttonSize))
                        .fontWeight(ResetPasswordConstants.Typography.buttonWeight)
                        .foregroundColor(ResetPasswordConstants.Typography.buttonColor)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: ResetPasswordConstants.Layout.buttonHeight)
            .background(ResetPasswordConstants.Colors.primaryButtonBackground) // Always green, never grey
            .cornerRadius(ResetPasswordConstants.Layout.buttonCornerRadius)
            .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1) // EXACT: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
        }
        .disabled(!viewModel.canSubmit || viewModel.isLoading)
        .accessibilityIdentifier(ResetPasswordConstants.Accessibility.resetButtonIdentifier)
    }
    


    // MARK: - Helper Methods
    private func fieldBorderColor(isFocused: Bool, hasError: Bool) -> Color {
        if hasError {
            return .red
        } else if isFocused {
            return ResetPasswordConstants.Colors.primaryButtonBackground
        } else {
            return ResetPasswordConstants.Colors.fieldBorder
        }
    }

    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}



// MARK: - Preview
#Preview {
    NavigationView {
        ResetPasswordView(
            resetToken: "sample-token",
            onNavigateBack: {
                print("Navigate back")
            },
            onResetSuccess: {
                print("Reset success")
            }
        )
    }
}
