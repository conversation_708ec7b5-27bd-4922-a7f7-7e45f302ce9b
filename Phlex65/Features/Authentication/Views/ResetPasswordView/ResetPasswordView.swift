//
//  ResetPasswordView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Reset Password View
struct ResetPasswordView: View {
    
    // MARK: - Navigation Callbacks
    let onNavigateBack: () -> Void
    let onResetSuccess: () -> Void
    
    // MARK: - View Model
    @StateObject private var viewModel: ResetPasswordViewModel
    
    // MARK: - Initialization
    init(
        resetToken: String,
        onNavigateBack: @escaping () -> Void,
        onResetSuccess: @escaping () -> Void
    ) {
        self.onNavigateBack = onNavigateBack
        self.onResetSuccess = onResetSuccess
        self._viewModel = StateObject(wrappedValue: ResetPasswordViewModel(resetToken: resetToken))
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Main content
            GeometryReader { geometry in
                formView(geometry: geometry)
            }
            .background(ResetPasswordConstants.Colors.screenBackground)
            .navigationBarHidden(true)
            .onTapGesture {
                // Dismiss keyboard when tapping outside
                hideKeyboard()
            }
            .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
                Button("OK") {
                    viewModel.generalError = nil
                }
            } message: {
                if let error = viewModel.generalError {
                    Text(error)
                }
            }
            
            // Success popup overlay
            if viewModel.showSuccessPopup {
                SuccessPopupView(
                    title: resetPasswordSuccessTitle,
                    message: resetPasswordSuccessMessage,
                    buttonText: resetGoToLoginText,
                    onButtonTap: {
                        viewModel.resetSuccessState()
                        onResetSuccess()
                    },
                    onDismiss: {
                        viewModel.resetSuccessState()
                        onResetSuccess()
                    }
                )
                .zIndex(1)
            }
        }
        .accessibilityElement(children: .contain)
    }
    
    // MARK: - Form View
    private func formView(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                // Header Section
                headerSection
                
                // Form Section
                formSection
                
                // Reset Button
                resetButtonSection
                
                Spacer(minLength: 0)
            }
            .padding(.horizontal, ResetPasswordConstants.Layout.screenPadding)
            .padding(.top, ResetPasswordConstants.Layout.topPadding)
            .padding(.bottom, ResetPasswordConstants.Layout.bottomPadding)
            .frame(minHeight: geometry.size.height)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Back Button
            BackButton(
                action: onNavigateBack
            )
            .accessibilityIdentifier(ResetPasswordConstants.Accessibility.backButtonIdentifier)
            .padding(.bottom, 32)
            
            // Title
            Text(resetPasswordTitle)
                .font(.custom(ResetPasswordConstants.Typography.titleFontName, size: ResetPasswordConstants.Typography.titleSize))
                .fontWeight(ResetPasswordConstants.Typography.titleWeight)
                .foregroundColor(ResetPasswordConstants.Typography.titleColor)
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.titleIdentifier)
                .accessibilityAddTraits(.isHeader)
                .padding(.bottom, ResetPasswordConstants.Layout.titleToSubtitleSpacing)
            
            // Subtitle
            Text(resetPasswordSubtitle)
                .font(.custom(ResetPasswordConstants.Typography.subtitleFontName, size: ResetPasswordConstants.Typography.subtitleSize))
                .fontWeight(ResetPasswordConstants.Typography.subtitleWeight)
                .foregroundColor(ResetPasswordConstants.Typography.subtitleColor)
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.subtitleIdentifier)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: ResetPasswordConstants.Layout.fieldSpacing) {
            // Password Field
            VStack(alignment: .leading, spacing: ResetPasswordConstants.Layout.labelToFieldSpacing) {
                Text(resetPasswordLabel)
                    .font(.custom(ResetPasswordConstants.Typography.labelFontName, size: ResetPasswordConstants.Typography.labelSize))
                    .fontWeight(ResetPasswordConstants.Typography.labelWeight)
                    .foregroundColor(ResetPasswordConstants.Typography.labelColor)
                
                CustomTextField.password(
                    text: $viewModel.password,
                    isFocused: $viewModel.isPasswordFocused,
                    showPassword: $viewModel.showPassword,
                    errorMessage: viewModel.passwordError,
                    onTextChanged: viewModel.onPasswordChanged,
                    onFocusChanged: viewModel.onPasswordFocusChanged,
                    onSubmit: {
                        // Move focus to confirm password field
                        viewModel.isConfirmPasswordFocused = true
                    }
                )
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.passwordFieldIdentifier)
            }
            
            // Confirm Password Field
            VStack(alignment: .leading, spacing: ResetPasswordConstants.Layout.labelToFieldSpacing) {
                Text(resetConfirmPasswordLabel)
                    .font(.custom(ResetPasswordConstants.Typography.labelFontName, size: ResetPasswordConstants.Typography.labelSize))
                    .fontWeight(ResetPasswordConstants.Typography.labelWeight)
                    .foregroundColor(ResetPasswordConstants.Typography.labelColor)
                
                CustomTextField.password(
                    text: $viewModel.confirmPassword,
                    isFocused: $viewModel.isConfirmPasswordFocused,
                    showPassword: $viewModel.showConfirmPassword,
                    errorMessage: viewModel.confirmPasswordError,
                    onTextChanged: viewModel.onConfirmPasswordChanged,
                    onFocusChanged: viewModel.onConfirmPasswordFocusChanged,
                    onSubmit: {
                        Task {
                            await viewModel.resetPassword()
                        }
                    }
                )
                .accessibilityIdentifier(ResetPasswordConstants.Accessibility.confirmPasswordFieldIdentifier)
            }
        }
        .padding(.top, ResetPasswordConstants.Layout.subtitleToFormSpacing)
    }
    
    // MARK: - Reset Button Section
    private var resetButtonSection: some View {
        LoginButton(
            title: createNewPasswordText,
            isLoading: viewModel.isLoading,
            isEnabled: viewModel.canSubmit,
            action: {
                Task {
                    await viewModel.resetPassword()
                }
            }
        )
        .accessibilityIdentifier(ResetPasswordConstants.Accessibility.resetButtonIdentifier)
        .padding(.top, ResetPasswordConstants.Layout.fieldToButtonSpacing)
    }
    
    // MARK: - Helper Methods
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        ResetPasswordView(
            resetToken: "sample-token",
            onNavigateBack: {
                print("Navigate back")
            },
            onResetSuccess: {
                print("Reset success")
            }
        )
    }
}
