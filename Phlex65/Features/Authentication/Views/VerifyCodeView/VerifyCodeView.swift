//
//  VerifyCodeView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Verify Code View
struct VerifyCodeView: View {
    
    // MARK: - Navigation Callbacks
    let email: String
    let onNavigateBack: () -> Void
    let onVerificationSuccess: () -> Void
    
    // MARK: - View Model
    @StateObject private var viewModel: VerifyCodeViewModel

    // MARK: - Initialization
    init(
        context: VerificationContext,
        onNavigateBack: @escaping () -> Void,
        onVerificationSuccess: @escaping () -> Void
    ) {
        self.email = {
            switch context {
            case .signup(let email), .forgotPassword(let email), .unverifiedLogin(let email):
                return email
            }
        }()
        self.onNavigateBack = onNavigateBack
        self.onVerificationSuccess = onVerificationSuccess
        self._viewModel = StateObject(wrappedValue: VerifyCodeViewModel(context: context))
    }
    
    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            if viewModel.showSuccessMessage {
                // Success State
                successView
            } else {
                // Form State
                formView(geometry: geometry)
            }
        }
        .background(VerifyCodeConstants.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            // Dismiss keyboard when tapping outside
            hideKeyboard()
        }
        .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
            Button("OK") {
                viewModel.generalError = nil
            }
        } message: {
            if let error = viewModel.generalError {
                Text(error)
            }
        }
        .accessibilityElement(children: .contain)
    }

    // MARK: - Form View
    private func formView(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                // Header Section
                headerSection

                // Code Input Section
                codeInputSection

                // Resend Section
                resendSection

                // Verify Button
                verifyButtonSection

                Spacer(minLength: 0)
            }
            .padding(.horizontal, VerifyCodeConstants.Layout.screenPadding)
            .padding(.top, VerifyCodeConstants.Layout.topPadding)
            .padding(.bottom, VerifyCodeConstants.Layout.bottomPadding)
            .frame(minHeight: geometry.size.height)
        }
    }

    // MARK: - Header Section (EXACT Figma Design)
    private var headerSection: some View {
        VStack(alignment: .center, spacing: 0) {
            // Back Button (left-aligned)
            HStack {
                BackButton(action: onNavigateBack)
                Spacer()
            }
            .padding(.bottom, VerifyCodeConstants.Layout.backButtonToTitleSpacing)

            // Title and Subtitle (center-aligned as per Figma)
            VStack(alignment: .center, spacing: VerifyCodeConstants.Layout.titleToSubtitleSpacing) {
                // Title - "Verify Code" (center-aligned)
                Text(viewModel.contextTitle)
                    .font(.custom(VerifyCodeConstants.Typography.titleFontName, size: VerifyCodeConstants.Typography.titleSize))
                    .fontWeight(VerifyCodeConstants.Typography.titleWeight)
                    .foregroundColor(VerifyCodeConstants.Typography.titleColor)
                    .multilineTextAlignment(.center)
                    .accessibilityIdentifier(VerifyCodeConstants.Accessibility.titleIdentifier)
                    .accessibilityAddTraits(.isHeader)

                // Subtitle (center-aligned)
                subtitleView
            }
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - Subtitle View (EXACT Figma Design with Mixed Colors)
    private var subtitleView: some View {
        // EXACT Figma: "Please enter the code we just sent <NAME_EMAIL>"
        // Mixed colors: grey text + blue email
        (Text("Please enter the code we just sent to email ") +
         Text(viewModel.formattedEmail)
            .foregroundColor(VerifyCodeConstants.Typography.emailHighlightColor))
            .font(.custom(VerifyCodeConstants.Typography.subtitleFontName, size: VerifyCodeConstants.Typography.subtitleSize))
            .fontWeight(VerifyCodeConstants.Typography.subtitleWeight)
            .foregroundColor(VerifyCodeConstants.Typography.subtitleColor)
            .multilineTextAlignment(.center)
            .frame(width: VerifyCodeConstants.Layout.subtitleWidth)
            .accessibilityIdentifier(VerifyCodeConstants.Accessibility.subtitleIdentifier)
    }

    // MARK: - Code Input Section (EXACT Figma Design)
    private var codeInputSection: some View {
        VStack(spacing: 0) {
            OTPInputField(
                code: $viewModel.code,
                isError: $viewModel.isCodeError,
                codeLength: VerifyCodeConstants.Layout.codeLength,
                onCodeComplete: viewModel.onCodeComplete
            )
            .accessibilityIdentifier(VerifyCodeConstants.Accessibility.codeFieldIdentifier)
        }
        .frame(maxWidth: .infinity) // Center the code input fields
        .padding(.top, VerifyCodeConstants.Layout.subtitleToCodeSpacing)
    }

    // MARK: - Resend Section (EXACT Figma Design)
    private var resendSection: some View {
        VStack(spacing: 4) { // EXACT Figma: gap: "4px"
            Text(didntReceiveOTPText)
                .font(.custom(VerifyCodeConstants.Typography.resendQuestionFontName, size: VerifyCodeConstants.Typography.resendQuestionSize))
                .fontWeight(VerifyCodeConstants.Typography.resendQuestionWeight)
                .foregroundColor(VerifyCodeConstants.Typography.resendQuestionColor)
                .multilineTextAlignment(.center)

            Button(action: {
                Task {
                    await viewModel.resendCode()
                }
            }) {
                Text(viewModel.resendButtonText)
                    .font(.custom(VerifyCodeConstants.Typography.resendLinkFontName, size: VerifyCodeConstants.Typography.resendLinkSize))
                    .fontWeight(VerifyCodeConstants.Typography.resendLinkWeight)
                    .foregroundColor(viewModel.canResend ? VerifyCodeConstants.Typography.resendLinkColor : VerifyCodeConstants.Colors.secondaryText)
            }
            .disabled(!viewModel.canResend)
            .accessibilityIdentifier(VerifyCodeConstants.Accessibility.resendButtonIdentifier)
        }
        .frame(maxWidth: .infinity) // Center the section
        .padding(.top, VerifyCodeConstants.Layout.codeToResendSpacing)
    }

    // MARK: - Verify Button Section
    private var verifyButtonSection: some View {
        LoginButton(
            title: verifyButtonText,
            isLoading: viewModel.isLoading,
            isEnabled: viewModel.canSubmit,
            action: {
                Task {
                    await viewModel.verifyCode()
                }
            }
        )
        .accessibilityIdentifier(VerifyCodeConstants.Accessibility.verifyButtonIdentifier)
        .padding(.top, VerifyCodeConstants.Layout.resendToButtonSpacing)
    }

    // MARK: - Success View
    private var successView: some View {
        VStack {
            Spacer()

            SuccessMessageView(
                title: "Code Verified!",
                message: "Your verification code has been confirmed successfully.",
                buttonText: "Continue",
                onButtonTap: {
                    viewModel.resetSuccessState()
                    onVerificationSuccess()
                }
            )

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(VerifyCodeConstants.Colors.screenBackground)
    }

    // MARK: - Helper Methods
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        VerifyCodeView(
            context: .signup(email: "<EMAIL>"),
            onNavigateBack: {
                print("Navigate back")
            },
            onVerificationSuccess: {
                print("Verification success")
            }
        )
    }
}
