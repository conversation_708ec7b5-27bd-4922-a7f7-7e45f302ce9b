//
//  RegisterView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Register View
struct RegisterView: View {
    let onNavigateToLogin: () -> Void
    let onNavigateToVerifyCode: ((String) -> Void)?

    // MARK: - Initialization
    init(
        onNavigateToLogin: @escaping () -> Void,
        onNavigateToVerifyCode: ((String) -> Void)? = nil
    ) {
        self.onNavigateToLogin = onNavigateToLogin
        self.onNavigateToVerifyCode = onNavigateToVerifyCode
    }

    var body: some View {
        VStack {
            Text("Register Screen")
                .font(AppTheme.Typography.headlineLarge)

            Button("Back to Login") {
                onNavigateToLogin()
            }
            .buttonStyle(PrimaryButtonStyle())
            .padding()

            // Temporary button to test verification flow
            But<PERSON>("Test Verify Code") {
                onNavigateToVerifyCode?("<EMAIL>")
            }
            .buttonStyle(PrimaryButtonStyle())
            .padding()
        }
        .padding()
        .navigationTitle("Register")
    }
}

// MARK: - Preview
#Preview {
    RegisterView(
        onNavigateToLogin: {}
    )
}
