//
//  SignupStep1View.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Signup Step 1 View
struct SignupStep1View: View {
    @StateObject private var viewModel = SignupStep1ViewModel()
    
    let onNavigateToLogin: () -> Void
    let onNavigateToStep2: ((String, String, String) -> Void)
    
    // MARK: - Body (EXACT Figma JSON Structure - SAME AS STEP 2)
    var body: some View {
        // Figma: Frame "Sign up" - width: 375px, height: 812px, background: #FFF
        VStack(spacing: 0) {
            // Main content area (separate from CTA)
            VStack(alignment: .leading, spacing: 0) {
                // Title + Description + Progress - Figma: Frame "title + desc" - gap: 4px
                titleDescriptionProgressSection
                    .padding(.bottom, 32) // Increased spacing after header

                // Form List - Figma: Frame "list" - gap: 14px (includes checkbox)
                formListSection
            }
            .padding(.horizontal, SignupConstants.Step1.Layout.screenPadding)
            .padding(.top, 16) // Increased top padding

            Spacer()

            // CTA Section - separate from form with bottom padding (SAME AS STEP 2)
            ctaSection
                .padding(.horizontal, SignupConstants.Step1.Layout.screenPadding)
                .padding(.bottom, 32) // Bottom padding for proper spacing from screen edge
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(SignupConstants.Step1.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            viewModel.clearFocus()
        }
        .onChange(of: viewModel.shouldNavigateToStep2) { shouldNavigate in
            print("📱 SignupStep1View: shouldNavigateToStep2 changed to: \(shouldNavigate)")
            if shouldNavigate {
                print("🚀 SignupStep1View: Calling onNavigateToStep2 with data:")
                print("   - fullName: '\(viewModel.fullName)'")
                print("   - email: '\(viewModel.email)'")
                print("   - password: '\(viewModel.password)'")
                onNavigateToStep2(viewModel.fullName, viewModel.email, viewModel.password)
                viewModel.shouldNavigateToStep2 = false
                print("✅ SignupStep1View: Navigation callback completed")
            }
        }
    }
    
    // MARK: - Title + Description + Progress Section (EXACT Figma Structure)
    private var titleDescriptionProgressSection: some View {
        // Figma: Frame "title + desc" - gap: 4px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.titleToSubtitleSpacing) {
            // Figma: Frame "Frame **********" - gap: 12px
            VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.subtitleToProgressSpacing) {
                // Figma: Frame "Frame **********" - gap: 4px
                VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.titleToSubtitleSpacing) {
                    // Title: "Create Account" - Inter 24px/600, color: #0C1523
                    Text(SignupConstants.Step1.Text.title)
                        .font(SignupConstants.Step1.Typography.titleFont)
                        .foregroundColor(SignupConstants.Step1.Colors.titleColor)
                        .accessibilityIdentifier(SignupConstants.Accessibility.step1TitleIdentifier)

                    // Subtitle: Inter 14px/400, color: #6B7280, line-height: 20px
                    Text(SignupConstants.Step1.Text.subtitle)
                        .font(SignupConstants.Step1.Typography.subtitleFont)
                        .foregroundColor(SignupConstants.Step1.Colors.subtitleColor)
                        .accessibilityIdentifier(SignupConstants.Accessibility.step1SubtitleIdentifier)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }

                // Figma: Frame "Progress" - gap: 16px, justify-content: center
                HStack(spacing: SignupConstants.Step1.Layout.progressBarToTextSpacing) {
                    // Figma: Frame "Progress Bar" - height: 4px, border-radius: 8px, background: #FFF
                    HStack(spacing: 4) {
                        // Progress Chip 1 (Active): background: #0292D9
                        Rectangle()
                            .fill(SignupConstants.Step1.Colors.progressActive)
                            .frame(height: SignupConstants.Step1.Layout.progressBarHeight)
                            .cornerRadius(SignupConstants.Step1.Layout.progressBarCornerRadius)

                        // Progress Chip 2 (Inactive): background: #EDEDED
                        Rectangle()
                            .fill(SignupConstants.Step1.Colors.progressInactive)
                            .frame(height: SignupConstants.Step1.Layout.progressBarHeight)
                            .cornerRadius(SignupConstants.Step1.Layout.progressBarCornerRadius)
                    }

                    // Progress Text: "1/2" - Inter 14px/500, color: #383A42
                    Text(SignupConstants.Step1.Text.progressText)
                        .font(SignupConstants.Step1.Typography.progressFont)
                        .foregroundColor(SignupConstants.Step1.Colors.progressTextColor)
                }
                .frame(maxWidth: .infinity, alignment: .center)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Form List Section (EXACT Figma Structure)
    private var formListSection: some View {
        // Figma: Frame "list" - gap: 14px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.fieldSpacing) {
            // Full Name Field - Figma: Text Input Instance
            fullNameFieldSection

            // Email Field - Figma: Frame "Frame 1618872156" - gap: 12px
            emailFieldSection

            // Password Field - Figma: Text Input Instance
            passwordFieldSection

            // Confirm Password Field - Figma: Text Input Instance
            confirmPasswordFieldSection

            // Terms Checkbox - Figma: Frame "Frame 1618872168" - gap: 6px (PART OF LIST)
            termsCheckboxSection
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Full Name Field (EXACT Figma Structure - NO DOUBLE LABELS)
    private var fullNameFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.labelToFieldSpacing) {
            // Label: "Full Name" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step1.Text.fullNameLabel)
                .font(SignupConstants.Step1.Typography.labelFont)
                .foregroundColor(SignupConstants.Step1.Colors.labelColor)

            // Input Field: height 48px, padding 8px 12px, border-radius 10px (NO LABEL in CustomTextField)
            CustomTextField.textWithoutLabel(
                text: $viewModel.fullName,
                placeholder: SignupConstants.Step1.Text.fullNamePlaceholder,
                isFocused: $viewModel.isFullNameFocused,
                errorMessage: viewModel.fullNameError,
                onTextChanged: viewModel.onFullNameChanged,
                onFocusChanged: viewModel.onFullNameFocusChanged,
                onSubmit: { viewModel.isEmailFocused = true }
            )
            .accessibilityIdentifier(SignupConstants.Accessibility.fullNameFieldIdentifier)
        }
    }

    // MARK: - Email Field (EXACT Figma Structure - NO DOUBLE LABELS)
    private var emailFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.labelToFieldSpacing) {
            // Label: "Email Address" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step1.Text.emailLabel)
                .font(SignupConstants.Step1.Typography.labelFont)
                .foregroundColor(SignupConstants.Step1.Colors.labelColor)

            // Input Field: height 48px, padding 8px 12px, border-radius 10px (NO LABEL in CustomTextField)
            CustomTextField.emailWithoutLabel(
                text: $viewModel.email,
                placeholder: SignupConstants.Step1.Text.emailPlaceholder,
                isFocused: $viewModel.isEmailFocused,
                errorMessage: viewModel.emailError,
                onTextChanged: viewModel.onEmailChanged,
                onFocusChanged: viewModel.onEmailFocusChanged,
                onSubmit: { viewModel.isPasswordFocused = true }
            )
            .accessibilityIdentifier(SignupConstants.Accessibility.emailFieldIdentifier)
        }
    }

    // MARK: - Password Field (EXACT Figma Structure - NO DOUBLE LABELS)
    private var passwordFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.labelToFieldSpacing) {
            // Label: "Password" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step1.Text.passwordLabel)
                .font(SignupConstants.Step1.Typography.labelFont)
                .foregroundColor(SignupConstants.Step1.Colors.labelColor)

            // Input Field: height 48px, padding 8px 12px, border-radius 10px (NO LABEL in CustomTextField)
            CustomTextField.passwordWithoutLabel(
                text: $viewModel.password,
                placeholder: SignupConstants.Step1.Text.passwordPlaceholder,
                isFocused: $viewModel.isPasswordFocused,
                showPassword: $viewModel.showPassword,
                errorMessage: viewModel.passwordError,
                onTextChanged: viewModel.onPasswordChanged,
                onFocusChanged: viewModel.onPasswordFocusChanged,
                onSubmit: { viewModel.isConfirmPasswordFocused = true }
            )
            .accessibilityIdentifier(SignupConstants.Accessibility.passwordFieldIdentifier)
        }
    }

    // MARK: - Confirm Password Field (EXACT Figma Structure - NO DOUBLE LABELS)
    private var confirmPasswordFieldSection: some View {
        // Figma: Text Input Instance - gap: 6px
        VStack(alignment: .leading, spacing: SignupConstants.Step1.Layout.labelToFieldSpacing) {
            // Label: "Confirm Password" - Inter 14px/500, color: #101828
            Text(SignupConstants.Step1.Text.confirmPasswordLabel)
                .font(SignupConstants.Step1.Typography.labelFont)
                .foregroundColor(SignupConstants.Step1.Colors.labelColor)

            // Input Field: height 48px, padding 8px 12px, border-radius 10px (NO LABEL in CustomTextField)
            CustomTextField.passwordWithoutLabel(
                text: $viewModel.confirmPassword,
                placeholder: SignupConstants.Step1.Text.confirmPasswordPlaceholder,
                isFocused: $viewModel.isConfirmPasswordFocused,
                showPassword: $viewModel.showConfirmPassword,
                errorMessage: viewModel.confirmPasswordError,
                onTextChanged: viewModel.onConfirmPasswordChanged,
                onFocusChanged: viewModel.onConfirmPasswordFocusChanged,
                onSubmit: { viewModel.proceedToStep2() }
            )
            .accessibilityIdentifier(SignupConstants.Accessibility.confirmPasswordFieldIdentifier)
        }
    }
    
    // MARK: - Terms Checkbox Section (EXACT Figma Structure)
    private var termsCheckboxSection: some View {
        // Figma: Frame "Frame 1618872168" - gap: 6px, align-items: center
        HStack(alignment: .center, spacing: SignupConstants.Step1.Layout.checkboxToTextSpacing) {
            // Checkbox: EXACT 24px x 24px as per Figma JSON, justify-content: center, align-items: center
            Button(action: viewModel.toggleTermsAgreement) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(viewModel.agreeToTerms ? SignupConstants.Step1.Colors.progressActive : Color.clear)
                    .frame(width: 24, height: 24) // EXACT Figma JSON: 24px x 24px
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(viewModel.agreeToTerms ? SignupConstants.Step1.Colors.progressActive : SignupConstants.Step1.Colors.fieldBorder, lineWidth: 1)
                    )
                    .overlay(
                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                            .opacity(viewModel.agreeToTerms ? 1 : 0)
                    )
            }
            .accessibilityIdentifier(SignupConstants.Accessibility.termsCheckboxIdentifier)

            // Terms Text: "Agree with Terms & Condition" - Inter 12px/500, line-height: 18px, color: #25272C
            Text(SignupConstants.Step1.Text.termsText)
                .font(SignupConstants.Step1.Typography.termsFont)
                .foregroundColor(SignupConstants.Step1.Colors.termsTextColor)

            Spacer()
        }
        // NO EXTRA PADDING - this is part of the list with 14px gap from previous field
    }

    // MARK: - CTA Section (EXACT Figma Structure - SAME AS STEP 2)
    private var ctaSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Next Button
            nextButtonSection

            // Footer Text
            footerSection
        }
    }

    // MARK: - Next Button (EXACT Figma Structure)
    private var nextButtonSection: some View {
        // Figma: Button Instance - height 48px, border-radius 50px, padding 12px 20px, background: #89C226
        Button(action: {
            print("Next button tapped - canSubmit: \(viewModel.canSubmit)")
            viewModel.proceedToStep2()
        }) {
            Text(SignupConstants.Step1.Text.nextButtonText)
                .font(SignupConstants.Step1.Typography.buttonFont)
                .foregroundColor(SignupConstants.Step1.Colors.buttonText)
                .frame(maxWidth: .infinity)
                .frame(height: SignupConstants.Step1.Layout.buttonHeight)
                .background(viewModel.canSubmit ? SignupConstants.Step1.Colors.buttonBackground : Color.gray.opacity(0.5))
                .cornerRadius(SignupConstants.Step1.Layout.buttonCornerRadius)
        }
        .disabled(!viewModel.canSubmit)
        .accessibilityIdentifier(SignupConstants.Accessibility.nextButtonIdentifier)
    }

    // MARK: - Footer Section (EXACT Figma Structure)
    private var footerSection: some View {
        // Footer Text: "Already have an account?" (grey) + "Sign In" (blue) - Inter 14px/500, text-align: center
        Button(action: onNavigateToLogin) {
            HStack(spacing: 4) {
                Text("Already  have an account?")
                    .font(SignupConstants.Step1.Typography.footerFont)
                    .foregroundColor(SignupConstants.Step1.Colors.subtitleColor) // Grey color

                Text("Sign In")
                    .font(SignupConstants.Step1.Typography.footerFont)
                    .foregroundColor(SignupConstants.Step1.Colors.footerText) // Blue color #0292D9
            }
            .frame(maxWidth: .infinity, alignment: .center)
        }
        .accessibilityIdentifier(SignupConstants.Accessibility.signInLinkIdentifier)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        SignupStep1View(
            onNavigateToLogin: {
                print("Navigate to Login")
            },
            onNavigateToStep2: { fullName, email, password in
                print("Navigate to Step 2: \(fullName), \(email)")
            }
        )
    }
}
