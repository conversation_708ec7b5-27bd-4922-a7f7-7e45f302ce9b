//
//  ForgotPasswordView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Forgot Password View
struct ForgotPasswordView: View {

    // MARK: - Navigation Callbacks
    let onNavigateToLogin: () -> Void
    let onNavigateToVerifyCode: ((String) -> Void)?

    // MARK: - View Model
    @StateObject private var viewModel = ForgotPasswordViewModel()

    // MARK: - Initialization
    init(
        onNavigateToLogin: @escaping () -> Void,
        onNavigateToVerifyCode: ((String) -> Void)? = nil
    ) {
        self.onNavigateToLogin = onNavigateToLogin
        self.onNavigateToVerifyCode = onNavigateToVerifyCode
    }

    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            if viewModel.showSuccessMessage {
                // Success State
                successView
            } else {
                // Form State
                formView(geometry: geometry)
            }
        }
        .background(ForgotPasswordConstants.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            // Dismiss keyboard when tapping outside
            viewModel.clearFocus()
        }
        .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
            Button("OK") {
                viewModel.generalError = nil
            }
        } message: {
            if let error = viewModel.generalError {
                Text(error)
            }
        }
        .accessibilityElement(children: .contain)
        .onChange(of: viewModel.shouldNavigateToVerifyCode) { shouldNavigate in
            if shouldNavigate {
                onNavigateToVerifyCode?(viewModel.email)
                viewModel.shouldNavigateToVerifyCode = false
            }
        }
    }

    // MARK: - Form View
    private func formView(geometry: GeometryProxy) -> some View {
        ScrollView {
            VStack(alignment: .center, spacing: ForgotPasswordConstants.Layout.headerToFormSpacing) { // EXACT: main container "gap": "32px"
                // Header Section - CENTER ALIGNED as per Figma
                headerSection

                // Form Section - CENTER ALIGNED as per Figma
                formSection

                Spacer(minLength: 0)
            }
            .padding(.horizontal, ForgotPasswordConstants.Layout.screenPadding) // EXACT: "padding": "0px 16px"
            .padding(.top, ForgotPasswordConstants.Layout.topPadding)
            .padding(.bottom, ForgotPasswordConstants.Layout.bottomPadding)
            .frame(minHeight: geometry.size.height)
            .frame(maxWidth: .infinity, alignment: .center) // EXACT: "align-items": "center"
        }
    }

    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .center, spacing: 0) {
            // Back Button - positioned at start but content is center aligned
            HStack {
                BackButton(action: onNavigateToLogin)
                Spacer()
            }
            .padding(.bottom, ForgotPasswordConstants.Layout.backButtonToTitleSpacing)

            // Title - CENTER ALIGNED as per Figma
            Text(forgotPasswordTitle)
                .font(.custom(ForgotPasswordConstants.Typography.titleFontName, size: ForgotPasswordConstants.Typography.titleSize))
                .fontWeight(ForgotPasswordConstants.Typography.titleWeight)
                .foregroundColor(ForgotPasswordConstants.Typography.titleColor)
                .multilineTextAlignment(.center) // Figma: text-align: center
                .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.titleIdentifier)
                .accessibilityAddTraits(.isHeader)
                .padding(.bottom, ForgotPasswordConstants.Layout.titleToSubtitleSpacing)

            // Subtitle - CENTER ALIGNED with 260px width as per Figma
            Text(forgotPasswordSubtitle)
                .font(.custom(ForgotPasswordConstants.Typography.subtitleFontName, size: ForgotPasswordConstants.Typography.subtitleSize))
                .fontWeight(ForgotPasswordConstants.Typography.subtitleWeight)
                .foregroundColor(ForgotPasswordConstants.Typography.subtitleColor)
                .multilineTextAlignment(.center) // Figma: text-align: center
                .frame(width: ForgotPasswordConstants.Layout.subtitleWidth) // Figma: width: 260px
                .lineLimit(nil)
                .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.subtitleIdentifier)
        }
        .frame(maxWidth: .infinity, alignment: .center) // CENTER ALIGNED as per Figma
    }

    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: ForgotPasswordConstants.Layout.fieldToButtonSpacing) { // EXACT: form section "gap": "12px"
            // Email Field
            CustomTextField.email(
                text: $viewModel.email,
                isFocused: $viewModel.isEmailFocused,
                errorMessage: viewModel.emailError,
                onTextChanged: viewModel.onEmailChanged,
                onFocusChanged: viewModel.onEmailFocusChanged,
                onSubmit: {
                    Task {
                        await viewModel.sendResetLink()
                    }
                }
            )
            .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.emailFieldIdentifier)

            // Send Reset Link Button - EXACT: 12px gap from field
            LoginButton(
                title: sendResetLinkText,
                isLoading: viewModel.isLoading,
                isEnabled: viewModel.canSubmit,
                action: {
                    Task {
                        await viewModel.sendResetLink()
                    }
                }
            )
            .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.sendResetButtonIdentifier)
        }
        .frame(maxWidth: .infinity) // Full width for form elements
    }

    // MARK: - Success View
    private var successView: some View {
        VStack {
            Spacer()

            SuccessMessageView(
                title: resetLinkSentTitle,
                message: resetLinkSentMessage,
                buttonText: backToLoginText,
                onButtonTap: {
                    viewModel.resetSuccessState()
                    onNavigateToLogin()
                }
            )

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ForgotPasswordConstants.Colors.screenBackground)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        ForgotPasswordView(
            onNavigateToLogin: {
                print("Navigate to Login")
            }
        )
    }
}
