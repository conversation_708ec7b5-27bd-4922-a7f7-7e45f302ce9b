//
//  ForgotPasswordView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Forgot Password View
struct ForgotPasswordView: View {

    // MARK: - Navigation Callbacks
    let onNavigateToLogin: () -> Void
    let onNavigateToVerifyCode: ((String) -> Void)?

    // MARK: - View Model
    @StateObject private var viewModel = ForgotPasswordViewModel()

    // MARK: - Initialization
    init(
        onNavigateToLogin: @escaping () -> Void,
        onNavigateToVerifyCode: ((String) -> Void)? = nil
    ) {
        self.onNavigateToLogin = onNavigateToLogin
        self.onNavigateToVerifyCode = onNavigateToVerifyCode
    }

    // MARK: - Body
    var body: some View {
        GeometryReader { geometry in
            if viewModel.showSuccessMessage {
                // Success State
                successView
            } else {
                // Form State
                formView(geometry: geometry)
            }
        }
        .background(ForgotPasswordConstants.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            // Dismiss keyboard when tapping outside
            viewModel.clearFocus()
        }
        .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
            Button("OK") {
                viewModel.generalError = nil
            }
        } message: {
            if let error = viewModel.generalError {
                Text(error)
            }
        }
        .accessibilityElement(children: .contain)
        .onChange(of: viewModel.shouldNavigateToVerifyCode) { shouldNavigate in
            if shouldNavigate {
                onNavigateToVerifyCode?(viewModel.email)
                viewModel.shouldNavigateToVerifyCode = false
            }
        }
    }

    // MARK: - Form View
    private func formView(geometry: GeometryProxy) -> some View {
        VStack(alignment: .leading, spacing: 0) {
            // Back Button - positioned at top left (matching Figma)
            HStack {
                BackButton(action: onNavigateToLogin)
                Spacer()
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)

            // Main Content - positioned from top (matching Figma layout)
            VStack(alignment: .center, spacing: 0) {
                // Title and Subtitle Section
                titleAndSubtitleSection
                    .padding(.top, 60) // Space from back button to title

                // Form Section (Email field + Button)
                formSection
                    .padding(.top, 32) // Space from subtitle to form

                Spacer()
            }
            .padding(.horizontal, 16)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
        .background(Color.white)
    }

    // MARK: - Title and Subtitle Section
    private var titleAndSubtitleSection: some View {
        VStack(alignment: .center, spacing: 8) {
            // Main Title - "Forgot Password" (matching Figma design)
            Text("Forgot Password")
                .font(.custom(ForgotPasswordConstants.Typography.titleFontName, size: ForgotPasswordConstants.Typography.titleSize))
                .fontWeight(ForgotPasswordConstants.Typography.titleWeight)
                .foregroundColor(ForgotPasswordConstants.Typography.titleColor)
                .multilineTextAlignment(.center)

            // Subtitle - Two lines of text (matching Figma design)
            Text("Enter your registered email to receive a\npassword reset link")
                .font(.custom(ForgotPasswordConstants.Typography.subtitleFontName, size: ForgotPasswordConstants.Typography.subtitleSize))
                .fontWeight(ForgotPasswordConstants.Typography.subtitleWeight)
                .foregroundColor(ForgotPasswordConstants.Typography.subtitleColor)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - Form Section
    private var formSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Email Label (matching Figma design)
            Text("Email Address")
                .font(.custom(ForgotPasswordConstants.Typography.labelFontName, size: ForgotPasswordConstants.Typography.labelSize))
                .fontWeight(ForgotPasswordConstants.Typography.labelWeight)
                .foregroundColor(ForgotPasswordConstants.Typography.labelColor)

            // Email Field (matching Figma design)
            CustomTextField.email(
                text: $viewModel.email,
                isFocused: $viewModel.isEmailFocused,
                errorMessage: viewModel.emailError,
                onTextChanged: viewModel.onEmailChanged,
                onFocusChanged: viewModel.onEmailFocusChanged,
                onSubmit: {
                    Task {
                        await viewModel.sendResetLink()
                    }
                }
            )
            .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.emailFieldIdentifier)

            // Send Reset Link Button (matching Figma design)
            LoginButton(
                title: sendResetLinkText,
                isLoading: viewModel.isLoading,
                isEnabled: viewModel.canSubmit,
                action: {
                    Task {
                        await viewModel.sendResetLink()
                    }
                }
            )
            .accessibilityIdentifier(ForgotPasswordConstants.Accessibility.sendResetButtonIdentifier)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Success View
    private var successView: some View {
        VStack {
            Spacer()

            SuccessMessageView(
                title: resetLinkSentTitle,
                message: resetLinkSentMessage,
                buttonText: backToLoginText,
                onButtonTap: {
                    viewModel.resetSuccessState()
                    onNavigateToLogin()
                }
            )

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(ForgotPasswordConstants.Colors.screenBackground)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        ForgotPasswordView(
            onNavigateToLogin: {
                print("Navigate to Login")
            }
        )
    }
}
