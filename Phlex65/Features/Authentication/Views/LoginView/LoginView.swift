//
//  LoginView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Login View (PIXEL-PERFECT Figma Implementation)
struct LoginView: View {

    // MARK: - Navigation Callbacks
    let onNavigateToRegister: () -> Void
    let onNavigateToForgotPassword: () -> Void

    // MARK: - View Model
    @StateObject private var viewModel = LoginViewModel()

    // MARK: - Body (EXACT Figma JSON Structure)
    var body: some View {
        // Figma: Frame "Sign in" - width: 375px, height: 812px, background: #FFF
        VStack(alignment: .leading, spacing: 0) {
            // EXACT Figma: Sign In is 32px from top status bar (not middle aligned)
            VStack(alignment: .leading, spacing: LoginConstants.Layout.bodyGap) {
                // Body container - Figma: Frame "body" - gap: 32px, align-self: stretch
                VStack(alignment: .leading, spacing: LoginConstants.Layout.bodyGap) {
                    // Title + Description - Figma: Frame "title + desc" - gap: 4px
                    titleAndDescriptionSection

                    // Form List - Figma: Frame "list" - gap: 14px, align-self: stretch
                    formListSection

                    // CTA Section - Figma: Frame "Frame 1618872167" - gap: 32px, align-self: stretch
                    ctaSection
                }
            }
            .padding(.horizontal, LoginConstants.Layout.mainContainerPadding)
            .padding(.top, 32) // EXACT Figma: 32px from top status bar

            // Push content to top, not center
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(LoginConstants.Colors.screenBackground)
        .navigationBarHidden(true)
        .onTapGesture {
            viewModel.clearFocus()
        }
        .alert("Error", isPresented: .constant(viewModel.generalError != nil)) {
            Button("OK") {
                viewModel.generalError = nil
            }
        } message: {
            if let error = viewModel.generalError {
                Text(error)
            }
        }
        .accessibilityElement(children: .contain)
    }

    // MARK: - Title and Description Section (EXACT Figma)
    private var titleAndDescriptionSection: some View {
        // Figma: Frame "title + desc" - gap: 4px
        VStack(alignment: .leading, spacing: LoginConstants.Layout.titleDescGap) {
            // Title - Figma: "Sign In" - Inter 24px/600, color: #0C1523
            Text("Sign In")
                .font(.custom(LoginConstants.Typography.titleFontName, size: LoginConstants.Typography.titleSize))
                .foregroundColor(LoginConstants.Typography.titleColor)
                .accessibilityIdentifier(LoginConstants.Accessibility.titleIdentifier)
                .accessibilityAddTraits(.isHeader)

            // Subtitle - Figma: "Sign in to book professional caregivers easily." - Inter 14px/400, width: 311px, color: #6B7280
            Text("Sign in to book professional caregivers easily.")
                .font(.custom(LoginConstants.Typography.subtitleFontName, size: LoginConstants.Typography.subtitleSize))
                .foregroundColor(LoginConstants.Typography.subtitleColor)
                .frame(width: LoginConstants.Typography.subtitleWidth, alignment: .topLeading)
                .accessibilityIdentifier(LoginConstants.Accessibility.subtitleIdentifier)
        }
    }

    // MARK: - Form List Section (EXACT Figma)
    private var formListSection: some View {
        // Figma: Frame "list" - gap: 14px, align-self: stretch
        VStack(alignment: .leading, spacing: LoginConstants.Layout.listGap) {
            // Email Field - Figma: Instance "Text Input" - gap: 6px, align-self: stretch
            emailFieldSection

            // Password Section - Figma: Frame "Frame 1618872156" - gap: 12px, align-self: stretch
            passwordSection
        }
        .frame(maxWidth: .infinity, alignment: .topLeading)
    }

    // MARK: - Email Field Section (EXACT Figma)
    private var emailFieldSection: some View {
        // Figma: Instance "Text Input" - gap: 6px, align-self: stretch
        VStack(alignment: .leading, spacing: LoginConstants.Layout.textInputGap) {
            // Label - Figma: Frame "Label" - width: 300px, gap: 8px
            HStack(alignment: .center, spacing: LoginConstants.Layout.labelGap) {
                Text("Email Address")
                    .font(.custom(LoginConstants.Typography.labelFontName, size: LoginConstants.Typography.labelSize))
                    .foregroundColor(LoginConstants.Typography.labelColor)
                Spacer()
            }
            .frame(width: LoginConstants.Layout.labelWidth, alignment: .leading)

            // Input Field - Figma: Frame "Input" - height: 48px, padding: 8px 12px, gap: 8px
            HStack(alignment: .center, spacing: LoginConstants.Layout.labelGap) {
                TextField("Enter Email Address", text: $viewModel.email)
                    .font(.custom(LoginConstants.Typography.placeholderFontName, size: LoginConstants.Typography.placeholderSize))
                    .foregroundColor(viewModel.email.isEmpty ? LoginConstants.Typography.placeholderColor : LoginConstants.Typography.fieldTextColor)
                    .keyboardType(.emailAddress)
                    .textContentType(.emailAddress)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .accessibilityIdentifier(LoginConstants.Accessibility.emailFieldIdentifier)
            }
            .padding(.horizontal, LoginConstants.Layout.fieldHorizontalPadding)
            .padding(.vertical, LoginConstants.Layout.fieldVerticalPadding)
            .frame(height: LoginConstants.Layout.fieldHeight)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(LoginConstants.Colors.fieldBackground)
            .cornerRadius(LoginConstants.Layout.fieldCornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: LoginConstants.Layout.fieldCornerRadius)
                    .inset(by: LoginConstants.Layout.fieldBorderWidth / 2)
                    .stroke(LoginConstants.Colors.fieldBorderDefault, lineWidth: LoginConstants.Layout.fieldBorderWidth)
            )
        }
        .frame(maxWidth: .infinity, alignment: .topLeading)
    }

    // MARK: - Password Section (EXACT Figma)
    private var passwordSection: some View {
        // Figma: Frame "Frame 1618872156" - gap: 12px, align-self: stretch
        VStack(alignment: .leading, spacing: LoginConstants.Layout.passwordSectionGap) {
            // Password Field - Figma: Instance "Text Input" - gap: 6px, align-self: stretch
            VStack(alignment: .leading, spacing: LoginConstants.Layout.textInputGap) {
                // Label - Figma: Frame "Label" - width: 300px, gap: 8px
                HStack(alignment: .center, spacing: LoginConstants.Layout.labelGap) {
                    Text("Password")
                        .font(.custom(LoginConstants.Typography.labelFontName, size: LoginConstants.Typography.labelSize))
                        .foregroundColor(LoginConstants.Typography.labelColor)
                    Spacer()
                }
                .frame(width: LoginConstants.Layout.labelWidth, alignment: .leading)

                // Input Field - Figma: Frame "Input" - height: 48px, padding: 8px 12px, gap: 8px
                HStack(alignment: .center, spacing: LoginConstants.Layout.labelGap) {
                    if viewModel.showPassword {
                        TextField("Enter Password", text: $viewModel.password)
                    } else {
                        SecureField("Enter Password", text: $viewModel.password)
                    }
                }
                .font(.custom(LoginConstants.Typography.placeholderFontName, size: LoginConstants.Typography.placeholderSize))
                .foregroundColor(viewModel.password.isEmpty ? LoginConstants.Typography.placeholderColor : LoginConstants.Typography.fieldTextColor)
                .textContentType(.password)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .padding(.horizontal, LoginConstants.Layout.fieldHorizontalPadding)
                .padding(.vertical, LoginConstants.Layout.fieldVerticalPadding)
                .frame(height: LoginConstants.Layout.fieldHeight)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(LoginConstants.Colors.fieldBackground)
                .cornerRadius(LoginConstants.Layout.fieldCornerRadius)
                .overlay(
                    RoundedRectangle(cornerRadius: LoginConstants.Layout.fieldCornerRadius)
                        .inset(by: LoginConstants.Layout.fieldBorderWidth / 2)
                        .stroke(LoginConstants.Colors.fieldBorderDefault, lineWidth: LoginConstants.Layout.fieldBorderWidth)
                )
                .accessibilityIdentifier(LoginConstants.Accessibility.passwordFieldIdentifier)
            }
            .frame(maxWidth: .infinity, alignment: .topLeading)

            // Forgot Password - Figma: Frame "form" - gap: 6px, align-self: stretch
            VStack(alignment: .leading, spacing: 6) {
                Button(action: onNavigateToForgotPassword) {
                    Text("Forgot Password")
                        .font(.custom(LoginConstants.Typography.forgotPasswordFontName, size: LoginConstants.Typography.forgotPasswordSize))
                        .foregroundColor(LoginConstants.Typography.forgotPasswordColor)
                        .multilineTextAlignment(.trailing)
                        .frame(maxWidth: .infinity, alignment: .topTrailing)
                }
                .accessibilityIdentifier(LoginConstants.Accessibility.forgotPasswordIdentifier)
            }
            .frame(maxWidth: .infinity, alignment: .topLeading)
        }
        .frame(maxWidth: .infinity, alignment: .topLeading)
    }

    // MARK: - CTA Section (EXACT Figma)
    private var ctaSection: some View {
        // Figma: Frame "Frame 1618872167" - gap: 32px, align-self: stretch
        VStack(alignment: .leading, spacing: LoginConstants.Layout.ctaGap) {
            // Button Section - Figma: Frame "cta" - gap: 24px, align-self: stretch
            VStack(alignment: .leading, spacing: LoginConstants.Layout.buttonSectionGap) {
                // Sign In Button - Figma: Instance "Button" - height: 48px, padding: 12px 20px, gap: 8px, border-radius: 50px, background: #89C226, box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05)
                Button(action: {
                    Task {
                        await viewModel.signIn()
                    }
                }) {
                    HStack(alignment: .center, spacing: LoginConstants.Layout.buttonGap) {
                        Text("Sign In")
                            .font(.custom(LoginConstants.Typography.buttonFontName, size: LoginConstants.Typography.buttonSize))
                            .foregroundColor(LoginConstants.Typography.buttonColor)
                    }
                    .padding(.horizontal, LoginConstants.Layout.buttonHorizontalPadding)
                    .padding(.vertical, LoginConstants.Layout.buttonVerticalPadding)
                    .frame(height: LoginConstants.Layout.buttonHeight)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(LoginConstants.Colors.primaryButtonBackground)
                    .cornerRadius(LoginConstants.Layout.buttonCornerRadius)
                    .shadow(
                        color: LoginConstants.Colors.buttonShadowColor,
                        radius: LoginConstants.Colors.buttonShadowRadius,
                        x: LoginConstants.Colors.buttonShadowX,
                        y: LoginConstants.Colors.buttonShadowY
                    )
                }
                .disabled(!viewModel.canSubmit || viewModel.isLoading)
                .accessibilityIdentifier(LoginConstants.Accessibility.signInButtonIdentifier)
            }
            .frame(maxWidth: .infinity, alignment: .topLeading)

            // Footer Text - Figma: "Dont have account?" (grey) + "Sign Up" (blue) - Inter 14px/500, text-align: center
            Button(action: onNavigateToRegister) {
                HStack(spacing: 4) {
                    Text("Dont have account?")
                        .font(.custom(LoginConstants.Typography.footerFontName, size: LoginConstants.Typography.footerSize))
                        .foregroundColor(LoginConstants.Colors.secondaryText) // Grey color

                    Text("Sign Up")
                        .font(.custom(LoginConstants.Typography.footerFontName, size: LoginConstants.Typography.footerSize))
                        .foregroundColor(LoginConstants.Typography.footerColor) // Blue color #0292D9
                }
                .frame(maxWidth: .infinity, alignment: .center)
            }
            .accessibilityIdentifier(LoginConstants.Accessibility.signUpLinkIdentifier)
        }
        .frame(maxWidth: .infinity, alignment: .topLeading)
    }
}

// MARK: - Preview
#Preview {
    NavigationView {
        LoginView(
            onNavigateToRegister: {
                print("Navigate to Register")
            },
            onNavigateToForgotPassword: {
                print("Navigate to Forgot Password")
            }
        )
    }
}
