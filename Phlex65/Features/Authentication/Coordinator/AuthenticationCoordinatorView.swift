//
//  AuthenticationCoordinatorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Authentication Coordinator View
struct AuthenticationCoordinatorView: View {
    @State private var currentScreen: AuthenticationScreen = .login
    @StateObject private var authService = AuthenticationService.shared

    var body: some View {
        NavigationView {
            Group {
                switch currentScreen {
                case .login:
                    LoginView(
                        onNavigateToRegister: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .signupStep1
                            }
                        },
                        onNavigateToForgotPassword: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .forgotPassword
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .register:
                    RegisterView(
                        onNavigateToLogin: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        },
                        onNavigateToVerifyCode: { email in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .verifyCode(context: .signup(email: email))
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .signupStep1:
                    SignupStep1View(
                        onNavigateToLogin: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        },
                        onNavigateToStep2: { fullName, email, password in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .signupStep2(fullName: fullName, email: email, password: password)
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .signupStep2(let fullName, let email, let password):
                    SignupStep2View(
                        fullName: fullName,
                        email: email,
                        password: password,
                        onNavigateBack: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .signupStep1
                            }
                        },
                        onNavigateToLogin: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        },
                        onNavigateToVerifyCode: { email in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .verifyCode(context: .signup(email: email))
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .forgotPassword:
                    ForgotPasswordView(
                        onNavigateToLogin: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        },
                        onNavigateToVerifyCode: { email in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .verifyCode(context: .forgotPassword(email: email))
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .verifyCode(let context):
                    VerifyCodeView(
                        context: context,
                        onNavigateBack: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                handleVerifyCodeBackNavigation(context: context)
                            }
                        },
                        onVerificationSuccess: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                handleVerificationSuccess(context: context)
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                case .resetPassword(let token):
                    ResetPasswordView(
                        resetToken: token,
                        onNavigateBack: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        },
                        onResetSuccess: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                currentScreen = .login
                            }
                        }
                    )
                    .transition(.asymmetric(
                        insertion: .move(edge: .trailing).combined(with: .opacity),
                        removal: .move(edge: .leading).combined(with: .opacity)
                    ))
                }
            }
            .animation(.easeInOut(duration: 0.3), value: currentScreen)
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .onChange(of: authService.requiresVerification) { requiresVerification in
            if requiresVerification, let email = authService.unverifiedUserEmail {
                currentScreen = .verifyCode(context: .unverifiedLogin(email: email))
            }
        }
    }

    // MARK: - Navigation Helpers
    private func handleVerifyCodeBackNavigation(context: VerificationContext) {
        switch context {
        case .signup:
            currentScreen = .register
        case .forgotPassword:
            currentScreen = .forgotPassword
        case .unverifiedLogin:
            currentScreen = .login
        }
    }

    private func handleVerificationSuccess(context: VerificationContext) {
        switch context {
        case .signup, .unverifiedLogin:
            // After successful verification for signup or unverified login,
            // the user should be automatically logged in and navigate to main app
            // For now, navigate to login screen
            currentScreen = .login
        case .forgotPassword:
            // After forgot password verification, navigate to reset password screen
            if let token = authService.resetToken {
                currentScreen = .resetPassword(token: token)
            } else {
                // Fallback to login if no token available
                currentScreen = .login
            }
        }
    }
}

// MARK: - Preview
#Preview {
    AuthenticationCoordinatorView()
}
