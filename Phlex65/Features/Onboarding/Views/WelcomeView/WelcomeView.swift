//
//  WelcomeView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//  MVVM Architecture with Constants
//

import SwiftUI

// MARK: - Welcome View
struct WelcomeView: View {

    // MARK: - Properties
    let onGetStarted: () -> Void
    let onSignIn: (() -> Void)?

    // MARK: - ViewModel
    @StateObject private var viewModel = WelcomeViewModel()

    // MARK: - Initialization
    init(
        onGetStarted: @escaping () -> Void,
        onSignIn: (() -> Void)? = nil
    ) {
        self.onGetStarted = onGetStarted
        self.onSignIn = onSignIn
    }

    // MARK: - Body
    var body: some View {
        ZStack(alignment: .top) {
            // Background Image - fills from top
            backgroundImageView

            // Gradient Overlay - EXACT Figma specifications
            gradientOverlay

            // Content Layout - positioned at bottom
            VStack {
                Spacer()
                contentCard
            }
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Background Image View
    private var backgroundImageView: some View {
        VStack(spacing: 0) {
            Image(viewModel.backgroundImageName)
                .resizable()
                .aspectRatio(WelcomeConstants.Images.aspectRatio, contentMode: .fit)
                .frame(maxWidth: .infinity)
                .clipped()

            Spacer(minLength: 0)
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Gradient Overlay (EXACT Figma Specifications - Full Screen)
    private var gradientOverlay: some View {
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(red: WelcomeConstants.Colors.gradientColor.red,
                                 green: WelcomeConstants.Colors.gradientColor.green,
                                 blue: WelcomeConstants.Colors.gradientColor.blue,
                                 opacity: 0.10), location: 0.0), // 0% - rgba(17, 12, 29, 0.10)
                .init(color: Color(red: WelcomeConstants.Colors.gradientColor.red,
                                 green: WelcomeConstants.Colors.gradientColor.green,
                                 blue: WelcomeConstants.Colors.gradientColor.blue,
                                 opacity: 0.0), location: 0.325), // 32.5% - rgba(17, 12, 29, 0.00)
                .init(color: Color(red: WelcomeConstants.Colors.gradientColor.red,
                                 green: WelcomeConstants.Colors.gradientColor.green,
                                 blue: WelcomeConstants.Colors.gradientColor.blue,
                                 opacity: 1.0), location: 0.6627) // 66.27% - #110C1D
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity) // Full screen coverage
        .ignoresSafeArea(.all)
    }



    // MARK: - Content Card
    private var contentCard: some View {
        VStack(spacing: 0) {
            VStack(spacing: 0) {
                titleSection
                    .padding(.bottom, WelcomeConstants.Layout.textContentSpacing) // 18px from Figma

                subtitleSection
                    .padding(.bottom, WelcomeConstants.Layout.mainContentSpacing) // 24px from Figma
            }
            .padding(.top, WelcomeConstants.Layout.topPadding) // 32px from Figma

            Spacer()

            VStack(spacing: 0) {
                primaryButton

                secondaryTextSection
                    .padding(.top, WelcomeConstants.Layout.buttonSpacing) // 24px from Figma
            }
            .padding(.bottom, WelcomeConstants.Layout.bottomPadding) // 40px from Figma
        }
        .padding(.horizontal, WelcomeConstants.Layout.horizontalPadding) // 24px from Figma
        .frame(maxWidth: .infinity)
        .frame(height: WelcomeConstants.Layout.cardHeight) // 294px from Figma
        .background(
            Color(red: WelcomeConstants.Colors.white.red,
                  green: WelcomeConstants.Colors.white.green,
                  blue: WelcomeConstants.Colors.white.blue)
        )
        .clipShape(
            RoundedCorner(
                radius: WelcomeConstants.Layout.cardCornerRadius,
                corners: [.topLeft, .topRight]
            )
        )
        .shadow(color: Color.black.opacity(0.0), radius: 7, x: 0, y: -2)
    }

    // MARK: - Title Section (Pixel Perfect from Figma)
    private var titleSection: some View {
        VStack(spacing: 0) {
            Text(WelcomeConstants.Text.welcomeTo)
                .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.titleSize)
                      .weight(WelcomeConstants.Fonts.titleWeight))
                .foregroundColor(Color(red: WelcomeConstants.Colors.titleMainColor.red,
                                     green: WelcomeConstants.Colors.titleMainColor.green,
                                     blue: WelcomeConstants.Colors.titleMainColor.blue))
            +
            Text(WelcomeConstants.Text.appName)
                .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.titleSize)
                      .weight(WelcomeConstants.Fonts.titleWeight))
                .foregroundColor(Color(red: WelcomeConstants.Colors.titleAccentColor.red,
                                     green: WelcomeConstants.Colors.titleAccentColor.green,
                                     blue: WelcomeConstants.Colors.titleAccentColor.blue))
        }
        .frame(width: WelcomeConstants.Layout.titleWidth, height: WelcomeConstants.Layout.titleHeight)
        .multilineTextAlignment(.center)
    }

    // MARK: - Subtitle Section (Pixel Perfect from Figma)
    private var subtitleSection: some View {
        Text(WelcomeConstants.Text.subtitle)
            .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.subtitleSize)
                  .weight(WelcomeConstants.Fonts.subtitleWeight))
            .foregroundColor(Color(red: WelcomeConstants.Colors.subtitleColor.red,
                                 green: WelcomeConstants.Colors.subtitleColor.green,
                                 blue: WelcomeConstants.Colors.subtitleColor.blue))
            .frame(width: WelcomeConstants.Layout.subtitleWidth, height: WelcomeConstants.Layout.subtitleHeight)
            .multilineTextAlignment(.center)
            .lineSpacing(WelcomeConstants.Fonts.subtitleLineHeight - WelcomeConstants.Fonts.subtitleSize) // Figma: 21px line height - 14px font size = 7px spacing
            .lineLimit(nil)
    }

    // MARK: - Primary Button (Pixel Perfect from Figma)
    private var primaryButton: some View {
        Button(action: onGetStarted) {
            Text(WelcomeConstants.Text.primaryButtonTitle)
                .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.buttonSize)
                      .weight(WelcomeConstants.Fonts.buttonWeight))
                .foregroundColor(Color(red: WelcomeConstants.Colors.buttonTextColor.red,
                                     green: WelcomeConstants.Colors.buttonTextColor.green,
                                     blue: WelcomeConstants.Colors.buttonTextColor.blue))
                .frame(width: WelcomeConstants.Layout.buttonWidth, height: WelcomeConstants.Layout.buttonHeight)
                .background(Color(red: WelcomeConstants.Colors.buttonBackgroundColor.red,
                                green: WelcomeConstants.Colors.buttonBackgroundColor.green,
                                blue: WelcomeConstants.Colors.buttonBackgroundColor.blue))
                .cornerRadius(WelcomeConstants.Layout.buttonCornerRadius) // 50px for fully rounded
                .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        }
    }

    // MARK: - Secondary Text Section
    private var secondaryTextSection: some View {
        Group {
            if let onSignIn = onSignIn {
                Button(action: onSignIn) {
                    HStack(spacing: 0) {
                        Text(WelcomeConstants.Text.secondaryTextPrefix)
                            .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.secondaryTextSize)
                                  .weight(WelcomeConstants.Fonts.secondaryTextWeight))
                            .foregroundColor(Color(red: WelcomeConstants.Colors.secondaryTextColor.red,
                                                 green: WelcomeConstants.Colors.secondaryTextColor.green,
                                                 blue: WelcomeConstants.Colors.secondaryTextColor.blue))

                        Text(WelcomeConstants.Text.secondaryTextAction)
                            .font(.custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.secondaryTextSize)
                                  .weight(WelcomeConstants.Fonts.secondaryTextWeight))
                            .foregroundColor(Color(red: WelcomeConstants.Colors.titleAccentColor.red,
                                                 green: WelcomeConstants.Colors.titleAccentColor.green,
                                                 blue: WelcomeConstants.Colors.titleAccentColor.blue))
                    }
                    .frame(width: WelcomeConstants.Layout.secondaryTextWidth, height: WelcomeConstants.Layout.secondaryTextHeight)
                    .lineSpacing(WelcomeConstants.Fonts.secondaryTextLineHeight - WelcomeConstants.Fonts.secondaryTextSize) // Figma: 21px line height - 14px font size = 7px spacing
                }
            } else {
                EmptyView()
            }
        }
    }


}

// MARK: - Preview
#Preview {
    WelcomeView(
        onGetStarted: {
            print("Get Started tapped")
        },
        onSignIn: {
            print("Sign In tapped")
        }
    )
}
