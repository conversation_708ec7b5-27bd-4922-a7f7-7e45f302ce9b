//
//  OnboardingStep3View.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Step 3 View
struct OnboardingStep3View: View {
    
    // MARK: - Properties
    let onGetStartedTap: () -> Void
    let onBackTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        onGetStartedTap: @escaping () -> Void,
        onBackTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.onGetStartedTap = onGetStartedTap
        self.onBackTap = onBackTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        OnboardingStepView(
            step: OnboardingStep.step3,
            currentStepIndex: 3,
            canGoBack: true,
            canSkip: false, // No skip on last step
            isLoading: isLoading,
            onNextTap: onGetStartedTap,
            onBackTap: onBackTap,
            onSkipTap: nil
        )
    }
}

// MARK: - Preview
#Preview {
    OnboardingStep3View(
        onGetStartedTap: {
            print("Step 3: Get Started tapped")
        },
        onBackTap: {
            print("Step 3: Back tapped")
        }
    )
}
