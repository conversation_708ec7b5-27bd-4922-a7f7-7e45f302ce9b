//
//  OnboardingStep1View.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Step 1 View
struct OnboardingStep1View: View {
    
    // MARK: - Properties
    let onNextTap: () -> Void
    let onSkipTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        onNextTap: @escaping () -> Void,
        onSkipTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.onNextTap = onNextTap
        self.onSkipTap = onSkipTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        OnboardingStepView(
            step: OnboardingStep.step1,
            currentStepIndex: 1,
            canGoBack: false,
            canSkip: true,
            isLoading: isLoading,
            onNextTap: onNextTap,
            onBackTap: nil,
            onSkipTap: onSkipTap
        )
    }
}

// MARK: - Preview
#Preview {
    OnboardingStep1View(
        onNextTap: {
            print("Step 1: Next tapped")
        },
        onSkipTap: {
            print("Step 1: Skip tapped")
        }
    )
}
