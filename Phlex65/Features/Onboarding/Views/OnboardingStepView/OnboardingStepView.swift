//
//  OnboardingStepView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Step View (Generic Reusable View)
struct OnboardingStepView: View {
    
    // MARK: - Properties
    let step: OnboardingStep
    let currentStepIndex: Int
    let canGoBack: Bool
    let canSkip: Bool
    let isLoading: Bool
    let onNextTap: () -> Void
    let onBackTap: (() -> Void)?
    let onSkipTap: (() -> Void)?
    
    // MARK: - Initialization
    init(
        step: OnboardingStep,
        currentStepIndex: Int,
        canGoBack: Bool = false,
        canSkip: Bool = true,
        isLoading: Bool = false,
        onNextTap: @escaping () -> Void,
        onBackTap: (() -> Void)? = nil,
        onSkipTap: (() -> Void)? = nil
    ) {
        self.step = step
        self.currentStepIndex = currentStepIndex
        self.canGoBack = canGoBack
        self.canSkip = canSkip
        self.isLoading = isLoading
        self.onNextTap = onNextTap
        self.onBackTap = onBackTap
        self.onSkipTap = onSkipTap
    }
    
    // MARK: - Body (Figma Design Layout - No White Card)
    var body: some View {
        ZStack {
            // Background image properly contained
            backgroundImageView

            // Gradient overlay - EXACT Figma specifications
            gradientOverlay

            // Content positioned at bottom - NO WHITE CARD
            VStack(spacing: 0) {
                Spacer()

                // Bottom content area - directly on gradient overlay
                contentSection
                    .padding(.horizontal, OnboardingConstants.Layout.screenPadding) // 16px padding
                    .padding(.bottom, OnboardingConstants.Layout.contentBottomPadding) // 60px from bottom
            }
        }
        .ignoresSafeArea(.all)
    }

    // MARK: - Background Image View (CSS contain, top center)
    private var backgroundImageView: some View {
        GeometryReader { geometry in
            let width = geometry.size.width
            let aspectRatio = getAspectRatio()
            let imageHeight = width / aspectRatio

            VStack(spacing: 0) {
                Image(step.backgroundImageName)
                    .resizable()
                    .aspectRatio(aspectRatio, contentMode: .fit)
                    .frame(width: width, height: imageHeight, alignment: .top)
                    .clipped()
                Spacer(minLength: 0)
            }
            .frame(width: width, height: geometry.size.height, alignment: .top)
        }
        .ignoresSafeArea(.all)
    }

    // ✅ FIXED: Get Figma's exact aspect ratio for each step
    private func getAspectRatio() -> CGFloat {
        switch step.id {
        case 1:
            return OnboardingConstants.Step1.Images.aspectRatio // 375/811 ≈ 0.46
        case 2:
            return OnboardingConstants.Step2.Images.aspectRatio // 381/280 = 1.36
        case 3:
            return OnboardingConstants.Step3.Images.aspectRatio // 857/572 = 1.49
        default:
            return 1.0 // Fallback
        }
    }

    // MARK: - Gradient Overlay (EXACT Figma Specifications - Full Screen)
    private var gradientOverlay: some View {
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(red: OnboardingConstants.Colors.gradientColor.red,
                                 green: OnboardingConstants.Colors.gradientColor.green,
                                 blue: OnboardingConstants.Colors.gradientColor.blue,
                                 opacity: 0.10), location: 0.0), // 0% - rgba(17, 12, 29, 0.10)
                .init(color: Color(red: OnboardingConstants.Colors.gradientColor.red,
                                 green: OnboardingConstants.Colors.gradientColor.green,
                                 blue: OnboardingConstants.Colors.gradientColor.blue,
                                 opacity: 0.0), location: 0.325), // 32.5% - rgba(17, 12, 29, 0.00)
                .init(color: Color(red: OnboardingConstants.Colors.gradientColor.red,
                                 green: OnboardingConstants.Colors.gradientColor.green,
                                 blue: OnboardingConstants.Colors.gradientColor.blue,
                                 opacity: 1.0), location: 0.6627) // 66.27% - #110C1D
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity) // Full screen coverage
        .ignoresSafeArea(.all)
    }

    // MARK: - Content Section (EXACT Figma: Title, subtitle, progress, and buttons - No White Card)
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: OnboardingConstants.Layout.contentContainerGap) { // gap: 24px
            // Onboarding Information Container
            VStack(alignment: .leading, spacing: OnboardingConstants.Layout.onboardingInfoGap) { // gap: 8px
                // Title and Description Container
                VStack(alignment: .leading, spacing: OnboardingConstants.Layout.titleDescriptionGap) { // gap: 4px
                    // Title - Figma: Plus Jakarta Sans 30px/600, line-height: 36px, color: #FFF
                    Text(step.title)
                        .font(.custom("PlusJakartaSans-SemiBold", size: OnboardingConstants.Typography.titleSize))
                        .fontWeight(OnboardingConstants.Typography.titleWeight)
                        .foregroundColor(OnboardingConstants.Typography.titleColor)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // Subtitle - Figma: Inter 16px/400, line-height: 24px, color: #FFF
                    Text(step.subtitle)
                        .font(.custom("Inter-Regular", size: OnboardingConstants.Typography.subtitleSize))
                        .fontWeight(OnboardingConstants.Typography.subtitleWeight)
                        .foregroundColor(OnboardingConstants.Typography.subtitleColor)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }

            // Progress indicators - Figma: width: 160px, gap: 8px, center aligned
            progressSection

            // Buttons
            buttonSection
        }
        .frame(maxWidth: .infinity) // Use full available width, remove alignment constraint
    }

    // MARK: - Progress Section (EXACT Figma: Conditional Alignment Based on Step)
    private var progressSection: some View {
        // Conditional alignment: Step 1 = Left aligned, Steps 2+ = Center aligned
        HStack {
            // Indicator Container - Figma: width: 160px, gap: 8px
            HStack(spacing: OnboardingConstants.Layout.progressBarSpacing) { // gap: 8px
                ForEach(1...OnboardingConstants.Navigation.totalSteps, id: \.self) { stepIndex in
                    RoundedRectangle(cornerRadius: OnboardingConstants.Layout.progressBarCornerRadius) // border-radius: 100px
                        .fill(stepIndex <= currentStepIndex ?
                              OnboardingConstants.Colors.progressActive : // #89C226
                              OnboardingConstants.Colors.progressInactive) // #F4F5F4
                        .frame(width: OnboardingConstants.Layout.progressBarWidth, // width: 48px
                               height: OnboardingConstants.Layout.progressBarHeight) // height: 4px
                        .animation(.easeInOut(duration: OnboardingConstants.Animation.progressAnimationDuration), value: currentStepIndex)
                }
            }

            // Conditional spacer: Only add spacer for steps 2+ (center alignment)
            if currentStepIndex > 1 {
                Spacer()
            }
        }
        .frame(maxWidth: .infinity, alignment: currentStepIndex == 1 ? .leading : .center)
    }

    // MARK: - Button Section (EXACT Figma: Bottom Action)
    private var buttonSection: some View {
        // Bottom Action - Figma: gap: 12px
        VStack(spacing: OnboardingConstants.Layout.bottomActionGap) { // gap: 12px
            // Next Button - Figma: height: 48px, padding: 12px 20px, border-radius: 50px, background: #89C226
            Button(action: onNextTap) {
                Text(OnboardingConstants.Navigation.nextButtonTitle)
                    .font(.custom("Inter-Medium", size: OnboardingConstants.Typography.buttonSize)) // Inter 16px/500
                    .fontWeight(OnboardingConstants.Typography.buttonWeight)
                    .foregroundColor(OnboardingConstants.Typography.buttonColor) // #FFF
                    .frame(maxWidth: .infinity)
                    .frame(height: OnboardingConstants.Layout.buttonHeight) // height: 48px
                    .background(OnboardingConstants.Colors.primaryButtonBackground) // #89C226
                    .cornerRadius(OnboardingConstants.Layout.buttonCornerRadius) // border-radius: 50px
            }

            // Skip Button - Figma: Inter 16px/400, color: #FFF
            if canSkip, let onSkipTap = onSkipTap {
                Button(action: onSkipTap) {
                    Text(OnboardingConstants.Navigation.skipButtonTitle)
                        .font(.custom("Inter-Regular", size: OnboardingConstants.Typography.skipSize)) // Inter 16px/400
                        .fontWeight(OnboardingConstants.Typography.skipWeight)
                        .foregroundColor(OnboardingConstants.Typography.skipColor) // #FFF
                }
            }
        }
        .frame(maxWidth: .infinity) // Use available width, not fixed screen width
    }
}

// MARK: - Preview
#Preview {
    OnboardingStepView(
        step: OnboardingStep.step1,
        currentStepIndex: 1,
        canGoBack: false,
        canSkip: true,
        onNextTap: {
            print("Next tapped")
        },
        onSkipTap: {
            print("Skip tapped")
        }
    )
}
