//
//  OnboardingCardView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Card View (Reusable Component)
struct OnboardingCardView: View {
    
    // MARK: - Properties
    let step: OnboardingStep
    let onButtonTap: () -> Void
    let showBackButton: Bool
    let onBackTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        step: OnboardingStep,
        onButtonTap: @escaping () -> Void,
        showBackButton: Bool = false,
        onBackTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.step = step
        self.onButtonTap = onButtonTap
        self.showBackButton = showBackButton
        self.onBackTap = onBackTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            
            // Content Section
            VStack(spacing: 0) {
                titleSection
                    .padding(.bottom, OnboardingConstants.Layout.titleToSubtitleSpacing)
                
                subtitleSection
                    .padding(.bottom, OnboardingConstants.Layout.progressToButtonSpacing)
            }
            
            Spacer()
            
            // Button Section
            VStack(spacing: 0) {
                if showBackButton {
                    backButtonSection
                        .padding(.bottom, 16)
                }
                
                primaryButtonSection
            }
            .padding(.bottom, OnboardingConstants.Layout.contentBottomPadding)
        }
        .padding(.horizontal, OnboardingConstants.Layout.screenPadding)
        .frame(maxWidth: .infinity)
        .frame(height: OnboardingConstants.Layout.overlayBottomHeight)
        .background(cardBackground)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        Color(red: OnboardingConstants.Colors.white.red,
              green: OnboardingConstants.Colors.white.green,
              blue: OnboardingConstants.Colors.white.blue)
            .clipShape(
                RoundedCorner(
                    radius: OnboardingConstants.Layout.overlayCornerRadius,
                    corners: [.topLeft, .topRight]
                )
            )
    }
    
    // MARK: - Title Section (Figma Typography)
    private var titleSection: some View {
        Text(step.title)
            .font(.custom(OnboardingConstants.Typography.titleFontName, size: OnboardingConstants.Typography.titleSize)
                  .weight(OnboardingConstants.Typography.titleWeight))
            .foregroundColor(OnboardingConstants.Typography.titleColor)
            .multilineTextAlignment(.leading)
            .frame(maxWidth: .infinity, alignment: .leading)
            .lineSpacing(OnboardingConstants.Typography.titleLineHeight - OnboardingConstants.Typography.titleSize)
            .lineLimit(nil)
    }
    
    // MARK: - Subtitle Section (EXACT Figma Typography)
    private var subtitleSection: some View {
        Text(step.subtitle)
            .font(.custom(OnboardingConstants.Typography.subtitleFontName, size: OnboardingConstants.Typography.subtitleSize)
                  .weight(OnboardingConstants.Typography.subtitleWeight))
            .foregroundColor(OnboardingConstants.Typography.subtitleColor)
            .multilineTextAlignment(.leading)
            .frame(maxWidth: .infinity, alignment: .leading)
            .lineSpacing(OnboardingConstants.Typography.subtitleLineHeight - OnboardingConstants.Typography.subtitleSize)
            .lineLimit(nil)
    }
    
    // MARK: - Primary Button Section
    private var primaryButtonSection: some View {
        OnboardingButtonView(
            title: OnboardingConstants.Navigation.nextButtonTitle,
            style: .primary,
            isLoading: isLoading,
            action: onButtonTap
        )
    }
    
    // MARK: - Back Button Section
    private var backButtonSection: some View {
        Group {
            if let onBackTap = onBackTap {
                OnboardingButtonView(
                    title: OnboardingConstants.Navigation.backButtonTitle,
                    style: .secondary,
                    isLoading: false,
                    action: onBackTap
                )
            }
        }
    }
}

// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.1)
            .ignoresSafeArea()
        
        VStack {
            Spacer()
            OnboardingCardView(
                step: OnboardingStep.step1,
                onButtonTap: {
                    print("Button tapped")
                },
                showBackButton: true,
                onBackTap: {
                    print("Back tapped")
                }
            )
        }
    }
}
