//
//  OnboardingProgressView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Progress View (Reusable Component)
struct OnboardingProgressView: View {
    
    // MARK: - Properties
    let currentStep: Int
    let totalSteps: Int
    let showSkipButton: Bool
    let onSkipTap: (() -> Void)?
    
    // MARK: - Initialization
    init(
        currentStep: Int,
        totalSteps: Int = OnboardingConstants.Navigation.totalSteps,
        showSkipButton: Bool = true,
        onSkipTap: (() -> Void)? = nil
    ) {
        self.currentStep = currentStep
        self.totalSteps = totalSteps
        self.showSkipButton = showSkipButton
        self.onSkipTap = onSkipTap
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // Top section with skip button
            HStack {
                Spacer()
                
                if showSkipButton, let onSkipTap = onSkipTap {
                    Button(action: onSkipTap) {
                        Text(OnboardingConstants.Navigation.skipButtonTitle)
                            .font(.custom(OnboardingConstants.Typography.skipFontName, size: OnboardingConstants.Typography.skipSize)
                                  .weight(OnboardingConstants.Typography.skipWeight))
                            .foregroundColor(OnboardingConstants.Typography.skipColor)
                    }
                }
            }
            .padding(.horizontal, OnboardingConstants.Layout.screenPadding)
            .padding(.top, 16)
            .padding(.bottom, 24)
            
            // Progress indicators
            HStack(spacing: OnboardingConstants.Layout.progressBarSpacing) {
                ForEach(1...totalSteps, id: \.self) { step in
                    progressIndicator(for: step)
                }
            }
            .padding(.horizontal, OnboardingConstants.Layout.screenPadding)
        }
    }
    
    // MARK: - Progress Indicator
    private func progressIndicator(for step: Int) -> some View {
        RoundedRectangle(cornerRadius: OnboardingConstants.Layout.progressBarCornerRadius)
            .fill(progressColor(for: step))
            .frame(width: OnboardingConstants.Layout.progressBarWidth,
                   height: OnboardingConstants.Layout.progressBarHeight)
            .animation(.easeInOut(duration: OnboardingConstants.Animation.progressAnimationDuration),
                      value: currentStep)
    }
    
    // MARK: - Progress Color (Figma Specifications)
    private func progressColor(for step: Int) -> Color {
        if step <= currentStep {
            return OnboardingConstants.Colors.progressActive
        } else {
            return OnboardingConstants.Colors.progressInactive
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 40) {
        OnboardingProgressView(
            currentStep: 1,
            onSkipTap: {
                print("Skip tapped")
            }
        )
        
        OnboardingProgressView(
            currentStep: 2,
            onSkipTap: {
                print("Skip tapped")
            }
        )
        
        OnboardingProgressView(
            currentStep: 3,
            showSkipButton: false
        )
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
