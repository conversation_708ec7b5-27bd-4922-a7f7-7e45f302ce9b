//
//  OnboardingButtonView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Button Style (Figma Specifications)
enum OnboardingButtonStyle {
    case primary
    case secondary
    case text

    var backgroundColor: Color {
        switch self {
        case .primary:
            return OnboardingConstants.Colors.primaryButtonBackground // Green from Figma
        case .secondary:
            return Color.clear // Transparent for secondary
        case .text:
            return Color.clear
        }
    }

    var textColor: Color {
        switch self {
        case .primary:
            return OnboardingConstants.Colors.primaryButtonText // White text on green
        case .secondary:
            return OnboardingConstants.Colors.primaryText // White text
        case .text:
            return OnboardingConstants.Colors.skipButtonText // White with opacity
        }
    }

    var borderColor: Color? {
        switch self {
        case .primary, .secondary:
            return nil // No borders as per Figma
        case .text:
            return nil
        }
    }

    var borderWidth: CGFloat {
        return 0 // No borders as per Figma
    }
}

// MARK: - Onboarding Button View (Reusable Component)
struct OnboardingButtonView: View {
    
    // MARK: - Properties
    let title: String
    let style: OnboardingButtonStyle
    let isLoading: Bool
    let action: () -> Void
    
    // MARK: - State
    @State private var isPressed = false
    
    // MARK: - Initialization
    init(
        title: String,
        style: OnboardingButtonStyle = .primary,
        isLoading: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.isLoading = isLoading
        self.action = action
    }
    
    // MARK: - Body
    var body: some View {
        Button(action: {
            if !isLoading {
                action()
            }
        }) {
            HStack(spacing: 8) {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: style.textColor))
                        .scaleEffect(0.8)
                }

                Text(isLoading ? "Loading..." : title)
                    .font(.custom(
                        style == .primary ? OnboardingConstants.Typography.buttonFontName : OnboardingConstants.Typography.skipFontName,
                        size: style == .primary ? OnboardingConstants.Typography.buttonSize : OnboardingConstants.Typography.skipSize
                    ).weight(style == .primary ? OnboardingConstants.Typography.buttonWeight : OnboardingConstants.Typography.skipWeight))
                    .foregroundColor(style.textColor)
            }
            .frame(maxWidth: style == .primary ? .infinity : nil)
            .frame(height: style == .primary ? OnboardingConstants.Layout.buttonHeight : nil)
            .padding(.horizontal, style == .primary ? 0 : 16)
            .background(buttonBackground)
            .overlay(buttonBorder)
            .cornerRadius(buttonCornerRadius)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .disabled(isLoading)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    // MARK: - Computed Properties

    
    private var buttonCornerRadius: CGFloat {
        switch style {
        case .primary, .secondary:
            return OnboardingConstants.Layout.buttonCornerRadius
        case .text:
            return 0 // No corner radius for text buttons
        }
    }
    
    private var buttonBackground: some View {
        RoundedRectangle(cornerRadius: buttonCornerRadius)
            .fill(style.backgroundColor)
    }

    private var buttonBorder: some View {
        Group {
            if let borderColor = style.borderColor {
                RoundedRectangle(cornerRadius: buttonCornerRadius)
                    .stroke(borderColor, lineWidth: style.borderWidth)
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: 20) {
        OnboardingButtonView(
            title: "Primary Button",
            style: .primary,
            action: {
                print("Primary button tapped")
            }
        )
        
        OnboardingButtonView(
            title: "Secondary Button",
            style: .secondary,
            action: {
                print("Secondary button tapped")
            }
        )
        
        OnboardingButtonView(
            title: "Text Button",
            style: .text,
            action: {
                print("Text button tapped")
            }
        )
        
        OnboardingButtonView(
            title: "Loading Button",
            style: .primary,
            isLoading: true,
            action: {
                print("Loading button tapped")
            }
        )
    }
    .padding()
}
