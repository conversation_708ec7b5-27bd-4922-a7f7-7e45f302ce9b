//
//  OnboardingStep2View.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Step 2 View
struct OnboardingStep2View: View {
    
    // MARK: - Properties
    let onNextTap: () -> Void
    let onBackTap: (() -> Void)?
    let onSkipTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        onNextTap: @escaping () -> Void,
        onBackTap: (() -> Void)? = nil,
        onSkipTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.onNextTap = onNextTap
        self.onBackTap = onBackTap
        self.onSkipTap = onSkipTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        OnboardingStepView(
            step: OnboardingStep.step2,
            currentStepIndex: 2,
            canGoBack: true,
            canSkip: true,
            isLoading: isLoading,
            onNextTap: onNextTap,
            onBackTap: onBackTap,
            onSkipTap: onSkipTap
        )
    }
}

// MARK: - Preview
#Preview {
    OnboardingStep2View(
        onNextTap: {
            print("Step 2: Next tapped")
        },
        onBackTap: {
            print("Step 2: Back tapped")
        },
        onSkipTap: {
            print("Step 2: Skip tapped")
        }
    )
}
