//
//  RoleSelectionView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Role Selection View
struct RoleSelectionView: View {
    
    // MARK: - Properties
    @State private var selectedRole: UserRole?
    let onRoleSelected: (UserRole) -> Void
    let onBackTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        onRoleSelected: @escaping (UserRole) -> Void,
        onBackTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.onRoleSelected = onRoleSelected
        self.onBackTap = onBackTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Full-screen background image
            backgroundImageView
            
            // Gradient overlay - EXACT Figma specifications
            gradientOverlay
            
            // Content positioned according to Figma layout
            VStack(spacing: 0) {
                Spacer() // Push content to bottom

                // Onboarding Information Container (EXACT Figma: width: 343px, gap: 8px)
                VStack(spacing: RoleSelectionConstants.Layout.informationContainerGap) { // EXACT: "gap": "8px"
                    contentSection
                }
                .frame(width: RoleSelectionConstants.Layout.contentWidth) // EXACT: "width": "343px"

                Spacer().frame(height: 32) // Spacing between content and buttons

                // Button Container (EXACT Figma: width: 375px, padding: 0px 16px, gap: 20px)
                roleSelectionSection

                Spacer().frame(height: 60) // Bottom spacing
            }
        }
        .ignoresSafeArea(.all)
    }
    
    // MARK: - Background Image View (EXACT Figma: aspect-ratio 413/279, top-aligned, contained)
    private var backgroundImageView: some View {
        VStack(spacing: 0) {
            Image(RoleSelectionConstants.Images.backgroundImage)
                .resizable()
                .aspectRatio(RoleSelectionConstants.Images.aspectRatio, contentMode: .fit) // EXACT: aspect-ratio 413/279, contained like CSS background-size: contain
                .frame(maxWidth: .infinity)
                .clipped()

            Spacer() // Push image to top, prevent overflow
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .ignoresSafeArea(.all)
    }
    
    // MARK: - Gradient Overlay (EXACT Figma Specifications - Full Screen)
    private var gradientOverlay: some View {
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 0.10), location: 0.0), // 0% - rgba(17, 12, 29, 0.10)
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 0.0), location: 0.325), // 32.5% - rgba(17, 12, 29, 0.00)
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 1.0), location: 0.6627) // 66.27% - #110C1D
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity) // Full screen coverage
        .ignoresSafeArea(.all)
    }
    
    // MARK: - Content Section (EXACT Figma: Title and Description Container)
    private var contentSection: some View {
        // Title and Description Container (EXACT Figma: gap: 4px, align-items: flex-start)
        VStack(alignment: .leading, spacing: RoleSelectionConstants.Layout.titleToSubtitleSpacing) { // EXACT: "gap": "4px"
            // Title (EXACT Figma: Plus Jakarta Sans, 30px, 600 weight, 36px line-height, white)
            Text(RoleSelectionConstants.Text.title)
                .font(.custom(RoleSelectionConstants.Typography.titleFontName, size: RoleSelectionConstants.Typography.titleSize))
                .fontWeight(RoleSelectionConstants.Typography.titleWeight) // EXACT: "font-weight": "600"
                .foregroundColor(RoleSelectionConstants.Typography.titleColor) // EXACT: "color": "var(--Greyscale-0, #FFF)"
                .lineSpacing(RoleSelectionConstants.Typography.titleLineHeight - RoleSelectionConstants.Typography.titleSize) // EXACT: "line-height": "36px"
                .multilineTextAlignment(.leading)
                .lineLimit(nil)

            // Subtitle (EXACT Figma: Inter, 16px, 400 weight, 24px line-height, white, width: 340px)
            Text(RoleSelectionConstants.Text.subtitle)
                .font(.custom(RoleSelectionConstants.Typography.subtitleFontName, size: RoleSelectionConstants.Typography.subtitleSize))
                .fontWeight(RoleSelectionConstants.Typography.subtitleWeight) // EXACT: "font-weight": "400"
                .foregroundColor(RoleSelectionConstants.Typography.subtitleColor) // EXACT: "color": "var(--Greyscale-0, #FFF)"
                .lineSpacing(RoleSelectionConstants.Typography.subtitleLineHeight - RoleSelectionConstants.Typography.subtitleSize) // EXACT: "line-height": "24px"
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .frame(width: 340, alignment: .leading) // EXACT: "width": "340px"
        }
        .frame(maxWidth: .infinity, alignment: .leading) // EXACT: "align-items": "flex-start"
    }
    
    // MARK: - Role Selection Section (EXACT Figma: Button Container)
    private var roleSelectionSection: some View {
        // Button Container (EXACT Figma: width: 375px, padding: 0px 16px, gap: 20px)
        VStack(spacing: RoleSelectionConstants.Layout.buttonSpacing) { // EXACT: "gap": "20px"
            // Care Receiver Button (EXACT Figma: Green button with shadow)
            Button(action: {
                selectedRole = .careReceiver
                onRoleSelected(.careReceiver)
            }) {
                Text(RoleSelectionConstants.Text.careReceiverButtonTitle)
                    .font(.custom(RoleSelectionConstants.Typography.buttonFontName, size: RoleSelectionConstants.Typography.buttonSize))
                    .fontWeight(RoleSelectionConstants.Typography.buttonWeight) // EXACT: "font-weight": "500"
                    .foregroundColor(RoleSelectionConstants.Colors.careReceiverButtonText) // EXACT: "color": "var(--White, #FFF)"
                    .lineSpacing(RoleSelectionConstants.Typography.buttonLineHeight - RoleSelectionConstants.Typography.buttonSize) // EXACT: "line-height": "24px"
                    .frame(maxWidth: .infinity)
                    .frame(height: RoleSelectionConstants.Layout.buttonHeight) // EXACT: "height": "48px"
                    .background(RoleSelectionConstants.Colors.careReceiverButtonBackground) // EXACT: "background": "var(--Primary-Primary-500, #89C226)"
                    .cornerRadius(RoleSelectionConstants.Layout.buttonCornerRadius) // EXACT: "border-radius": "50px"
                    .shadow(
                        color: RoleSelectionConstants.Colors.careReceiverButtonShadow,
                        radius: 1,
                        x: 0,
                        y: 1
                    ) // EXACT: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
            }

            // Caregiver Button (EXACT Figma: White button with border and shadow)
            Button(action: {
                selectedRole = .caregiver
                onRoleSelected(.caregiver)
            }) {
                Text(RoleSelectionConstants.Text.caregiverButtonTitle)
                    .font(.custom(RoleSelectionConstants.Typography.buttonFontName, size: RoleSelectionConstants.Typography.buttonSize))
                    .fontWeight(RoleSelectionConstants.Typography.buttonWeight) // EXACT: "font-weight": "500"
                    .foregroundColor(RoleSelectionConstants.Colors.caregiverButtonText) // EXACT: "color": "var(--Grayscale-Gray-700, #344054)"
                    .lineSpacing(RoleSelectionConstants.Typography.buttonLineHeight - RoleSelectionConstants.Typography.buttonSize) // EXACT: "line-height": "24px"
                    .frame(maxWidth: .infinity)
                    .frame(height: RoleSelectionConstants.Layout.buttonHeight) // EXACT: "height": "48px"
                    .background(RoleSelectionConstants.Colors.caregiverButtonBackground) // EXACT: "background": "var(--White, #FFF)"
                    .cornerRadius(RoleSelectionConstants.Layout.buttonCornerRadius) // EXACT: "border-radius": "50px"
                    .overlay(
                        RoundedRectangle(cornerRadius: RoleSelectionConstants.Layout.buttonCornerRadius)
                            .stroke(RoleSelectionConstants.Colors.caregiverButtonBorder, lineWidth: 1) // EXACT: "border": "1px solid #E7ECF2"
                    )
                    .shadow(
                        color: RoleSelectionConstants.Colors.caregiverButtonShadow,
                        radius: 1,
                        x: 0,
                        y: 1
                    ) // EXACT: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
            }
        }
        .frame(width: RoleSelectionConstants.Layout.buttonContainerWidth) // EXACT: "width": "375px"
        .padding(.horizontal, RoleSelectionConstants.Layout.buttonContainerPadding) // EXACT: "padding": "0px 16px"
    }

}



// MARK: - Preview
#Preview {
    RoleSelectionView(
        onRoleSelected: { role in
            print("Role selected: \(role.title)")
        },
        onBackTap: {
            print("Back tapped")
        }
    )
}
