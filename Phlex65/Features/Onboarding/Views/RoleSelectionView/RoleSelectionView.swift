//
//  RoleSelectionView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Role Selection View
struct RoleSelectionView: View {
    
    // MARK: - Properties
    @State private var selectedRole: UserRole?
    let onRoleSelected: (UserRole) -> Void
    let onBackTap: (() -> Void)?
    let isLoading: Bool
    
    // MARK: - Initialization
    init(
        onRoleSelected: @escaping (UserRole) -> Void,
        onBackTap: (() -> Void)? = nil,
        isLoading: Bool = false
    ) {
        self.onRoleSelected = onRoleSelected
        self.onBackTap = onBackTap
        self.isLoading = isLoading
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Full-screen background image
            backgroundImageView
            
            // Gradient overlay - EXACT Figma specifications
            gradientOverlay
            
            // Content positioned at bottom - NO WHITE CARD
            VStack(spacing: 0) {
                Spacer()
                
                // Bottom content area - directly on image with overlay
                VStack(spacing: 0) {
                    // Main content
                    contentSection
                    
                    // Role selection cards
                    roleSelectionSection
                    
                    // Continue button
                    buttonSection
                }
                .padding(.horizontal, RoleSelectionConstants.Layout.screenPadding)
                .padding(.bottom, RoleSelectionConstants.Layout.contentBottomPadding)
            }
        }
        .ignoresSafeArea(.all)
    }
    
    // MARK: - Background Image View
    private var backgroundImageView: some View {
        Image(RoleSelectionConstants.Images.backgroundImage)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .clipped()
            .ignoresSafeArea(.all)
    }
    
    // MARK: - Gradient Overlay (EXACT Figma Specifications - Full Screen)
    private var gradientOverlay: some View {
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 0.10), location: 0.0), // 0% - rgba(17, 12, 29, 0.10)
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 0.0), location: 0.325), // 32.5% - rgba(17, 12, 29, 0.00)
                .init(color: Color(red: RoleSelectionConstants.Colors.gradientColor.red,
                                 green: RoleSelectionConstants.Colors.gradientColor.green,
                                 blue: RoleSelectionConstants.Colors.gradientColor.blue,
                                 opacity: 1.0), location: 0.6627) // 66.27% - #110C1D
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity) // Full screen coverage
        .ignoresSafeArea(.all)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: RoleSelectionConstants.Layout.titleToSubtitleSpacing) {
            // Title
            Text(RoleSelectionConstants.Text.title)
                .font(.custom(RoleSelectionConstants.Typography.titleFontName, size: RoleSelectionConstants.Typography.titleSize)
                      .weight(RoleSelectionConstants.Typography.titleWeight))
                .foregroundColor(RoleSelectionConstants.Typography.titleColor)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
            
            // Subtitle
            Text(RoleSelectionConstants.Text.subtitle)
                .font(.custom(RoleSelectionConstants.Typography.subtitleFontName, size: RoleSelectionConstants.Typography.subtitleSize)
                      .weight(RoleSelectionConstants.Typography.subtitleWeight))
                .foregroundColor(RoleSelectionConstants.Typography.subtitleColor)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - Role Selection Section
    private var roleSelectionSection: some View {
        VStack(spacing: RoleSelectionConstants.Layout.buttonSpacing) {
            // Care Receiver button (green)
            Button(action: {
                selectedRole = .careReceiver
                onRoleSelected(.careReceiver)
            }) {
                Text(RoleSelectionConstants.Text.careReceiverButtonTitle)
                    .font(.custom(
                        RoleSelectionConstants.Typography.buttonFontName,
                        size: RoleSelectionConstants.Typography.buttonSize
                    ))
                    .fontWeight(RoleSelectionConstants.Typography.buttonWeight)
                    .foregroundColor(RoleSelectionConstants.Colors.careReceiverButtonText)
                    .frame(maxWidth: .infinity)
                    .frame(height: RoleSelectionConstants.Layout.buttonHeight)
                    .background(RoleSelectionConstants.Colors.careReceiverButtonBackground)
                    .cornerRadius(RoleSelectionConstants.Layout.buttonCornerRadius)
            }

            // Caregiver button (white)
            Button(action: {
                selectedRole = .caregiver
                onRoleSelected(.caregiver)
            }) {
                Text(RoleSelectionConstants.Text.caregiverButtonTitle)
                    .font(.custom(
                        RoleSelectionConstants.Typography.buttonFontName,
                        size: RoleSelectionConstants.Typography.buttonSize
                    ))
                    .fontWeight(RoleSelectionConstants.Typography.buttonWeight)
                    .foregroundColor(RoleSelectionConstants.Colors.caregiverButtonText)
                    .frame(maxWidth: .infinity)
                    .frame(height: RoleSelectionConstants.Layout.buttonHeight)
                    .background(RoleSelectionConstants.Colors.caregiverButtonBackground)
                    .cornerRadius(RoleSelectionConstants.Layout.buttonCornerRadius)
            }
        }
        .padding(.top, RoleSelectionConstants.Layout.subtitleToButtonsSpacing)
    }
    
    // MARK: - Progress Indicator Section
    private var buttonSection: some View {
        VStack(spacing: 0) {
            // Progress indicator
            RoundedRectangle(cornerRadius: RoleSelectionConstants.Layout.progressBarCornerRadius)
                .fill(RoleSelectionConstants.Colors.progressBarBackground)
                .frame(width: RoleSelectionConstants.Layout.progressBarWidth, height: RoleSelectionConstants.Layout.progressBarHeight)
        }
        .padding(.top, RoleSelectionConstants.Layout.buttonsToProgressSpacing)
    }
}



// MARK: - Preview
#Preview {
    RoleSelectionView(
        onRoleSelected: { role in
            print("Role selected: \(role.title)")
        },
        onBackTap: {
            print("Back tapped")
        }
    )
}
