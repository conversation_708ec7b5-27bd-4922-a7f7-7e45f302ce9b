//
//  WelcomeViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

class WelcomeViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading = false

    // MARK: - Text Properties
    var welcomeText: String {
        WelcomeConstants.Text.welcomeTo
    }

    var appName: String {
        WelcomeConstants.Text.appName
    }

    var subtitle: String {
        WelcomeConstants.Text.subtitle
    }

    var primaryButtonTitle: String {
        WelcomeConstants.Text.primaryButtonTitle
    }

    var secondaryTextPrefix: String {
        WelcomeConstants.Text.secondaryTextPrefix
    }

    var secondaryTextAction: String {
        WelcomeConstants.Text.secondaryTextAction
    }

    // MARK: - Computed Properties
    var title: String {
        WelcomeConstants.Text.title
    }

    // MARK: - Image Properties
    var backgroundImageName: String {
        WelcomeConstants.Images.backgroundImage
    }

    // MARK: - Color Properties (Pixel Perfect from Figma)
    var titleMainColor: Color {
        Color(red: WelcomeConstants.Colors.titleMainColor.red,
              green: WelcomeConstants.Colors.titleMainColor.green,
              blue: WelcomeConstants.Colors.titleMainColor.blue)
    }

    var titleAccentColor: Color {
        Color(red: WelcomeConstants.Colors.titleAccentColor.red,
              green: WelcomeConstants.Colors.titleAccentColor.green,
              blue: WelcomeConstants.Colors.titleAccentColor.blue)
    }

    var subtitleColor: Color {
        Color(red: WelcomeConstants.Colors.subtitleColor.red,
              green: WelcomeConstants.Colors.subtitleColor.green,
              blue: WelcomeConstants.Colors.subtitleColor.blue)
    }

    var buttonBackgroundColor: Color {
        Color(red: WelcomeConstants.Colors.buttonBackgroundColor.red,
              green: WelcomeConstants.Colors.buttonBackgroundColor.green,
              blue: WelcomeConstants.Colors.buttonBackgroundColor.blue)
    }

    var buttonTextColor: Color {
        Color(red: WelcomeConstants.Colors.buttonTextColor.red,
              green: WelcomeConstants.Colors.buttonTextColor.green,
              blue: WelcomeConstants.Colors.buttonTextColor.blue)
    }

    var secondaryTextColor: Color {
        Color(red: WelcomeConstants.Colors.secondaryTextColor.red,
              green: WelcomeConstants.Colors.secondaryTextColor.green,
              blue: WelcomeConstants.Colors.secondaryTextColor.blue)
    }

    var backgroundWhiteColor: Color {
        Color(red: WelcomeConstants.Colors.backgroundWhite.red,
              green: WelcomeConstants.Colors.backgroundWhite.green,
              blue: WelcomeConstants.Colors.backgroundWhite.blue)
    }

    // MARK: - New Figma Color Properties
    var phlexBlueColor: Color {
        Color(red: WelcomeConstants.Colors.secondary500.red,
              green: WelcomeConstants.Colors.secondary500.green,
              blue: WelcomeConstants.Colors.secondary500.blue)
    }

    // Legacy properties for compatibility
    var primaryBlueColor: Color { titleAccentColor }
    var primaryGreenColor: Color { buttonBackgroundColor }
    var textGrayColor: Color { subtitleColor }
    var textBlackColor: Color { titleMainColor }

    // Additional legacy mappings
    var titleColor: Color { titleMainColor }
    var buttonColor: Color { buttonBackgroundColor }

    // MARK: - Layout Properties (Figma Specifications)
    var cardCornerRadius: CGFloat {
        WelcomeConstants.Layout.cardCornerRadius
    }

    var buttonHeight: CGFloat {
        WelcomeConstants.Layout.buttonHeight // 48.0 from Figma
    }

    var buttonCornerRadius: CGFloat {
        WelcomeConstants.Layout.buttonCornerRadius // 24.0 from Figma
    }

    var horizontalPadding: CGFloat {
        WelcomeConstants.Layout.horizontalPadding // 24.0 from Figma
    }

    var topPadding: CGFloat {
        WelcomeConstants.Layout.topPadding
    }

    var bottomPadding: CGFloat {
        WelcomeConstants.Layout.bottomPadding
    }

    var contentSpacing: CGFloat {
        WelcomeConstants.Layout.mainContentSpacing // 24.0 from Figma
    }

    var buttonSpacing: CGFloat {
        WelcomeConstants.Layout.buttonSpacing // 24.0 from Figma
    }

    // Additional Figma-specific layout properties
    var textContentSpacing: CGFloat {
        WelcomeConstants.Layout.textContentSpacing // 18.0 from Figma
    }

    var buttonWidth: CGFloat {
        WelcomeConstants.Layout.buttonWidth // 335.0 from Figma
    }

    // MARK: - Font Properties (Figma Specifications)
    var titleFont: Font {
        .custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.titleSize)
        .weight(WelcomeConstants.Fonts.titleWeight)
    }

    var subtitleFont: Font {
        .custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.subtitleSize)
        .weight(WelcomeConstants.Fonts.subtitleWeight)
    }

    var buttonFont: Font {
        .custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.buttonSize)
        .weight(WelcomeConstants.Fonts.buttonWeight)
    }

    var secondaryTextFont: Font {
        .custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.secondaryTextSize)
        .weight(WelcomeConstants.Fonts.secondaryTextWeight)
    }

    var secondaryActionFont: Font {
        .custom(WelcomeConstants.Fonts.fontFamily, size: WelcomeConstants.Fonts.secondaryTextSize)
        .weight(.semibold)
    }

    // MARK: - Actions
    func handleGetStarted() {
        isLoading = true
        // Handle get started action
        // This will be called by the view
    }

    func handleSignIn() {
        isLoading = true
        // Handle sign in action
        // This will be called by the view
    }
}
