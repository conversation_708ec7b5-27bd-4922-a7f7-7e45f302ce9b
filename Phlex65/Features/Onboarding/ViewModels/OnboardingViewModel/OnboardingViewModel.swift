//
//  OnboardingViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Onboarding View Model
@MainActor
class OnboardingViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var currentFlowState: OnboardingFlowState = .welcome
    @Published var isLoading = false
    @Published var showSkipAlert = false
    @Published var selectedRole: UserRole?

    // MARK: - Private Properties
    private let steps = OnboardingStep.allSteps
    
    // MARK: - Computed Properties
    var currentStep: OnboardingStep? {
        guard case .step(let index) = currentFlowState,
              index > 0 && index <= steps.count else {
            return nil
        }
        return steps[index - 1]
    }
    
    var currentStepIndex: Int {
        currentFlowState.currentStepIndex ?? 0
    }
    
    var progressValue: Double {
        guard let step = currentStep else { return 0.0 }
        return step.progressValue
    }
    
    var canGoBack: Bool {
        switch currentFlowState {
        case .step(let index):
            return index > 1
        default:
            return false
        }
    }
    
    var canSkip: Bool {
        switch currentFlowState {
        case .step:
            return true
        default:
            return false
        }
    }
    
    var isLastStep: Bool {
        currentStep?.isLastStep ?? false
    }
    
    // MARK: - Navigation Actions
    func startOnboarding() {
        withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
            currentFlowState = .step(1)
        }
    }
    
    func goToNextStep() {
        switch currentFlowState {
        case .step(let index):
            if index < OnboardingConstants.Navigation.totalSteps {
                withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
                    currentFlowState = .step(index + 1)
                }
            } else {
                // After last step, go to role selection
                goToRoleSelection()
            }
        default:
            break
        }
    }
    
    func goToPreviousStep() {
        switch currentFlowState {
        case .step(let index):
            if index > 1 {
                withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
                    currentFlowState = .step(index - 1)
                }
            } else {
                withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
                    currentFlowState = .welcome
                }
            }
        case .roleSelection:
            // Go back to last onboarding step
            withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
                currentFlowState = .step(OnboardingConstants.Navigation.totalSteps)
            }
        default:
            break
        }
    }
    
    func skipOnboarding() {
        showSkipAlert = true
    }
    
    func confirmSkip() {
        showSkipAlert = false
        completeOnboarding()
    }
    
    func cancelSkip() {
        showSkipAlert = false
    }
    
    func goToRoleSelection() {
        withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
            currentFlowState = .roleSelection
        }
    }

    func selectRole(_ role: UserRole) {
        selectedRole = role
        isLoading = true

        // Simulate loading delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.isLoading = false
            self?.completeOnboarding()
        }
    }

    func completeOnboarding() {
        withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
            currentFlowState = .completed
        }
    }
    
    // MARK: - Step-specific Actions
    func handleStepAction() {
        isLoading = true

        // Simulate loading delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.isLoading = false
            self?.goToNextStep()
        }
    }
    
    // MARK: - Helper Methods
    func getStepByIndex(_ index: Int) -> OnboardingStep? {
        guard index > 0 && index <= steps.count else { return nil }
        return steps[index - 1]
    }
    
    func resetOnboarding() {
        withAnimation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration)) {
            currentFlowState = .welcome
            isLoading = false
            showSkipAlert = false
        }
    }
}
