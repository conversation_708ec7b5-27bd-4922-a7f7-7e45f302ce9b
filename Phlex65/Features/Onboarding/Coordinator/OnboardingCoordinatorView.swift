//
//  OnboardingCoordinatorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Onboarding Coordinator View
struct OnboardingCoordinatorView: View {
    @EnvironmentObject var appCoordinator: AppCoordinator
    @StateObject private var onboardingViewModel = OnboardingViewModel()

    var body: some View {
        Group {
            switch onboardingViewModel.currentFlowState {
            case .welcome:
                WelcomeView(
                    onGetStarted: {
                        onboardingViewModel.startOnboarding()
                    },
                    onSignIn: {
                        // Navigate directly to authentication
                        appCoordinator.navigateToAuthentication()
                    }
                )
                .transition(.opacity)

            case .step(1):
                OnboardingStep1View(
                    onNextTap: {
                        onboardingViewModel.handleStepAction()
                    },
                    onSkipTap: {
                        onboardingViewModel.skipOnboarding()
                    },
                    isLoading: onboardingViewModel.isLoading
                )
                .transition(.asymmetric(insertion: .move(edge: .trailing), removal: .move(edge: .leading)))

            case .step(2):
                OnboardingStep2View(
                    onNextTap: {
                        onboardingViewModel.handleStepAction()
                    },
                    onBackTap: {
                        onboardingViewModel.goToPreviousStep()
                    },
                    onSkipTap: {
                        onboardingViewModel.skipOnboarding()
                    },
                    isLoading: onboardingViewModel.isLoading
                )
                .transition(.asymmetric(insertion: .move(edge: .trailing), removal: .move(edge: .leading)))

            case .step(3):
                OnboardingStep3View(
                    onGetStartedTap: {
                        onboardingViewModel.handleStepAction()
                    },
                    onBackTap: {
                        onboardingViewModel.goToPreviousStep()
                    },
                    isLoading: onboardingViewModel.isLoading
                )
                .transition(.asymmetric(insertion: .move(edge: .trailing), removal: .move(edge: .leading)))

            case .roleSelection:
                RoleSelectionView(
                    onRoleSelected: { role in
                        onboardingViewModel.selectRole(role)
                    },
                    onBackTap: { onboardingViewModel.goToPreviousStep() },
                    isLoading: onboardingViewModel.isLoading
                )
                .transition(.asymmetric(insertion: .move(edge: .trailing), removal: .move(edge: .leading)))

            case .completed:
                // Navigate to authentication when onboarding is completed
                Color.clear
                    .onAppear {
                        appCoordinator.navigateToAuthentication()
                    }

            default:
                WelcomeView(
                    onGetStarted: {
                        onboardingViewModel.startOnboarding()
                    },
                    onSignIn: {
                        appCoordinator.navigateToAuthentication()
                    }
                )
            }
        }
        .animation(.easeInOut(duration: OnboardingConstants.Animation.transitionDuration),
                  value: onboardingViewModel.currentFlowState)
        .alert("Skip Onboarding", isPresented: $onboardingViewModel.showSkipAlert) {
            Button("Cancel", role: .cancel) {
                onboardingViewModel.cancelSkip()
            }
            Button("Skip", role: .destructive) {
                onboardingViewModel.confirmSkip()
            }
        } message: {
            Text("Are you sure you want to skip the onboarding? You can always view this information later in settings.")
        }
    }
}

// MARK: - Preview
#Preview {
    OnboardingCoordinatorView()
        .environmentObject(AppCoordinator())
}
