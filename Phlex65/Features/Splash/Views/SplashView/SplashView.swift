//
//  SplashView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Splash View
struct SplashView: View {
    
    // MARK: - Properties
    let onCompletion: () -> Void
    let configuration: SplashConfiguration
    
    // MARK: - ViewModel
    @StateObject private var viewModel: SplashViewModel
    
    // MARK: - Initialization
    init(
        configuration: SplashConfiguration = .default,
        onCompletion: @escaping () -> Void
    ) {
        self.configuration = configuration
        self.onCompletion = onCompletion
        self._viewModel = StateObject(wrappedValue: SplashViewModel(
            configuration: configuration,
            onCompletion: onCompletion
        ))
    }
    
    // MARK: - Body
    var body: some View {
        ZStack {
            // Background
            viewModel.backgroundColor
                .ignoresSafeArea(.all)
            
            // Logo
            VStack {
                Image(viewModel.logoImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: viewModel.maxLogoWidth)
                    .scaleEffect(viewModel.logoScale)
                    .opacity(viewModel.logoOpacity)
            }
        }
        .onAppear {
            viewModel.startAnimation()
        }
        .onTapGesture {
            viewModel.skipAnimation()
        }
    }
}

// MARK: - Preview
#Preview {
    SplashView {
        print("Splash completed")
    }
}
