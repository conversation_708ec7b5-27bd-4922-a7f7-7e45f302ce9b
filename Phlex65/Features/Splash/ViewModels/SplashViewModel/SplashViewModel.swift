//
//  SplashViewModel.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Splash View Model
@MainActor
class SplashViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var logoScale: CGFloat
    @Published var logoOpacity: Double
    @Published var isAnimationComplete = false
    
    // MARK: - Private Properties
    private let configuration: SplashConfiguration
    private let onCompletion: () -> Void
    private var animationTask: Task<Void, Never>?
    
    // MARK: - Computed Properties
    var backgroundColor: Color {
        configuration.backgroundColor
    }
    
    var maxLogoWidth: CGFloat {
        configuration.maxLogoWidth
    }
    
    var logoImageName: String {
        configuration.logoImageName
    }
    
    // MARK: - Initialization
    init(configuration: SplashConfiguration, onCompletion: @escaping () -> Void) {
        self.configuration = configuration
        self.onCompletion = onCompletion
        self.logoScale = configuration.initialScale
        self.logoOpacity = configuration.initialOpacity
    }
    
    // MARK: - Public Methods
    func startAnimation() {
        guard !isAnimationComplete else { return }
        
        animationTask = Task {
            await performAnimation()
        }
    }
    
    func skipAnimation() {
        animationTask?.cancel()
        
        withAnimation(.easeOut(duration: configuration.fadeOutDuration)) {
            logoScale = configuration.finalScale
            logoOpacity = configuration.finalOpacity
            isAnimationComplete = true
        }
        
        // Small delay to let the animation complete
        Task {
            try? await Task.sleep(nanoseconds: UInt64(configuration.fadeOutDuration * 1_000_000_000))
            onCompletion()
        }
    }
    
    // MARK: - Private Methods
    private func performAnimation() async {
        // Initial animation
        withAnimation(.easeOut(duration: configuration.animationDuration)) {
            logoScale = configuration.finalScale
            logoOpacity = configuration.finalOpacity
        }
        
        // Wait for animation to complete
        try? await Task.sleep(nanoseconds: UInt64(configuration.animationDuration * 1_000_000_000))
        
        // Wait for minimum display time
        try? await Task.sleep(nanoseconds: UInt64(configuration.minimumDisplayTime * 1_000_000_000))
        
        // Check if task was cancelled
        guard !Task.isCancelled else { return }
        
        // Fade out animation
        withAnimation(.easeOut(duration: configuration.fadeOutDuration)) {
            isAnimationComplete = true
        }
        
        // Wait for fade out to complete
        try? await Task.sleep(nanoseconds: UInt64(configuration.fadeOutDuration * 1_000_000_000))
        
        // Check if task was cancelled
        guard !Task.isCancelled else { return }
        
        // Call completion
        onCompletion()
    }
    
    // MARK: - Cleanup
    deinit {
        animationTask?.cancel()
    }
}
