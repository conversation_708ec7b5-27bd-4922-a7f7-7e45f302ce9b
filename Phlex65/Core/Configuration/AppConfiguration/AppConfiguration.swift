//
//  AppConfiguration.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - App Configuration
class AppConfiguration {

    // MARK: - Singleton
    static let shared = AppConfiguration()

    // MARK: - Properties
    let environment: Environment

    // MARK: - Initialization
    private init() {
        #if DEBUG
        self.environment = .development
        #elseif STAGING
        self.environment = .staging
        #else
        self.environment = .production
        #endif
    }

    // MARK: - URLs
    var baseURL: String {
        switch environment {
        case .development:
            return APIConstants.developmentBaseURL
        case .staging:
            return APIConstants.stagingBaseURL
        case .production:
            return APIConstants.productionBaseURL
        }
    }

    var socketURL: String {
        switch environment {
        case .development:
            return APIConstants.developmentSocketURL
        case .staging:
            return APIConstants.stagingSocketURL
        case .production:
            return APIConstants.productionSocketURL
        }
    }

    // MARK: - Feature Flags
    var isDebugModeEnabled: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }

    var isLoggingEnabled: Bool {
        return isDebugModeEnabled
    }

    var isCrashReportingEnabled: Bool {
        return environment == .production
    }

    // MARK: - App Information
    var appVersion: String {
        return Bundle.main.infoDictionary?[AppConstants.shortVersionStringBundleKey] as? String ?? AppConstants.defaultAppVersion
    }

    var buildNumber: String {
        return Bundle.main.infoDictionary?[AppConstants.versionBundleKey] as? String ?? AppConstants.defaultBuildNumber
    }

    var bundleIdentifier: String {
        return Bundle.main.bundleIdentifier ?? AppConstants.defaultBundleIdentifier
    }

    // MARK: - Network Configuration
    var requestTimeout: TimeInterval {
        return 30.0
    }

    var resourceTimeout: TimeInterval {
        return 60.0
    }

    // MARK: - Debug Methods
    func printConfiguration() {
        guard isLoggingEnabled else { return }

        print(appConfigurationDebugMessage)
        print("\(environmentDebugLabel) \(environment.name)")
        print("\(baseURLDebugLabel) \(baseURL)")
        print("\(socketURLDebugLabel) \(socketURL)")
        print("\(appVersionDebugLabel) \(appVersion)")
        print("\(buildNumberDebugLabel) \(buildNumber)")
        print("\(bundleIDDebugLabel) \(bundleIdentifier)")
        print("\(debugModeDebugLabel) \(isDebugModeEnabled)")
        print("\(loggingDebugLabel) \(isLoggingEnabled)")
        print("\(crashReportingDebugLabel) \(isCrashReportingEnabled)")
    }
}
