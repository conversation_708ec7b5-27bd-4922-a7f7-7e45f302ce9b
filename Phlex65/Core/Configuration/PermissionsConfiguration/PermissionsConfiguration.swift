//
//  PermissionsConfiguration.swift
//  Phlex65
//
//  Created by Augment Agent on 18/06/2025.
//

import Foundation

// MARK: - Permissions Configuration
struct PermissionsConfiguration {
    
    // MARK: - Permission Descriptions
    struct Descriptions {
        static let camera = "This app needs access to camera to take profile pictures."
        static let photoLibrary = "This app needs access to photo library to select profile pictures."
        static let photoLibraryAdd = "This app needs access to save photos to your photo library."
    }
    
    // MARK: - Permission Keys
    struct Keys {
        static let camera = "NSCameraUsageDescription"
        static let photoLibrary = "NSPhotoLibraryUsageDescription"
        static let photoLibraryAdd = "NSPhotoLibraryAddUsageDescription"
    }
}

// MARK: - Note for Developer
/*
 For SwiftUI projects, add these permissions to your project's Info.plist or build settings:
 
 1. In Xcode, select your project target
 2. Go to Info tab
 3. Add these custom iOS target properties:
    - NSCameraUsageDescription: "This app needs access to camera to take profile pictures."
    - NSPhotoLibraryUsageDescription: "This app needs access to photo library to select profile pictures."
    - NSPhotoLibraryAddUsageDescription: "This app needs access to save photos to your photo library."
 
 Alternatively, you can add them in the project's build settings under "Custom iOS Target Properties"
 */
