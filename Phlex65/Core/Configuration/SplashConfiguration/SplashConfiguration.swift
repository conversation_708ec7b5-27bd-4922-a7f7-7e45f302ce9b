//
//  SplashConfiguration.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

// MARK: - Splash Configuration
struct SplashConfiguration {
    
    // MARK: - Animation Properties
    let animationDuration: TimeInterval
    let initialScale: CGFloat
    let finalScale: CGFloat
    let initialOpacity: Double
    let finalOpacity: Double
    
    // MARK: - Visual Properties
    let backgroundColor: Color
    let maxLogoWidth: CGFloat
    let logoImageName: String
    
    // MARK: - Timing Properties
    let minimumDisplayTime: TimeInterval
    let fadeOutDuration: TimeInterval
    
    // MARK: - Initialization
    init(
        animationDuration: TimeInterval = 1.5,
        initialScale: CGFloat = 0.8,
        finalScale: CGFloat = 1.0,
        initialOpacity: Double = 0.0,
        finalOpacity: Double = 1.0,
        backgroundColor: Color = .white,
        maxLogoWidth: CGFloat = 200,
        logoImageName: String = "AppLogo",
        minimumDisplayTime: TimeInterval = 2.0,
        fadeOutDuration: TimeInterval = 0.5
    ) {
        self.animationDuration = animationDuration
        self.initialScale = initialScale
        self.finalScale = finalScale
        self.initialOpacity = initialOpacity
        self.finalOpacity = finalOpacity
        self.backgroundColor = backgroundColor
        self.maxLogoWidth = maxLogoWidth
        self.logoImageName = logoImageName
        self.minimumDisplayTime = minimumDisplayTime
        self.fadeOutDuration = fadeOutDuration
    }
    
    // MARK: - Predefined Configurations
    static let `default` = SplashConfiguration()
    
    static let fast = SplashConfiguration(
        animationDuration: 0.8,
        minimumDisplayTime: 1.0,
        fadeOutDuration: 0.3
    )
    
    static let slow = SplashConfiguration(
        animationDuration: 2.5,
        minimumDisplayTime: 3.0,
        fadeOutDuration: 0.8
    )
    
    static let minimal = SplashConfiguration(
        animationDuration: 0.5,
        initialScale: 1.0,
        finalScale: 1.0,
        initialOpacity: 1.0,
        finalOpacity: 1.0,
        minimumDisplayTime: 0.5,
        fadeOutDuration: 0.2
    )
}
