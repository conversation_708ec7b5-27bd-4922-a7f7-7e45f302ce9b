//
//  CameraSelectionConstants.swift
//  Phlex65
//
//  Created by Augment Agent on 18/06/2025.
//

import SwiftUI

// MARK: - Camera Selection Constants (EXACT Figma JSON Specifications)
struct CameraSelectionConstants {
    
    // MARK: - Layout Constants
    struct Layout {
        // Container: width: 390px, padding: 10px 16px 36px 16px
        static let containerWidth: CGFloat = 390
        static let containerPaddingHorizontal: CGFloat = 16
        static let containerPaddingTop: CGFloat = 10
        static let containerPaddingBottom: CGFloat = 36
        static let containerGap: CGFloat = 10

        // Total height calculation for bottom sheet
        static let totalHeight: CGFloat = containerPaddingTop + tabHeight + containerGap +
                                         topBarPaddingVertical * 2 + closeIconContainerSize + containerGap +
                                         iconContainerHeight + containerPaddingBottom // ~200px
        
        // Tab indicator: width: 50px, height: 5px, border-radius: 100px
        static let tabWidth: CGFloat = 50
        static let tabHeight: CGFloat = 5
        static let tabCornerRadius: CGFloat = 100
        
        // Top Bar: padding: 10px 0px, justify-content: space-between
        static let topBarPaddingVertical: CGFloat = 10
        static let topBarPaddingHorizontal: CGFloat = 0
        
        // Close Icon Container: width: 48px, height: 48px, padding: 10px, border-radius: 9999px
        static let closeIconContainerSize: CGFloat = 48
        static let closeIconContainerPadding: CGFloat = 10
        static let closeIconContainerCornerRadius: CGFloat = 9999
        static let closeIconSize: CGFloat = 24
        
        // Delete Icon Container: width: 40px, height: 40px, padding: 8.333px, border-radius: 8332.5px
        static let deleteIconContainerSize: CGFloat = 40
        static let deleteIconContainerPadding: CGFloat = 8.333
        static let deleteIconContainerCornerRadius: CGFloat = 8332.5
        static let deleteIconSize: CGFloat = 24
        
        // Icon Group: gap: 32px
        static let iconGroupGap: CGFloat = 32
        
        // Individual Icon: gap: 10px
        static let iconItemGap: CGFloat = 10
        
        // Button: height: 64px, padding: 16px, border-radius: 9999px
        static let buttonSize: CGFloat = 64
        static let buttonPadding: CGFloat = 16
        static let buttonCornerRadius: CGFloat = 9999
        static let buttonIconSize: CGFloat = 32
        
        // Icon container total: width: 64px, height: 98px
        static let iconContainerWidth: CGFloat = 64
        static let iconContainerHeight: CGFloat = 98
    }
    
    // MARK: - Color Constants (EXACT Figma JSON Colors)
    struct Colors {
        // Background: #FFF
        static let backgroundColor = Color(hex: "#FFFFFF")
        
        // Tab indicator: #5B616E
        static let tabColor = Color(hex: "#5B616E")
        
        // Title text: #101219
        static let titleColor = Color(hex: "#101219")
        
        // Button background: #0292D9
        static let buttonBackground = Color(hex: "#0292D9")
        
        // Button border: #196AA6
        static let buttonBorder = Color(hex: "#196AA6")
        
        // Button icon: white
        static let buttonIconColor = Color.white
        
        // Label text: #25272C
        static let labelColor = Color(hex: "#25272C")
        
        // Close/Delete icon background: #FFF
        static let iconContainerBackground = Color(hex: "#FFFFFF")
    }
    
    // MARK: - Typography Constants (EXACT Figma JSON Typography)
    struct Typography {
        // Title: Inter, 20px, 600 weight, 30px line-height
        static let titleFont = Font.custom("Inter-SemiBold", size: 20)
        static let titleLineHeight: CGFloat = 30

        // Label: Inter, 14px, 500 weight, 20px line-height
        static let labelFont = Font.custom("Inter-Medium", size: 14)
        static let labelLineHeight: CGFloat = 20
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let slideUpDuration: Double = 0.3
        static let fadeInDuration: Double = 0.2
        static let springResponse: Double = 0.6
        static let springDampingFraction: Double = 0.8
    }
    
    // MARK: - Accessibility Constants
    struct Accessibility {
        static let cameraButtonIdentifier = "camera_selection_camera_button"
        static let galleryButtonIdentifier = "camera_selection_gallery_button"
        static let closeButtonIdentifier = "camera_selection_close_button"
        static let deleteButtonIdentifier = "camera_selection_delete_button"
        static let containerIdentifier = "camera_selection_container"
    }
    
    // MARK: - Text Constants
    struct Text {
        static let title = "Profile Picture"
        static let cameraLabel = "Camera"
        static let galleryLabel = "Gallery"
    }
}
