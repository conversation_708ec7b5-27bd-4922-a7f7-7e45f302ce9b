//
//  LoginConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Login Constants (EXACT Figma Design Specifications)
struct LoginConstants {

    // MARK: - Layout Constants (EXACT From Figma JSON)
    struct Layout {
        // Screen layout - Figma: width: 375px, height: 812px, padding: 0px 16px
        static let screenWidth: CGFloat = 375.0
        static let screenHeight: CGFloat = 812.0
        static let screenPadding: CGFloat = 16.0

        // Form layout - Figma: gap: 32px, 4px, 14px, 6px, 12px
        static let titleToSubtitleSpacing: CGFloat = 4.0
        static let subtitleToFormSpacing: CGFloat = 32.0
        static let fieldSpacing: CGFloat = 14.0
        static let labelToFieldSpacing: CGFloat = 6.0
        static let passwordToForgotSpacing: CGFloat = 12.0
        static let forgotToButtonSpacing: CGFloat = 32.0
        static let buttonToSignUpSpacing: CGFloat = 32.0
        static let buttonSectionSpacing: CGFloat = 24.0

        // Input field layout - Figma: height: 48px, padding: 8px 12px, border-radius: 10px
        static let fieldHeight: CGFloat = 48.0
        static let fieldCornerRadius: CGFloat = 10.0
        static let fieldBorderWidth: CGFloat = 1.0
        static let fieldHorizontalPadding: CGFloat = 12.0
        static let fieldVerticalPadding: CGFloat = 8.0

        // Label layout - Figma: width: 300px, gap: 8px
        static let labelWidth: CGFloat = 300.0
        static let labelGap: CGFloat = 8.0

        // Button layout - Figma: height: 48px, padding: 12px 20px, border-radius: 50px
        static let buttonHeight: CGFloat = 48.0
        static let buttonCornerRadius: CGFloat = 50.0
        static let buttonHorizontalPadding: CGFloat = 20.0
        static let buttonVerticalPadding: CGFloat = 12.0
        static let buttonGap: CGFloat = 8.0

        // EXACT Figma spacing values
        static let mainContainerPadding: CGFloat = 16.0 // "padding": "0px 16px"
        static let bodyGap: CGFloat = 32.0 // "gap": "32px"
        static let titleDescGap: CGFloat = 4.0 // "gap": "4px"
        static let listGap: CGFloat = 14.0 // "gap": "14px"
        static let textInputGap: CGFloat = 6.0 // "gap": "6px"
        static let passwordSectionGap: CGFloat = 12.0 // "gap": "12px"
        static let ctaGap: CGFloat = 32.0 // "gap": "32px"
        static let buttonSectionGap: CGFloat = 24.0 // "gap": "24px"
    }

    // MARK: - Typography (EXACT From Figma JSON)
    struct Typography {
        // Main title - "Sign In" - Figma: Inter 24px/600 weight, line-height: 140% (33.6px)
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 24.0
        static let titleWeight: Font.Weight = .semibold
        static let titleColor = Colors.primaryText // #0C1523

        // Subtitle - "Sign in to book professional caregivers easily." - Figma: Inter 14px/400, width: 311px
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 14.0
        static let subtitleWeight: Font.Weight = .regular
        static let subtitleColor = Colors.secondaryText // #6B7280
        static let subtitleWidth: CGFloat = 311.0

        // Field labels - "Email Address", "Password" - Figma: Inter 14px/500, letter-spacing: -0.14px
        static let labelFontName = "Inter-Medium"
        static let labelSize: CGFloat = 14.0
        static let labelWeight: Font.Weight = .medium
        static let labelColor = Colors.labelText // #101828

        // Field placeholders - Figma: Inter 14px/400, letter-spacing: -0.14px
        static let placeholderFontName = "Inter-Regular"
        static let placeholderSize: CGFloat = 14.0
        static let placeholderWeight: Font.Weight = .regular
        static let placeholderColor = Colors.placeholderText // #667085

        // Field text - Same as placeholder
        static let fieldTextFontName = "Inter-Regular"
        static let fieldTextSize: CGFloat = 14.0
        static let fieldTextWeight: Font.Weight = .regular
        static let fieldTextColor = Colors.labelText // #101828

        // Button text - Figma: Inter 16px/500, line-height: 150% (24px)
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0
        static let buttonWeight: Font.Weight = .medium
        static let buttonColor = Color.white

        // Forgot Password link - Figma: Inter 12px/500, line-height: 150% (18px), text-align: right
        static let forgotPasswordFontName = "Inter-Medium"
        static let forgotPasswordSize: CGFloat = 12.0
        static let forgotPasswordWeight: Font.Weight = .medium
        static let forgotPasswordColor = Colors.linkText // #0292D9

        // Footer text - "Dont have account? Sign Up" - Figma: Inter 14px/500, text-align: center
        static let footerFontName = "Inter-Medium"
        static let footerSize: CGFloat = 14.0
        static let footerWeight: Font.Weight = .medium
        static let footerColor = Colors.linkText // #0292D9
    }

    // MARK: - Colors (EXACT From Figma JSON)
    struct Colors {
        // Background colors - Figma: #FFF
        static let screenBackground = Color.white
        static let fieldBackground = Color.white

        // Border colors - Figma: #D8DBDF (Gray-200) - EXACT HEX
        static let fieldBorderDefault = Color(hex: "D8DBDF") // #D8DBDF
        static let fieldBorderFocused = Color(hex: "0292D9") // #0292D9
        static let fieldBorderError = Color(hex: "EF4444") // #EF4444

        // Button colors - Figma: #89C226 (Primary-500) - EXACT HEX
        static let primaryButtonBackground = Color(hex: "89C226") // #89C226
        static let primaryButtonBackgroundPressed = Color(hex: "7CB342") // Darker
        static let primaryButtonText = Color.white

        // Button shadow - Figma: 0px 1px 2px 0px rgba(16, 24, 40, 0.05)
        static let buttonShadowColor = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.05)
        static let buttonShadowRadius: CGFloat = 1.0
        static let buttonShadowX: CGFloat = 0.0
        static let buttonShadowY: CGFloat = 1.0

        // Text colors - EXACT From Figma JSON HEX values
        static let primaryText = Color(hex: "0C1523") // #0C1523 - black-500
        static let secondaryText = Color(hex: "6B7280") // #6B7280 - Gray-500
        static let placeholderText = Color(hex: "667085") // #667085 - Grayscale-Gray-500
        static let labelText = Color(hex: "101828") // #101828 - Grayscale-Gray-900
        static let linkText = Color(hex: "0292D9") // #0292D9 - Secondary-500
        static let errorText = Color(hex: "EF4444") // #EF4444

        // Status colors
        static let errorBackground = Color(red: 1.0, green: 0.95, blue: 0.95) // Light red
        static let successBackground = Color(red: 0.94, green: 0.99, blue: 0.96) // Light green
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let fieldFocusDuration: Double = 0.2
        static let buttonPressDuration: Double = 0.1
        static let errorShakeDuration: Double = 0.5
        static let loadingDuration: Double = 1.0
    }
    
    // MARK: - Validation Constants
    struct Validation {
        static let minPasswordLength: Int = 8
        static let maxPasswordLength: Int = 128
        static let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let titleIdentifier = "login_title"
        static let subtitleIdentifier = "login_subtitle"
        static let emailFieldIdentifier = "login_email_field"
        static let passwordFieldIdentifier = "login_password_field"
        static let signInButtonIdentifier = "login_sign_in_button"
        static let forgotPasswordIdentifier = "login_forgot_password_link"
        static let signUpLinkIdentifier = "login_sign_up_link"
    }
}


