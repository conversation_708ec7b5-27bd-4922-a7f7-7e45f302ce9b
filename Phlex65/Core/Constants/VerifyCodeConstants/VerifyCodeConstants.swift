//
//  VerifyCodeConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Verify Code Constants (Figma Design Specifications)
struct VerifyCodeConstants {
    
    // MARK: - Layout Constants (From Figma Design)
    struct Layout {
        // Screen layout
        static let screenPadding: CGFloat = 24.0
        static let topPadding: CGFloat = 32.0
        static let bottomPadding: CGFloat = 40.0
        
        // Header layout
        static let backButtonSize: CGFloat = 44.0
        static let backButtonToTitleSpacing: CGFloat = 24.0
        static let titleToSubtitleSpacing: CGFloat = 8.0
        static let subtitleToCodeSpacing: CGFloat = 32.0
        
        // Code input layout
        static let codeFieldSize: CGFloat = 56.0
        static let codeFieldSpacing: CGFloat = 16.0
        static let codeFieldCornerRadius: CGFloat = 12.0
        static let codeFieldBorderWidth: CGFloat = 1.0
        static let codeToResendSpacing: CGFloat = 32.0
        
        // Resend section layout
        static let resendToButtonSpacing: CGFloat = 32.0
        
        // Button layout
        static let buttonHeight: CGFloat = 56.0
        static let buttonCornerRadius: CGFloat = 28.0 // Fully rounded
        static let buttonHorizontalPadding: CGFloat = 24.0
        
        // Back button layout
        static let backButtonCornerRadius: CGFloat = 22.0
        static let backButtonIconSize: CGFloat = 20.0
        
        // Code length
        static let codeLength: Int = 4
    }
    
    // MARK: - Typography (From Figma Design)
    struct Typography {
        // Main title - "Verify Code"
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 32.0
        static let titleWeight: Font.Weight = .semibold
        static let titleColor = Color.black
        
        // Subtitle - "Please enter the code we just sent to email"
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 16.0
        static let subtitleWeight: Font.Weight = .regular
        static let subtitleColor = Color(hex: "6B7280") // Gray-500
        
        // Email highlight in subtitle
        static let emailHighlightFontName = "Inter-Medium"
        static let emailHighlightWeight: Font.Weight = .medium
        static let emailHighlightColor = Color(hex: "3B82F6") // Blue-500
        
        // Code input text
        static let codeTextFontName = "Inter-SemiBold"
        static let codeTextSize: CGFloat = 24.0
        static let codeTextWeight: Font.Weight = .semibold
        static let codeTextColor = Color.black
        
        // Resend text - "Didn't receive OTP?"
        static let resendQuestionFontName = "Inter-Regular"
        static let resendQuestionSize: CGFloat = 16.0
        static let resendQuestionWeight: Font.Weight = .regular
        static let resendQuestionColor = Color(hex: "6B7280") // Gray-500
        
        // Resend link - "Resend code"
        static let resendLinkFontName = "Inter-SemiBold"
        static let resendLinkSize: CGFloat = 16.0
        static let resendLinkWeight: Font.Weight = .semibold
        static let resendLinkColor = Color(hex: "3B82F6") // Blue-500
        
        // Button text
        static let buttonFontName = "Inter-SemiBold"
        static let buttonSize: CGFloat = 16.0
        static let buttonWeight: Font.Weight = .semibold
        static let buttonColor = Color.white
    }
    
    // MARK: - Colors (From Figma Design)
    struct Colors {
        // Background colors
        static let screenBackground = Color.white
        static let codeFieldBackground = Color.white
        
        // Border colors
        static let codeFieldBorderDefault = Color(hex: "E5E7EB") // Gray-200
        static let codeFieldBorderFocused = Color(hex: "3B82F6") // Blue-500
        static let codeFieldBorderFilled = Color(hex: "10B981") // Green-500
        static let codeFieldBorderError = Color(hex: "EF4444") // Red-500
        
        // Button colors
        static let primaryButtonBackground = Color(hex: "8BC34A") // Green from design
        static let primaryButtonBackgroundPressed = Color(hex: "7CB342") // Darker green
        static let primaryButtonBackgroundDisabled = Color(hex: "D1D5DB") // Gray-300
        static let primaryButtonText = Color.white
        static let primaryButtonTextDisabled = Color(hex: "9CA3AF") // Gray-400
        
        // Back button colors
        static let backButtonBackground = Color(hex: "F9FAFB") // Gray-50
        static let backButtonBackgroundPressed = Color(hex: "F3F4F6") // Gray-100
        static let backButtonIcon = Color(hex: "374151") // Gray-700
        static let backButtonBorder = Color(hex: "E5E7EB") // Gray-200
        
        // Text colors
        static let primaryText = Color.black
        static let secondaryText = Color(hex: "6B7280") // Gray-500
        static let linkText = Color(hex: "3B82F6") // Blue-500
        static let errorText = Color(hex: "EF4444") // Red-500
        static let successText = Color(hex: "10B981") // Green-500
        
        // Status colors
        static let errorBackground = Color(hex: "FEF2F2") // Red-50
        static let successBackground = Color(hex: "F0FDF4") // Green-50
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let codeFieldFocusDuration: Double = 0.2
        static let buttonPressDuration: Double = 0.1
        static let backButtonPressDuration: Double = 0.1
        static let errorShakeDuration: Double = 0.5
        static let loadingDuration: Double = 1.0
        static let successFeedbackDuration: Double = 2.0
        static let codeAutoAdvanceDuration: Double = 0.1
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let backButtonIdentifier = "verify_code_back_button"
        static let titleIdentifier = "verify_code_title"
        static let subtitleIdentifier = "verify_code_subtitle"
        static let codeFieldIdentifier = "verify_code_field"
        static let resendButtonIdentifier = "verify_code_resend_button"
        static let verifyButtonIdentifier = "verify_code_verify_button"
    }
    
    // MARK: - Validation
    struct Validation {
        static let codePattern = "^[0-9]{4}$"
        static let resendCooldownSeconds: Int = 30
    }
}
