//
//  VerifyCodeConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Verify Code Constants (Figma Design Specifications)
struct VerifyCodeConstants {
    
    // MARK: - Layout Constants (EXACT Figma Design)
    struct Layout {
        // Screen layout (EXACT Figma: padding: "0px 16px")
        static let screenPadding: CGFloat = 16.0
        static let topPadding: CGFloat = 32.0
        static let bottomPadding: CGFloat = 40.0

        // Header layout
        static let backButtonSize: CGFloat = 44.0
        static let backButtonToTitleSpacing: CGFloat = 24.0
        static let titleToSubtitleSpacing: CGFloat = 3.948 // EXACT: gap: "3.948px"
        static let subtitleToCodeSpacing: CGFloat = 36.0 // EXACT: gap: "36px"

        // Code input layout (EXACT Figma measurements)
        static let codeFieldWidth: CGFloat = 57.0 // EXACT: "width": "57px"
        static let codeFieldHeight: CGFloat = 41.276 // EXACT: "height": "41.276px"
        static let codeFieldSpacing: CGFloat = 16.0 // EXACT: gap: "16px"
        static let codeFieldCornerRadius: CGFloat = 12.0 // EXACT: "border-radius": "12px"
        static let codeFieldBorderWidth: CGFloat = 1.0
        static let codeToResendSpacing: CGFloat = 32.0

        // Resend section layout
        static let resendToButtonSpacing: CGFloat = 32.0 // EXACT: gap: "32px"

        // Button layout (EXACT Figma measurements)
        static let buttonHeight: CGFloat = 48.0 // EXACT: "height": "48px"
        static let buttonCornerRadius: CGFloat = 50.0 // EXACT: "border-radius": "50px"
        static let buttonHorizontalPadding: CGFloat = 20.0 // EXACT: padding: "12px 20px"
        static let buttonVerticalPadding: CGFloat = 12.0 // EXACT: padding: "12px 20px"

        // Back button layout (EXACT Figma measurements)
        static let backButtonCornerRadius: CGFloat = 33.333 // EXACT: "border-radius": "33.333px"
        static let backButtonPadding: CGFloat = 10.0 // EXACT: padding: "10px"
        static let backButtonIconSize: CGFloat = 24.0 // EXACT: "width": "24px", "height": "24px"

        // Code length
        static let codeLength: Int = 4

        // Subtitle width (EXACT Figma measurement)
        static let subtitleWidth: CGFloat = 260.0 // EXACT: "width": "260px"
    }
    
    // MARK: - Typography (EXACT Figma Design with Proper Font Names)
    struct Typography {
        // Main title - "Verify Code" (EXACT Figma: 20px Inter Medium)
        static let titleFontName = "Inter-Medium"
        static let titleSize: CGFloat = 20.0 // EXACT: "font-size": "20px"
        static let titleWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let titleColor = Color(hex: "242424") // EXACT: "color": "var(--main-black, #242424)"

        // Subtitle - "Please enter the code..." (EXACT Figma: 12px Inter Regular, Grey)
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 12.0 // EXACT: "font-size": "12px"
        static let subtitleWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let subtitleColor = Color(hex: "797979") // EXACT: Grey color for main text

        // Email highlight in subtitle (EXACT Figma: Blue color for email only)
        static let emailHighlightFontName = "Inter-Regular"
        static let emailHighlightWeight: Font.Weight = .regular
        static let emailHighlightColor = Color(hex: "0292D9") // EXACT: "color": "var(--Secondary-Secondary-500, #0292D9)" for email only

        // Code input text (EXACT Figma: 12px Inter Regular)
        static let codeTextFontName = "Inter-Regular"
        static let codeTextSize: CGFloat = 12.0 // EXACT: "font-size": "12px"
        static let codeTextWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let codeTextColor = Color(hex: "242424") // EXACT: "color": "var(--main-black, #242424)"

        // Resend text - "Didn't receive OTP?" (EXACT Figma: 12px Inter Regular)
        static let resendQuestionFontName = "Inter-Regular"
        static let resendQuestionSize: CGFloat = 12.0 // EXACT: "font-size": "12px"
        static let resendQuestionWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let resendQuestionColor = Color(hex: "797979") // EXACT: "color": "var(--secoundry-black, #797979)"

        // Resend link - "Resend code" (EXACT Figma: 12px Inter SemiBold)
        static let resendLinkFontName = "Inter-SemiBold"
        static let resendLinkSize: CGFloat = 12.0 // EXACT: "font-size": "12px"
        static let resendLinkWeight: Font.Weight = .semibold // EXACT: "font-weight": "600"
        static let resendLinkColor = Color(hex: "0292D9") // EXACT: "color": "var(--Secondary-Secondary-500, #0292D9)"

        // Button text (EXACT Figma: 16px Inter Medium)
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0 // EXACT: "font-size": "16px"
        static let buttonWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let buttonColor = Color.white // EXACT: "color": "var(--White, #FFF)"
    }
    
    // MARK: - Colors (From Figma Design)
    struct Colors {
        // Background colors
        static let screenBackground = Color.white
        static let codeFieldBackground = Color.white
        
        // Border colors (EXACT Figma specifications)
        static let codeFieldBorderDefault = Color(hex: "D8DBDF") // EXACT: "border": "1px solid var(--Gray-200, #D8DBDF)"
        static let codeFieldBorderFocused = Color(hex: "0292D9") // Blue for focused state
        static let codeFieldBorderFilled = Color(hex: "D8DBDF") // Same as default when filled
        static let codeFieldBorderError = Color(hex: "EF4444") // Red for error
        static let codeFieldBorderStroke = Color(hex: "000000").opacity(0.10) // EXACT: "border": "1px solid var(--stroke, rgba(0, 0, 0, 0.10))"

        // Button colors (EXACT Figma specifications)
        static let primaryButtonBackground = Color(hex: "89C226") // EXACT: "background": "var(--Primary-Primary-500, #89C226)"
        static let primaryButtonBackgroundPressed = Color(hex: "7CB342") // Darker green for pressed state
        static let primaryButtonBackgroundDisabled = Color(hex: "D1D5DB") // Gray for disabled
        static let primaryButtonText = Color.white // EXACT: "color": "var(--White, #FFF)"
        static let primaryButtonTextDisabled = Color(hex: "9CA3AF") // Gray for disabled text

        // Back button colors (EXACT Figma specifications)
        static let backButtonBackground = Color.white // EXACT: "background": "var(--Primary-Colors-White-White, #FFF)"
        static let backButtonBackgroundPressed = Color(hex: "F3F4F6") // Gray for pressed state
        static let backButtonIcon = Color.black // Icon color
        static let backButtonBorder = Color(hex: "D8DBDF") // EXACT: "border": "1px solid var(--Gray-200, #D8DBDF)"
        
        // Text colors
        static let primaryText = Color.black
        static let secondaryText = Color(hex: "6B7280") // Gray-500
        static let linkText = Color(hex: "3B82F6") // Blue-500
        static let errorText = Color(hex: "EF4444") // Red-500
        static let successText = Color(hex: "10B981") // Green-500
        
        // Status colors
        static let errorBackground = Color(hex: "FEF2F2") // Red-50
        static let successBackground = Color(hex: "F0FDF4") // Green-50
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let codeFieldFocusDuration: Double = 0.2
        static let buttonPressDuration: Double = 0.1
        static let backButtonPressDuration: Double = 0.1
        static let errorShakeDuration: Double = 0.5
        static let loadingDuration: Double = 1.0
        static let successFeedbackDuration: Double = 2.0
        static let codeAutoAdvanceDuration: Double = 0.1
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let backButtonIdentifier = "verify_code_back_button"
        static let titleIdentifier = "verify_code_title"
        static let subtitleIdentifier = "verify_code_subtitle"
        static let codeFieldIdentifier = "verify_code_field"
        static let resendButtonIdentifier = "verify_code_resend_button"
        static let verifyButtonIdentifier = "verify_code_verify_button"
    }
    
    // MARK: - Validation
    struct Validation {
        static let codePattern = "^[0-9]{4}$"
        static let resendCooldownSeconds: Int = 30
    }
}
