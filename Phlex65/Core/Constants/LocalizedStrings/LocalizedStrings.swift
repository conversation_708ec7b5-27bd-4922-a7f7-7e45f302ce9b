//
//  LocalizedStrings.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Localized Strings
// MARK: - General
let okText = NSLocalizedString("OK", comment: "OK button text")
let cancelText = NSLocalizedString("Cancel", comment: "Cancel button text")
let saveText = NSLocalizedString("Save", comment: "Save button text")
let deleteText = NSLocalizedString("Delete", comment: "Delete button text")
let editText = NSLocalizedString("Edit", comment: "Edit button text")
let doneText = NSLocalizedString("Done", comment: "Done button text")
let nextText = NSLocalizedString("Next", comment: "Next button text")
let backText = NSLocalizedString("Back", comment: "Back button text")
let retryText = NSLocalizedString("Retry", comment: "Retry button text")
let loadingText = NSLocalizedString("Loading...", comment: "Loading text")

// MARK: - Navigation
let homeText = NSLocalizedString("Home", comment: "Home tab title")
let searchText = NSLocalizedString("Search", comment: "Search tab title")
let bookingsText = NSLocalizedString("Bookings", comment: "Bookings tab title")
let profileText = NSLocalizedString("Profile", comment: "Profile tab title")

// MARK: - Authentication
let signInText = NSLocalizedString("Sign In", comment: "Sign in button text")
let signUpText = NSLocalizedString("Sign Up", comment: "Sign up button text")
let loginText = NSLocalizedString("Login", comment: "Login button text")
let registerText = NSLocalizedString("Register", comment: "Register button text")
let forgotPasswordText = NSLocalizedString("Forgot Password", comment: "Forgot password button text")
let emailAddressLabel = NSLocalizedString("Email Address", comment: "Email address label")
let passwordLabel = NSLocalizedString("Password", comment: "Password label")
let emailPlaceholder = NSLocalizedString("Enter Email Address", comment: "Email placeholder")
let passwordPlaceholder = NSLocalizedString("Enter Password", comment: "Password placeholder")
let confirmPasswordPlaceholder = NSLocalizedString("Confirm Password", comment: "Confirm password placeholder")
let firstNamePlaceholder = NSLocalizedString("First Name", comment: "First name placeholder")
let signInSubtitle = NSLocalizedString("Sign in to book professional caregivers easily.", comment: "Sign in subtitle")
let dontHaveAccountText = NSLocalizedString("Dont have account?", comment: "Don't have account text")

// MARK: - Forgot Password
let forgotPasswordTitle = NSLocalizedString("Forgot Password", comment: "Forgot password title")
let forgotPasswordSubtitle = NSLocalizedString("Enter your registered email to receive a password reset link", comment: "Forgot password subtitle")
let sendResetLinkText = NSLocalizedString("Send Reset Link", comment: "Send reset link button text")
let resetLinkSentTitle = NSLocalizedString("Reset Link Sent", comment: "Reset link sent title")
let resetLinkSentMessage = NSLocalizedString("We've sent a password reset link to your email address. Please check your inbox and follow the instructions.", comment: "Reset link sent message")
let backToLoginText = NSLocalizedString("Back to Login", comment: "Back to login button text")

// MARK: - Verify Code
let verifyCodeTitle = NSLocalizedString("Verify Code", comment: "Verify code title")
let verifyCodeSubtitle = NSLocalizedString("Please enter the code we just sent to email", comment: "Verify code subtitle")
let didntReceiveOTPText = NSLocalizedString("Didn't receive OTP?", comment: "Didn't receive OTP text")
let resendCodeText = NSLocalizedString("Resend code", comment: "Resend code button text")
let verifyButtonText = NSLocalizedString("Verify", comment: "Verify button text")
let invalidCodeMessage = NSLocalizedString("Invalid verification code. Please try again.", comment: "Invalid code error message")
let codeExpiredMessage = NSLocalizedString("Verification code has expired. Please request a new one.", comment: "Code expired error message")
let resendCodeSuccessMessage = NSLocalizedString("Verification code sent successfully!", comment: "Resend code success message")
let lastNamePlaceholder = NSLocalizedString("Last Name", comment: "Last name placeholder")

// MARK: - Error Messages
let genericErrorMessage = NSLocalizedString("Something went wrong. Please try again.", comment: "Generic error message")
let networkErrorMessage = NSLocalizedString("Network connection error. Please check your internet connection.", comment: "Network error message")
let invalidEmailMessage = NSLocalizedString("Please enter a valid email address.", comment: "Invalid email error message")
let invalidPasswordMessage = NSLocalizedString("Password must be at least 8 characters long.", comment: "Invalid password error message")
let passwordMismatchMessage = NSLocalizedString("Passwords do not match.", comment: "Password mismatch error message")
let emptyEmailMessage = NSLocalizedString("Email address is required.", comment: "Empty email error message")
let emptyPasswordMessage = NSLocalizedString("Password is required.", comment: "Empty password error message")
let loginFailedMessage = NSLocalizedString("Invalid email or password. Please try again.", comment: "Login failed error message")
let requiredFieldMessage = NSLocalizedString("This field is required.", comment: "Required field error message")
let invalidURLErrorMessage = NSLocalizedString("Invalid URL", comment: "Invalid URL error message")
let invalidResponseErrorMessage = NSLocalizedString("Invalid response from server", comment: "Invalid response error message")
let noInternetConnectionErrorMessage = NSLocalizedString("No internet connection", comment: "No internet connection error message")
let timeoutErrorMessage = NSLocalizedString("Request timed out", comment: "Timeout error message")
let unauthorizedErrorMessage = NSLocalizedString("Unauthorized access", comment: "Unauthorized error message")
let decodingErrorMessage = NSLocalizedString("Failed to decode response", comment: "Decoding error message")
let unknownErrorMessage = NSLocalizedString("Unknown error occurred", comment: "Unknown error message")

// MARK: - Success Messages
let loginSuccessMessage = NSLocalizedString("Login successful!", comment: "Login success message")
let registrationSuccessMessage = NSLocalizedString("Registration successful!", comment: "Registration success message")
let passwordResetSuccessMessage = NSLocalizedString("Password reset email sent!", comment: "Password reset success message")
let profileUpdateSuccessMessage = NSLocalizedString("Profile updated successfully!", comment: "Profile update success message")

// MARK: - Recovery Suggestions
let checkInternetConnectionMessage = NSLocalizedString("Please check your internet connection and try again.", comment: "Check internet connection message")
let tryAgainLaterMessage = NSLocalizedString("Please try again later.", comment: "Try again later message")
let pleaseLoginAgainMessage = NSLocalizedString("Please login again.", comment: "Please login again message")
let contactSupportMessage = NSLocalizedString("Please contact support if the problem persists.", comment: "Contact support message")

// MARK: - Empty States
let noItemsFoundTitle = NSLocalizedString("No Items Found", comment: "No items found title")
let noItemsFoundMessage = NSLocalizedString("There are no items to display at the moment.", comment: "No items found message")

// MARK: - Debug Messages
let appConfigurationDebugMessage = "🔧 App Configuration"
let environmentDebugLabel = "Environment:"
let baseURLDebugLabel = "Base URL:"
let socketURLDebugLabel = "Socket URL:"
let appVersionDebugLabel = "App Version:"
let buildNumberDebugLabel = "Build Number:"
let bundleIDDebugLabel = "Bundle ID:"
let debugModeDebugLabel = "Debug Mode:"
let loggingDebugLabel = "Logging:"
let crashReportingDebugLabel = "Crash Reporting:"
