//
//  ForgotPasswordConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Forgot Password Constants (Figma Design Specifications)
struct ForgotPasswordConstants {
    
    // MARK: - Layout Constants (From Figma Design)
    struct Layout {
        // Screen layout
        static let screenPadding: CGFloat = 24.0
        static let topPadding: CGFloat = 32.0
        static let bottomPadding: CGFloat = 40.0
        
        // Header layout
        static let backButtonSize: CGFloat = 44.0
        static let backButtonToTitleSpacing: CGFloat = 24.0
        static let titleToSubtitleSpacing: CGFloat = 8.0
        static let subtitleToFormSpacing: CGFloat = 32.0
        
        // Form layout
        static let fieldSpacing: CGFloat = 24.0
        static let fieldToButtonSpacing: CGFloat = 32.0
        
        // Input field layout
        static let fieldHeight: CGFloat = 56.0
        static let fieldCornerRadius: CGFloat = 12.0
        static let fieldBorderWidth: CGFloat = 1.0
        static let fieldHorizontalPadding: CGFloat = 16.0
        static let fieldVerticalPadding: CGFloat = 16.0
        
        // Button layout
        static let buttonHeight: CGFloat = 56.0
        static let buttonCornerRadius: CGFloat = 28.0 // Fully rounded
        static let buttonHorizontalPadding: CGFloat = 24.0
        
        // Back button layout
        static let backButtonCornerRadius: CGFloat = 22.0
        static let backButtonIconSize: CGFloat = 20.0
    }
    
    // MARK: - Typography (From Figma Design)
    struct Typography {
        // Main title - "Forgot Password"
        static let titleFontName = "Inter-SemiBold"
        static let titleSize: CGFloat = 32.0
        static let titleWeight: Font.Weight = .semibold
        static let titleColor = Color.black
        
        // Subtitle - "Enter your registered email to receive a password reset link"
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 16.0
        static let subtitleWeight: Font.Weight = .regular
        static let subtitleColor = Color(hex: "6B7280") // Gray-500
        
        // Field labels - "Email Address"
        static let labelFontName = "Inter-Medium"
        static let labelSize: CGFloat = 14.0
        static let labelWeight: Font.Weight = .medium
        static let labelColor = Color.black
        
        // Field placeholders
        static let placeholderFontName = "Inter-Regular"
        static let placeholderSize: CGFloat = 16.0
        static let placeholderWeight: Font.Weight = .regular
        static let placeholderColor = Color(hex: "9CA3AF") // Gray-400
        
        // Field text
        static let fieldTextFontName = "Inter-Regular"
        static let fieldTextSize: CGFloat = 16.0
        static let fieldTextWeight: Font.Weight = .regular
        static let fieldTextColor = Color.black
        
        // Button text
        static let buttonFontName = "Inter-SemiBold"
        static let buttonSize: CGFloat = 16.0
        static let buttonWeight: Font.Weight = .semibold
        static let buttonColor = Color.white
    }
    
    // MARK: - Colors (From Figma Design)
    struct Colors {
        // Background colors
        static let screenBackground = Color.white
        static let fieldBackground = Color.white
        
        // Border colors
        static let fieldBorderDefault = Color(hex: "E5E7EB") // Gray-200
        static let fieldBorderFocused = Color(hex: "3B82F6") // Blue-500
        static let fieldBorderError = Color(hex: "EF4444") // Red-500
        
        // Button colors
        static let primaryButtonBackground = Color(hex: "8BC34A") // Green from design
        static let primaryButtonBackgroundPressed = Color(hex: "7CB342") // Darker green
        static let primaryButtonText = Color.white
        
        // Back button colors
        static let backButtonBackground = Color(hex: "F9FAFB") // Gray-50
        static let backButtonBackgroundPressed = Color(hex: "F3F4F6") // Gray-100
        static let backButtonIcon = Color(hex: "374151") // Gray-700
        static let backButtonBorder = Color(hex: "E5E7EB") // Gray-200
        
        // Text colors
        static let primaryText = Color.black
        static let secondaryText = Color(hex: "6B7280") // Gray-500
        static let placeholderText = Color(hex: "9CA3AF") // Gray-400
        static let errorText = Color(hex: "EF4444") // Red-500
        
        // Status colors
        static let errorBackground = Color(hex: "FEF2F2") // Red-50
        static let successBackground = Color(hex: "F0FDF4") // Green-50
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let fieldFocusDuration: Double = 0.2
        static let buttonPressDuration: Double = 0.1
        static let backButtonPressDuration: Double = 0.1
        static let errorShakeDuration: Double = 0.5
        static let loadingDuration: Double = 1.0
        static let successFeedbackDuration: Double = 2.0
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let backButtonIdentifier = "forgot_password_back_button"
        static let titleIdentifier = "forgot_password_title"
        static let subtitleIdentifier = "forgot_password_subtitle"
        static let emailFieldIdentifier = "forgot_password_email_field"
        static let sendResetButtonIdentifier = "forgot_password_send_reset_button"
    }
    
    // MARK: - Success Messages
    struct Messages {
        static let resetLinkSentTitle = "Reset Link Sent"
        static let resetLinkSentMessage = "We've sent a password reset link to your email address. Please check your inbox and follow the instructions."
        static let resetLinkSentButtonText = "Back to Login"
    }
}
