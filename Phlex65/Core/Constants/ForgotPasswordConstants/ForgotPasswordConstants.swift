//
//  ForgotPasswordConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Forgot Password Constants (Figma Design Specifications)
struct ForgotPasswordConstants {
    
    // MARK: - Layout Constants (Pixel Perfect from Figma Design)
    struct Layout {
        // Screen layout - EXACT Figma specifications
        static let screenPadding: CGFloat = 16.0 // Figma: padding: 0px 16px
        static let topPadding: CGFloat = 32.0
        static let bottomPadding: CGFloat = 40.0

        // Layout - EXACT Figma JSON Line-by-Line
        static let backButtonSize: CGFloat = 44.0 // "padding": "10px" + "width": "24px" = 44px total
        static let backButtonToTitleSpacing: CGFloat = 32.0 // Main content gap
        static let titleToSubtitleSpacing: CGFloat = 3.948 // EXACT: "gap": "3.948px"
        static let headerToFormSpacing: CGFloat = 32.0 // EXACT: main container "gap": "32px"
        static let subtitleWidth: CGFloat = 260.0 // EXACT: "width": "260px"

        // Form layout - EXACT Figma JSON
        static let fieldToButtonSpacing: CGFloat = 12.0 // EXACT: form section "gap": "12px"

        // Back button - EXACT Figma JSON
        static let backButtonIconSize: CGFloat = 24.0 // EXACT: "width": "24px", "height": "24px"
        static let backButtonPadding: CGFloat = 10.0 // EXACT: "padding": "10px"
        static let backButtonCornerRadius: CGFloat = 33.333 // EXACT: "border-radius": "33.333px"
        static let backButtonBorderWidth: CGFloat = 1.0 // EXACT: "border": "1px solid"

        // Input field layout - EXACT Figma specifications
        static let fieldHeight: CGFloat = 48.0 // Figma: height: 48px
        static let fieldCornerRadius: CGFloat = 10.0 // Figma: border-radius: 10px
        static let fieldBorderWidth: CGFloat = 1.0 // Figma: border: 1px solid
        static let fieldHorizontalPadding: CGFloat = 12.0 // Figma: padding: 8px 12px
        static let fieldVerticalPadding: CGFloat = 8.0 // Figma: padding: 8px 12px

        // Button layout - EXACT Figma specifications
        static let buttonHeight: CGFloat = 48.0 // Figma: height: 48px
        static let buttonCornerRadius: CGFloat = 50.0 // Figma: border-radius: 50px
        static let buttonHorizontalPadding: CGFloat = 20.0 // Figma: padding: 12px 20px
        static let buttonVerticalPadding: CGFloat = 12.0 // Figma: padding: 12px 20px


    }
    
    // MARK: - Typography (EXACT Figma Design)
    struct Typography {
        // Title: Large, bold "Forgot Password" - matching Figma design
        static let titleFontName = "Inter"
        static let titleSize: CGFloat = 24.0 // Figma design shows larger title
        static let titleWeight: Font.Weight = .semibold // Figma shows semibold weight
        static let titleColor = Color(hex: "242424") // Dark text color

        // Subtitle: Smaller text below title - matching Figma design
        static let subtitleFontName = "Inter"
        static let subtitleSize: CGFloat = 14.0 // Figma design shows 14px
        static let subtitleWeight: Font.Weight = .regular // Regular weight
        static let subtitleColor = Color(hex: "6B7280") // Gray text color

        // Field labels - Figma: Inter 14px/500, line-height: 20px (142.857%)
        static let labelFontName = "Inter-Medium"
        static let labelSize: CGFloat = 14.0 // Figma: font-size: 14px
        static let labelWeight: Font.Weight = .medium // Figma: font-weight: 500
        static let labelColor = Color(hex: "101828") // Figma: #101828

        // Field placeholders - Figma: Inter 14px/400, line-height: 20px (142.857%)
        static let placeholderFontName = "Inter-Regular"
        static let placeholderSize: CGFloat = 14.0 // Figma: font-size: 14px
        static let placeholderWeight: Font.Weight = .regular // Figma: font-weight: 400
        static let placeholderColor = Color(hex: "667085") // Figma: #667085

        // Field text - Figma: Inter 14px/400, line-height: 20px (142.857%)
        static let fieldTextFontName = "Inter-Regular"
        static let fieldTextSize: CGFloat = 14.0 // Figma: font-size: 14px
        static let fieldTextWeight: Font.Weight = .regular // Figma: font-weight: 400
        static let fieldTextColor = Color(hex: "101828") // Figma: #101828

        // Button text - Figma: Inter 16px/500, line-height: 24px (150%)
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0 // Figma: font-size: 16px
        static let buttonWeight: Font.Weight = .medium // Figma: font-weight: 500
        static let buttonColor = Color.white // Figma: #FFF
    }
    
    // MARK: - Colors (EXACT Figma Design Specifications)
    struct Colors {
        // Background colors - EXACT Figma specifications
        static let screenBackground = Color.white // Figma: #FFF
        static let fieldBackground = Color.white // Figma: #FFF

        // Border colors - EXACT Figma specifications
        static let fieldBorderDefault = Color(hex: "D8DBDF") // Figma: #D8DBDF
        static let fieldBorderFocused = Color(hex: "0292D9") // Figma: #0292D9
        static let fieldBorderError = Color(hex: "EF4444") // Red-500

        // Button colors - EXACT Figma specifications
        static let primaryButtonBackground = Color(hex: "89C226") // Figma: #89C226
        static let primaryButtonBackgroundPressed = Color(hex: "7CB342") // Darker green
        static let primaryButtonText = Color.white // Figma: #FFF

        // Back button colors - EXACT Figma specifications
        static let backButtonBackground = Color.white // Figma: #FFF
        static let backButtonBackgroundPressed = Color(hex: "F3F4F6") // Gray-100
        static let backButtonIcon = Color(hex: "0C1523") // Figma: #0C1523
        static let backButtonBorder = Color(hex: "D8DBDF") // Figma: #D8DBDF

        // Text colors - EXACT Figma specifications
        static let primaryText = Color(hex: "0C1523") // Figma: #0C1523
        static let secondaryText = Color(hex: "6B7280") // Figma: #6B7280
        static let placeholderText = Color(hex: "667085") // Figma: #667085
        static let errorText = Color(hex: "EF4444") // Red-500

        // Status colors
        static let errorBackground = Color(hex: "FEF2F2") // Red-50
        static let successBackground = Color(hex: "F0FDF4") // Green-50
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let fieldFocusDuration: Double = 0.2
        static let buttonPressDuration: Double = 0.1
        static let backButtonPressDuration: Double = 0.1
        static let errorShakeDuration: Double = 0.5
        static let loadingDuration: Double = 1.0
        static let successFeedbackDuration: Double = 2.0
    }
    
    // MARK: - Accessibility
    struct Accessibility {
        static let backButtonIdentifier = "forgot_password_back_button"
        static let titleIdentifier = "forgot_password_title"
        static let subtitleIdentifier = "forgot_password_subtitle"
        static let emailFieldIdentifier = "forgot_password_email_field"
        static let sendResetButtonIdentifier = "forgot_password_send_reset_button"
    }
    
    // MARK: - Success Messages
    struct Messages {
        static let resetLinkSentTitle = "Reset Link Sent"
        static let resetLinkSentMessage = "We've sent a password reset link to your email address. Please check your inbox and follow the instructions."
        static let resetLinkSentButtonText = "Back to Login"
    }
}
