//
//  WelcomeConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

struct WelcomeConstants {

    // MARK: - Text Constants
    struct Text {
        static let title = "Welcome to Phlex65" // Complete title from Figma
        static let welcomeTo = "Welcome to "
        static let appName = "Phlex65"
        static let subtitle = "Book appointments effortlessly and manage your health journey."
        static let primaryButtonTitle = "Let's Get Started"
        static let secondaryTextPrefix = "Already have an account? "
        static let secondaryTextAction = "Sign In"
    }

    // MARK: - Image Constants
    struct Images {
        static let backgroundImage = "WelcomeBackground"
        static let aspectRatio: CGFloat = 0.69 // 375/542 ≈ 0.692
    }

    // MARK: - Layout Constants (Pixel Perfect from Figma)
    struct Layout {
        // Card layout - EXACT Figma specifications
        static let cardCornerRadius: CGFloat = 30.0 // Figma: 30.0 (top corners only)
        static let cardWidth: CGFloat = 375.0 // Figma: 375.0
        static let cardHeight: CGFloat = 294.0 // Figma: 294.0

        // Button layout - EXACT Figma specifications
        static let buttonHeight: CGFloat = 48.0 // Figma: 48.0
        static let buttonWidth: CGFloat = 335.0 // Figma: 335.0
        static let buttonCornerRadius: CGFloat = 50.0 // Figma: 50.0 (fully rounded)
        static let buttonPaddingHorizontal: CGFloat = 20.0 // Figma: 20.0
        static let buttonPaddingVertical: CGFloat = 12.0 // Figma: 12.0

        // Content spacing - EXACT Figma specifications
        static let horizontalPadding: CGFloat = 28.0 // Figma: content frame padding
        static let contentHorizontalPadding: CGFloat = 28.0 // Figma: 28.0 (content frame)
        static let topPadding: CGFloat = 32.0
        static let bottomPadding: CGFloat = 40.0

        // Spacing between elements - EXACT Figma specifications
        static let mainContentSpacing: CGFloat = 24.0 // Figma: itemSpacing 24.0
        static let textContentSpacing: CGFloat = 18.0 // Figma: itemSpacing 18.0 (title to subtitle)
        static let buttonSpacing: CGFloat = 24.0 // Space between button and secondary text

        // Text layout - EXACT Figma specifications
        static let titleWidth: CGFloat = 313.0 // Figma: 313.0
        static let titleHeight: CGFloat = 29.0 // Figma: 29.0
        static let subtitleWidth: CGFloat = 313.0 // Figma: 313.0
        static let subtitleHeight: CGFloat = 42.0 // Figma: 42.0
        static let secondaryTextWidth: CGFloat = 251.0 // Figma: 251.0
        static let secondaryTextHeight: CGFloat = 24.0 // Figma: 24.0
    }

    // MARK: - Font Constants (Pixel Perfect from Figma)
    struct Fonts {
        // Title: "Welcome to Phlex65" - EXACT Figma specs
        static let titleSize: CGFloat = 24.0 // Figma: 24.0
        static let titleWeight: Font.Weight = .semibold // Figma: 600 (SemiBold)
        static let titleLineHeight: CGFloat = 29.045454025268555 // Figma: exact value

        // Subtitle: "Book appointments..." - EXACT Figma specs
        static let subtitleSize: CGFloat = 14.0 // Figma: 14.0
        static let subtitleWeight: Font.Weight = .regular // Figma: 400 (Regular)
        static let subtitleLineHeight: CGFloat = 21.0 // Figma: 21.0

        // Button: "Get Started" - EXACT Figma specs
        static let buttonSize: CGFloat = 16.0 // Figma: 16.0
        static let buttonWeight: Font.Weight = .medium // Figma: 500 (Medium)
        static let buttonLineHeight: CGFloat = 24.0 // Figma: 24.0

        // Secondary text: "Already have an account?" - EXACT Figma specs
        static let secondaryTextSize: CGFloat = 14.0 // Figma: 14.0 (corrected from 16.0)
        static let secondaryTextWeight: Font.Weight = .regular // Figma: 400 (Regular)
        static let secondaryTextLineHeight: CGFloat = 21.0 // Figma: 21.0 (corrected from 24.0)

        static let fontFamily = "Inter" // Figma font family
    }

    // MARK: - Color Constants (From Figma)
    struct Colors {
        // Main black color (Figma: #242424)
        static let mainBlack = (red: 0.****************, green: 0.****************, blue: 0.****************)

        // Secondary black color (Figma: #797979)
        static let secondaryBlack = (red: 0.****************, green: 0.****************, blue: 0.****************)

        // Primary color (Figma: #89C226)
        static let primary500 = (red: 0.****************, green: 0.****************, blue: 0.*****************)

        // Secondary color (Figma: #0292D9)
        static let secondary500 = (red: 0.00784313725490196, green: 0.5725490196078431, blue: 0.8509803921568627)

        // Button background color - EXACT Figma specs (Figma: #89C226)
        static let buttonBackgroundGreen = (red: 0.****************, green: 0.****************, blue: 0.*****************)

        // White color
        static let white = (red: 1.0, green: 1.0, blue: 1.0)

        // Pixel Perfect color mappings from Figma
        static let titleMainColor = mainBlack // #242424
        static let titleAccentColor = secondary500 // #0292D9 (for "Phlex65" part)
        static let subtitleColor = secondaryBlack // #797979
        static let buttonBackgroundColor = buttonBackgroundGreen // #059669
        static let buttonTextColor = white // White
        static let secondaryTextColor = mainBlack // #242424
        static let backgroundWhite = white // White

        // Gradient Overlay Colors - EXACT Figma specifications
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        static let gradientColor = (red: 0.06666667014360428, green: 0.0470588244497776, blue: 0.11372549086809158) // #110C1D
    }
}
