//
//  RoleSelectionConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

struct RoleSelectionConstants {
    
    // MARK: - Text Constants
    struct Text {
        static let title = "Choose Your Role"
        static let subtitle = "Select how you'll be using Phlex65 to get personalized features and content."
        static let careReceiverButtonTitle = "Are you a Care Receiver?"
        static let caregiverButtonTitle = "Are you a Caregiver?"
        static let backButtonTitle = "Back"
    }

    // MARK: - Role Definitions
    struct Roles {
        struct Caregiver {
            static let title = "Caregiver"
            static let buttonTitle = "Are you a Caregiver?"
        }

        struct CareReceiver {
            static let title = "Care Receiver"
            static let buttonTitle = "Are you a Care Receiver?"
        }
    }
    
    // MARK: - Layout Constants (EXACT Figma JSON Specifications)
    struct Layout {
        // Screen layout (EXACT Figma: width: 375px, height: 812px)
        static let screenWidth: CGFloat = 375.0
        static let screenHeight: CGFloat = 812.0

        // Content container (EXACT Figma: width: 343px)
        static let contentWidth: CGFloat = 343.0
        static let screenPadding: CGFloat = 16.0 // (375 - 343) / 2 = 16px

        // Title and Description Container (EXACT Figma: gap: 4px)
        static let titleToSubtitleSpacing: CGFloat = 4.0 // EXACT: "gap": "4px"

        // Onboarding Information Container (EXACT Figma: gap: 8px)
        static let informationContainerGap: CGFloat = 8.0 // EXACT: "gap": "8px"

        // Button container (EXACT Figma: width: 375px, padding: 0px 16px, gap: 20px)
        static let buttonContainerWidth: CGFloat = 375.0
        static let buttonContainerPadding: CGFloat = 16.0 // EXACT: "padding": "0px 16px"
        static let buttonSpacing: CGFloat = 20.0 // EXACT: "gap": "20px"

        // Button layout (EXACT Figma: height: 48px, padding: 12px 20px, border-radius: 50px)
        static let buttonHeight: CGFloat = 48.0 // EXACT: "height": "48px"
        static let buttonPaddingVertical: CGFloat = 12.0 // EXACT: "padding": "12px 20px"
        static let buttonPaddingHorizontal: CGFloat = 20.0 // EXACT: "padding": "12px 20px"
        static let buttonCornerRadius: CGFloat = 50.0 // EXACT: "border-radius": "50px"
        static let buttonGap: CGFloat = 8.0 // EXACT: "gap": "8px"
    }
    
    // MARK: - Typography (EXACT Figma JSON Specifications)
    struct Typography {
        // Title (EXACT Figma: "Plus Jakarta Sans", 30px, 600 weight, 36px line-height)
        static let titleFontName = "PlusJakartaSans-SemiBold" // EXACT: "font-family": "\"Plus Jakarta Sans\""
        static let titleSize: CGFloat = 30.0 // EXACT: "font-size": "30px"
        static let titleWeight: Font.Weight = .semibold // EXACT: "font-weight": "600"
        static let titleLineHeight: CGFloat = 36.0 // EXACT: "line-height": "36px /* 120% */"
        static let titleColor = Color(hex: "#FFFFFF") // EXACT: "color": "var(--Greyscale-0, #FFF)"

        // Subtitle (EXACT Figma: Inter, 16px, 400 weight, 24px line-height)
        static let subtitleFontName = "Inter-Regular" // EXACT: "font-family": "Inter"
        static let subtitleSize: CGFloat = 16.0 // EXACT: "font-size": "16px"
        static let subtitleWeight: Font.Weight = .regular // EXACT: "font-weight": "400"
        static let subtitleLineHeight: CGFloat = 24.0 // EXACT: "line-height": "24px /* 150% */"
        static let subtitleColor = Color(hex: "#FFFFFF") // EXACT: "color": "var(--Greyscale-0, #FFF)"

        // Button text (EXACT Figma: Inter, 16px, 500 weight, 24px line-height)
        static let buttonFontName = "Inter-Medium" // EXACT: "font-family": "Inter"
        static let buttonSize: CGFloat = 16.0 // EXACT: "font-size": "16px"
        static let buttonWeight: Font.Weight = .medium // EXACT: "font-weight": "500"
        static let buttonLineHeight: CGFloat = 24.0 // EXACT: "line-height": "24px /* 150% */"
    }
    
    // MARK: - Colors (EXACT Figma JSON Specifications)
    struct Colors {
        // Screen background (EXACT Figma: "background": "var(--Greyscale-0, #FFF)")
        static let screenBackground = Color(hex: "#FFFFFF") // EXACT: "var(--Greyscale-0, #FFF)"

        // Care Receiver Button (EXACT Figma: Green button)
        static let careReceiverButtonBackground = Color(hex: "#89C226") // EXACT: "background": "var(--Primary-Primary-500, #89C226)"
        static let careReceiverButtonText = Color(hex: "#FFFFFF") // EXACT: "color": "var(--White, #FFF)"
        static let careReceiverButtonShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.05) // EXACT: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"

        // Caregiver Button (EXACT Figma: White button with border)
        static let caregiverButtonBackground = Color(hex: "#FFFFFF") // EXACT: "background": "var(--White, #FFF)"
        static let caregiverButtonText = Color(hex: "#344054") // EXACT: "color": "var(--Grayscale-Gray-700, #344054)"
        static let caregiverButtonBorder = Color(hex: "#E7ECF2") // EXACT: "border": "1px solid #E7ECF2"
        static let caregiverButtonShadow = Color(red: 16/255, green: 24/255, blue: 40/255).opacity(0.05) // EXACT: "box-shadow": "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"

        // Gradient Overlay Colors (EXACT Figma: linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%))
        static let gradientColor = OnboardingConstants.Colors.gradientColor
    }
    
    // MARK: - Images (EXACT Figma JSON Specifications)
    struct Images {
        // Background image (EXACT Figma JSON: width: 826px, height: 558px, aspect-ratio: 413/279)
        static let backgroundImage = "OnboardingRoleSelection" // EXACT: OnboardingRoleSelection.pdf asset
        static let aspectRatio: CGFloat = 413.0 / 279.0 // EXACT: "aspect-ratio": "413/279" = 1.****************
    }
    
    // MARK: - Animation Constants (Reusing OnboardingConstants)
    struct Animation {
        static let transitionDuration = OnboardingConstants.Animation.transitionDuration
        static let cardSelectionDuration: Double = 0.2
    }
}
