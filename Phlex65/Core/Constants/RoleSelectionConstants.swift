//
//  RoleSelectionConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

struct RoleSelectionConstants {
    
    // MARK: - Text Constants
    struct Text {
        static let title = "Choose Your Role"
        static let subtitle = "Lorem ipsum dolor sit amet, consectetur adipi scing elit. Lectus sed"
        static let careReceiverButtonTitle = "Are you a Care Receiver?"
        static let caregiverButtonTitle = "Are you a Caregiver?"
        static let backButtonTitle = "Back"
    }

    // MARK: - Role Definitions
    struct Roles {
        struct Caregiver {
            static let title = "Caregiver"
            static let buttonTitle = "Are you a Caregiver?"
        }

        struct CareReceiver {
            static let title = "Care Receiver"
            static let buttonTitle = "Are you a Care Receiver?"
        }
    }
    
    // MARK: - Layout Constants (Figma Design Specifications)
    struct Layout {
        // Screen layout
        static let screenPadding: CGFloat = OnboardingConstants.Layout.screenPadding
        static let contentBottomPadding: CGFloat = 60.0

        // Content spacing
        static let titleToSubtitleSpacing: CGFloat = 16.0
        static let subtitleToButtonsSpacing: CGFloat = 40.0
        static let buttonSpacing: CGFloat = 16.0
        static let buttonsToProgressSpacing: CGFloat = 32.0

        // Button layout - Figma design specifications
        static let buttonHeight: CGFloat = 56.0
        static let buttonCornerRadius: CGFloat = 28.0

        // Progress indicator
        static let progressBarHeight: CGFloat = 4.0
        static let progressBarWidth: CGFloat = 80.0
        static let progressBarCornerRadius: CGFloat = 2.0
    }
    
    // MARK: - Typography (Reusing OnboardingConstants)
    struct Typography {
        // Main title
        static let titleFontName = "PlusJakartaSans-SemiBold"
        static let titleSize: CGFloat = 30.0
        static let titleWeight: Font.Weight = .semibold
        static let titleColor = OnboardingConstants.Colors.primaryText

        // Subtitle
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 16.0
        static let subtitleWeight: Font.Weight = .regular
        static let subtitleColor = OnboardingConstants.Colors.secondaryText

        // Button text
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0
        static let buttonWeight: Font.Weight = .medium
    }
    
    // MARK: - Colors (Reusing OnboardingConstants)
    struct Colors {
        // Background colors
        static let imageOverlay = OnboardingConstants.Colors.imageOverlay
        static let contentBackground = OnboardingConstants.Colors.contentBackground

        // Text colors
        static let primaryText = OnboardingConstants.Colors.primaryText
        static let secondaryText = OnboardingConstants.Colors.secondaryText

        // Button colors - matching design
        static let careReceiverButtonBackground = Color(hex: "8BC34A") // Green button
        static let careReceiverButtonText = Color.white
        static let caregiverButtonBackground = Color.white
        static let caregiverButtonText = Color.black

        // Progress indicator
        static let progressBarBackground = Color.white

        // Gradient Overlay Colors - EXACT Figma specifications
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        static let gradientColor = OnboardingConstants.Colors.gradientColor
    }
    
    // MARK: - Images
    struct Images {
        static let backgroundImage = "OnboardingStep3Background" // Temporary - replace with OnboardingRoleSelection when SVG is fixed
    }
    
    // MARK: - Animation Constants (Reusing OnboardingConstants)
    struct Animation {
        static let transitionDuration = OnboardingConstants.Animation.transitionDuration
        static let cardSelectionDuration: Double = 0.2
    }
}
