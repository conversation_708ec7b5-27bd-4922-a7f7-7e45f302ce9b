//
//  APIConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - API Constants
struct APIConstants {
    
    // MARK: - Base URLs
    static let developmentBaseURL = "https://dev-api.phlex65.com"
    static let stagingBaseURL = "https://staging-api.phlex65.com"
    static let productionBaseURL = "https://api.phlex65.com"
    
    // MARK: - Socket URLs
    static let developmentSocketURL = "wss://dev-socket.phlex65.com"
    static let stagingSocketURL = "wss://staging-socket.phlex65.com"
    static let productionSocketURL = "wss://socket.phlex65.com"
    
    // MARK: - API Endpoints
    static let loginEndpoint = "/auth/login"
    static let registerEndpoint = "/auth/register"
    static let forgotPasswordEndpoint = "/auth/forgot-password"
    static let resetPasswordEndpoint = "/auth/reset-password"
    static let refreshTokenEndpoint = "/auth/refresh-token"
    static let logoutEndpoint = "/auth/logout"
    static let socialLoginEndpoint = "/auth/social-login"
    
    // MARK: - User Endpoints
    static let userProfileEndpoint = "/user/profile"
    static let userProfileImageEndpoint = "/user/profile/image"
    static let changePasswordEndpoint = "/user/change-password"
    static let deleteAccountEndpoint = "/user/delete-account"
    
    // MARK: - HTTP Headers
    static let contentTypeHeader = "Content-Type"
    static let acceptHeader = "Accept"
    static let authorizationHeader = "Authorization"
    static let userAgentHeader = "User-Agent"
    
    // MARK: - Content Types
    static let applicationJSONValue = "application/json"
    static let multipartFormDataValue = "multipart/form-data"
    
    // MARK: - Authorization
    static let bearerPrefix = "Bearer "
    
    // MARK: - Request Body Keys
    static let emailKey = "email"
    static let passwordKey = "password"
    static let firstNameKey = "firstName"
    static let lastNameKey = "lastName"
    static let phoneNumberKey = "phoneNumber"
    static let dateOfBirthKey = "dateOfBirth"
    static let genderKey = "gender"
    static let tokenKey = "token"
    static let newPasswordKey = "newPassword"
    static let oldPasswordKey = "oldPassword"
    static let refreshTokenKey = "refreshToken"
    static let providerKey = "provider"
}
