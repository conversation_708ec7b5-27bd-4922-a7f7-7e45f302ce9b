//
//  AppConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - App Constants
struct AppConstants {

    // MARK: - App Information
    static let appName = "Phlex65"
    static let appDisplayName = "Phlex65 Caregiver"
    static let appDescription = "Professional caregiver services platform"
    static let appVersion = "1.0.0"
    static let minimumIOSVersion = "15.0"

    // MARK: - Bundle Information
    static let defaultBundleIdentifier = "com.phlex65.caregiver.Phlex65"
    static let defaultAppVersion = "1.0"
    static let defaultBuildNumber = "1"

    // MARK: - Bundle Keys
    static let shortVersionStringBundleKey = "CFBundleShortVersionString"
    static let versionBundleKey = "CFBundleVersion"

    // MARK: - UserDefaults Keys
    static let selectedLanguageKey = "selectedLanguage"
    static let notificationsEnabledKey = "notificationsEnabled"
    static let biometricAuthEnabledKey = "biometricAuthEnabled"

    // MARK: - Keychain Keys
    static let accessTokenKeychainKey = "access_token"
    static let refreshTokenKeychainKey = "refresh_token"
    static let userCredentialsKeychainKey = "user_credentials"

    // MARK: - Notification Names
    static let userDidLoginNotification = "UserDidLoginNotification"
    static let userDidLogoutNotification = "UserDidLogoutNotification"
    static let networkStatusChangedNotification = "NetworkStatusChangedNotification"

    // MARK: - Animation Durations
    static let shortAnimationDuration: Double = 0.2
    static let mediumAnimationDuration: Double = 0.3
    static let longAnimationDuration: Double = 0.5

    // MARK: - UI Constants
    static let defaultCornerRadius: CGFloat = 8.0
    static let defaultPadding: CGFloat = 16.0
    static let defaultButtonHeight: CGFloat = 44.0
    static let defaultTextFieldHeight: CGFloat = 44.0

    // MARK: - Separators
    static let colonSeparator = ":"
    static let commaSeparator = ","
    static let spaceSeparator = " "

    // MARK: - System Image Names
    static let houseImage = "house"
    static let magnifyingGlassImage = "magnifyingglass"
    static let calendarImage = "calendar"
    static let personImage = "person"
    static let exclamationTriangleFillImage = "exclamation.triangle.fill"
    static let exclamationCircleFillImage = "exclamation.circle.fill"
    static let trayImage = "tray"
}
