//
//  OnboardingConstants.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import SwiftUI

struct OnboardingConstants {
    
    // MARK: - Step 1 Constants
    struct Step1 {
        struct Text {
            static let title = "Effortless Appointment Booking"
            static let subtitle = "Connecting with Care Receivers has never been simpler."
        }
        
        struct Images {
            static let backgroundImage = "OnboardingStep1Background"
            // ✅ FIXED: Dynamic aspect ratio for proper image containment (like welcome screen)
            static let aspectRatio: CGFloat = 375.0 / 811.0 // ≈ 0.46
        }
    }
    
    // MARK: - Step 2 Constants
    struct Step2 {
        struct Text {
            static let title = "Stay in Control of Your Jobs"
            static let subtitle = "Track your tasks, manage your schedule, and stay updated—all in one place."
        }
        
        struct Images {
            static let backgroundImage = "OnboardingStep2Background"
            // ✅ FIXED: Dynamic aspect ratio for proper image containment (like welcome screen)
            static let aspectRatio: CGFloat = 381.0 / 280.0 // = 1.36
        }
    }
    
    // MARK: - Step 3 Constants
    struct Step3 {
        struct Text {
            static let title = "Make a Meaningful Impact"
            static let subtitle = "Provide compassionate care and support while enjoying a seamless work experience tailored for caregivers."
        }
        
        struct Images {
            static let backgroundImage = "OnboardingStep3Background"
            // ✅ FIXED: Dynamic aspect ratio for proper image containment (like welcome screen)
            static let aspectRatio: CGFloat = 857.0 / 572.0 // = 1.49
        }
    }
    
    // MARK: - Layout Constants (EXACT Figma Design Specifications)
    struct Layout {
        // Screen layout - Figma: 375x812 design
        static let screenPadding: CGFloat = 16.0 // padding: 0px 16px
        static let contentBottomPadding: CGFloat = 20.0 // ✅ FIXED: Reduced from 34px to 20px to prevent overflow

        // Content container - Figma: width: 343px (375 - 32px padding), gap: 24px
        static let contentContainerWidth: CGFloat = 343.0
        static let contentContainerGap: CGFloat = 16.0 // ✅ FIXED: Reduced from 24px to 16px to prevent overflow

        // Title and Description Container - Figma: gap: 4px
        static let titleDescriptionGap: CGFloat = 4.0

        // Onboarding Information Container - Figma: gap: 8px
        static let onboardingInfoGap: CGFloat = 6.0 // ✅ FIXED: Reduced from 8px to 6px to prevent overflow

        // Button layout - Figma: height: 48px, padding: 12px 20px, border-radius: 50px
        static let buttonHeight: CGFloat = 48.0
        static let buttonCornerRadius: CGFloat = 50.0 // border-radius: 50px
        static let buttonHorizontalPadding: CGFloat = 20.0
        static let buttonVerticalPadding: CGFloat = 12.0
        static let buttonGap: CGFloat = 8.0

        // Content spacing - Figma specifications
        static let titleToSubtitleSpacing: CGFloat = 4.0 // gap: 4px in Title and Description Container
        static let subtitleToProgressSpacing: CGFloat = 24.0 // gap: 24px in Content Container
        static let progressToButtonSpacing: CGFloat = 24.0
        static let buttonToSkipSpacing: CGFloat = 12.0 // gap: 12px in Bottom Action

        // Progress indicator - Figma: width: 48px, height: 4px, border-radius: 100px, gap: 8px
        static let progressBarHeight: CGFloat = 4.0
        static let progressBarWidth: CGFloat = 48.0
        static let progressBarSpacing: CGFloat = 8.0
        static let progressBarCornerRadius: CGFloat = 100.0 // border-radius: 100px
        static let progressContainerWidth: CGFloat = 160.0 // width: 160px (48*3 + 8*2)

        // Bottom Action Container - Figma: gap: 12px
        static let bottomActionGap: CGFloat = 12.0

        // Content overlay
        static let overlayCornerRadius: CGFloat = 0.0 // No corner radius for full overlay
        static let overlayBottomHeight: CGFloat = 400.0 // Height of bottom content area
    }
    
    // MARK: - Typography (EXACT Figma Design Specifications)
    struct Typography {
        // Title - Figma: Plus Jakarta Sans 30px/600 weight, line-height: 36px (120%)
        // Using system font as fallback for Plus Jakarta Sans
        static let titleFontName = "PlusJakartaSans-SemiBold"
        static let titleSize: CGFloat = 30.0
        static let titleWeight: Font.Weight = .semibold // 600
        static let titleLineHeight: CGFloat = 36.0 // 120%
        static let titleColor = Color.white

        // Subtitle - Figma: Inter 16px/400 weight, line-height: 24px (150%)
        // Using system font as fallback for Inter
        static let subtitleFontName = "Inter-Regular"
        static let subtitleSize: CGFloat = 16.0
        static let subtitleWeight: Font.Weight = .regular // 400
        static let subtitleLineHeight: CGFloat = 24.0 // 150%
        static let subtitleColor = Color.white

        // Button text - Figma: Inter 16px/500 weight, line-height: 24px (150%)
        static let buttonFontName = "Inter-Medium"
        static let buttonSize: CGFloat = 16.0
        static let buttonWeight: Font.Weight = .medium // 500
        static let buttonLineHeight: CGFloat = 24.0 // 150%
        static let buttonColor = Color.white

        // Skip button text - Figma: Inter 16px/400 weight, line-height: 24px (150%)
        static let skipFontName = "Inter-Regular"
        static let skipSize: CGFloat = 16.0
        static let skipWeight: Font.Weight = .regular // 400
        static let skipLineHeight: CGFloat = 24.0 // 150%
        static let skipColor = Color.white
    }
    
    // MARK: - Colors (EXACT Figma Design Specifications)
    struct Colors {
        // Text colors - Figma: var(--Greyscale-0, #FFF)
        static let primaryText = Color.white // #FFF
        static let secondaryText = Color.white // #FFF
        static let skipButtonText = Color.white // #FFF

        // Button colors - Figma: var(--Primary-Primary-500, #89C226)
        static let primaryButtonBackground = Color(hex: "89C226") // #89C226
        static let primaryButtonText = Color.white // #FFF

        // Progress colors - Figma: Active: #89C226, Inactive: #F4F5F4
        static let progressActive = Color(hex: "89C226") // var(--Primary-Primary-500, #89C226)
        static let progressInactive = Color(hex: "F4F5F4") // var(--Greyscale-50, #F4F5F4)

        // Background colors
        static let imageOverlay = Color.clear // No overlay, using gradient instead
        static let contentBackground = Color.clear

        // Legacy support
        static let primary500 = (red: 0.5372549295425415, green: 0.7607843279838562, blue: 0.14901961386203766)
        static let white = (red: 1.0, green: 1.0, blue: 1.0)

        // Gradient Overlay Colors - EXACT Figma specifications
        // linear-gradient(180deg, rgba(17, 12, 29, 0.10) 0%, rgba(17, 12, 29, 0.00) 32.5%, #110C1D 66.27%)
        static let gradientColor = (red: 0.06666667014360428, green: 0.0470588244497776, blue: 0.11372549086809158) // #110C1D
    }
    
    // MARK: - Navigation Constants
    struct Navigation {
        static let skipButtonTitle = "Skip"
        static let backButtonTitle = "Back"
        static let nextButtonTitle = "Next"
        static let totalSteps = 3
    }
    
    // MARK: - Animation Constants
    struct Animation {
        static let transitionDuration: Double = 0.3
        static let cardAnimationDelay: Double = 0.1
        static let progressAnimationDuration: Double = 0.5
    }
}
