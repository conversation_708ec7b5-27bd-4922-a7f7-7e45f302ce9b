//
//  AppCoordinatorView.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Coordinator View
struct AppCoordinatorView: View {
    @EnvironmentObject var appCoordinator: AppCoordinator

    var body: some View {
        Group {
            switch appCoordinator.currentFlow {
            case .onboarding:
                OnboardingCoordinatorView()
                    .transition(.opacity)

            case .authentication:
                AuthenticationCoordinatorView()
                    .transition(.slide)

            case .main:
                MainCoordinatorView()
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appCoordinator.currentFlow)
    }
}

// MARK: - Preview
#Preview {
    AppCoordinatorView()
        .environmentObject(AppCoordinator())
}
