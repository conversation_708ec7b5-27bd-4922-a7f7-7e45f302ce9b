//
//  AppCoordinator.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Coordinator
@MainActor
class AppCoordinator: ObservableObject {

    // MARK: - Published Properties
    @Published var currentFlow: AppFlow = .onboarding

    // MARK: - Dependencies
    private let authService = AuthenticationService.shared

    // MARK: - Initialization
    init() {
        setupBindings()
        determineInitialFlow()
    }

    // MARK: - Public Methods
    func navigateToAuthentication() {
        currentFlow = .authentication
    }

    func navigateToMain() {
        currentFlow = .main
    }

    func navigateToOnboarding() {
        currentFlow = .onboarding
    }

    func handleAuthenticationSuccess() {
        currentFlow = .main
    }

    func handleLogout() {
        currentFlow = .authentication
    }

    // MARK: - Private Methods
    private func setupBindings() {
        // Observe authentication state changes
        authService.$isAuthenticated
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isAuthenticated in
                self?.handleAuthenticationStateChange(isAuthenticated: isAuthenticated)
            }
            .store(in: &cancellables)
    }

    private func determineInitialFlow() {
        // Always show Welcome screen first
        print("🚀 AppCoordinator: Determining initial flow - showing Welcome screen")
        currentFlow = .onboarding
    }



    private func handleAuthenticationStateChange(isAuthenticated: Bool) {
        // Only handle auth changes after initial flow is set
        // Don't override the Welcome screen on app launch
        if currentFlow != .onboarding {
            if isAuthenticated {
                currentFlow = .main
            } else {
                currentFlow = .authentication
            }
        }
    }

    // MARK: - Combine
    private var cancellables = Set<AnyCancellable>()
}

// MARK: - Combine Import
import Combine
