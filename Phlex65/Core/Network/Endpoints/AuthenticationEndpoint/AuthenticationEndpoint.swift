//
//  AuthenticationEndpoint.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Authentication Endpoint
enum AuthenticationEndpoint {
    case login(email: String, password: String)
    case register(email: String, password: String, firstName: String, lastName: String)
    case forgotPassword(email: String)
    case verifyCode(email: String, code: String)
    case resendCode(email: String)
    case resetPassword(token: String, newPassword: String)
    case refreshToken(refreshToken: String)
    case logout
    case socialLogin(provider: String, token: String)
}

// MARK: - APIEndpoint Conformance
extension AuthenticationEndpoint: APIEndpoint {
    var path: String {
        switch self {
        case .login:
            return APIConstants.loginEndpoint
        case .register:
            return APIConstants.registerEndpoint
        case .forgotPassword:
            return APIConstants.forgotPasswordEndpoint
        case .verifyCode:
            return "/auth/verify-code"
        case .resendCode:
            return "/auth/resend-code"
        case .resetPassword:
            return APIConstants.resetPasswordEndpoint
        case .refreshToken:
            return APIConstants.refreshTokenEndpoint
        case .logout:
            return APIConstants.logoutEndpoint
        case .socialLogin:
            return APIConstants.socialLoginEndpoint
        }
    }

    var method: HTTPMethod {
        switch self {
        case .login, .register, .forgotPassword, .verifyCode, .resendCode, .resetPassword, .refreshToken, .logout, .socialLogin:
            return .POST
        }
    }

    var headers: [String: String]? {
        return nil
    }

    var body: [String: Any]? {
        switch self {
        case .login(let email, let password):
            return [
                APIConstants.emailKey: email,
                APIConstants.passwordKey: password
            ]
        case .register(let email, let password, let firstName, let lastName):
            return [
                APIConstants.emailKey: email,
                APIConstants.passwordKey: password,
                APIConstants.firstNameKey: firstName,
                APIConstants.lastNameKey: lastName
            ]
        case .forgotPassword(let email):
            return [APIConstants.emailKey: email]
        case .verifyCode(let email, let code):
            return [
                APIConstants.emailKey: email,
                "code": code
            ]
        case .resendCode(let email):
            return [APIConstants.emailKey: email]
        case .resetPassword(let token, let newPassword):
            return [
                APIConstants.tokenKey: token,
                APIConstants.newPasswordKey: newPassword
            ]
        case .refreshToken(let refreshToken):
            return [APIConstants.refreshTokenKey: refreshToken]
        case .socialLogin(let provider, let token):
            return [
                APIConstants.providerKey: provider,
                APIConstants.tokenKey: token
            ]
        case .logout:
            return nil
        }
    }
}
