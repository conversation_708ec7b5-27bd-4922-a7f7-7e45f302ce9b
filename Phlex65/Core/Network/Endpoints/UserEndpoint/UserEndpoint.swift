//
//  UserEndpoint.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - User Endpoint
enum UserEndpoint {
    case getProfile
    case updateProfile(firstName: String, lastName: String, phoneNumber: String?, dateOfBirth: Date?, gender: Gender?)
    case uploadProfileImage(imageData: Data)
    case changePassword(oldPassword: String, newPassword: String)
    case deleteAccount
}

// MARK: - APIEndpoint Conformance
extension UserEndpoint: APIEndpoint {
    var path: String {
        switch self {
        case .getProfile, .updateProfile:
            return APIConstants.userProfileEndpoint
        case .uploadProfileImage:
            return APIConstants.userProfileImageEndpoint
        case .changePassword:
            return APIConstants.changePasswordEndpoint
        case .deleteAccount:
            return APIConstants.deleteAccountEndpoint
        }
    }

    var method: HTTPMethod {
        switch self {
        case .getProfile:
            return .GET
        case .updateProfile, .uploadProfileImage, .changePassword:
            return .PUT
        case .deleteAccount:
            return .DELETE
        }
    }

    var headers: [String: String]? {
        switch self {
        case .uploadProfileImage:
            return [APIConstants.contentTypeHeader: APIConstants.multipartFormDataValue]
        default:
            return nil
        }
    }

    var body: [String: Any]? {
        switch self {
        case .updateProfile(let firstName, let lastName, let phoneNumber, let dateOfBirth, let gender):
            var body: [String: Any] = [
                APIConstants.firstNameKey: firstName,
                APIConstants.lastNameKey: lastName
            ]

            if let phoneNumber = phoneNumber {
                body["phoneNumber"] = phoneNumber
            }

            if let dateOfBirth = dateOfBirth {
                let formatter = ISO8601DateFormatter()
                body["dateOfBirth"] = formatter.string(from: dateOfBirth)
            }

            if let gender = gender {
                body["gender"] = gender.rawValue
            }

            return body

        case .changePassword(let oldPassword, let newPassword):
            return [
                APIConstants.oldPasswordKey: oldPassword,
                APIConstants.newPasswordKey: newPassword
            ]

        case .getProfile, .uploadProfileImage, .deleteAccount:
            return nil
        }
    }
}
