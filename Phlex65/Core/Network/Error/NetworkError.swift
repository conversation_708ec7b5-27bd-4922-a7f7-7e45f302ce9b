//
//  NetworkError.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Network Error
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case invalidRequest
    case invalidResponse
    case noInternetConnection
    case timeout
    case unauthorized
    case clientError(Int)
    case serverError(Int)
    case decodingError
    case networkError(URLError)
    case unknown
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return invalidURLErrorMessage
        case .invalidResponse:
            return invalidResponseErrorMessage
        case .noInternetConnection:
            return noInternetConnectionErrorMessage
        case .timeout:
            return timeoutErrorMessage
        case .unauthorized:
            return unauthorizedErrorMessage
        case .clientError(let code):
            return "Client error (\(code))"
        case .serverError(let code):
            return "Server error (\(code))"
        case .decodingError:
            return decodingErrorMessage
        case .networkError(let urlError):
            return urlError.localizedDescription
        case .unknown:
            return unknownErrorMessage
        case .invalidRequest:
            return "Invalid request"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .noInternetConnection:
            return checkInternetConnectionMessage
        case .timeout:
            return tryAgainLaterMessage
        case .unauthorized:
            return pleaseLoginAgainMessage
        case .serverError:
            return tryAgainLaterMessage
        default:
            return contactSupportMessage
        }
    }
}
