//
//  NetworkManager.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import Alamofire

// MARK: - Network Manager
@MainActor
class NetworkManager: ObservableObject {

    // MARK: - Singleton
    static let shared = NetworkManager()

    // MARK: - Properties
    private let session: Session
    private let configuration: AppConfiguration

    // MARK: - Initialization
    private init() {
        // Configure Alamofire session
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60

        // Create custom session with interceptors
        self.session = Session(
            configuration: configuration,
            interceptor: NetworkInterceptor()
        )
        self.configuration = AppConfiguration.shared
    }

    private var defaultHeaders: [String: String] {
        let headers = [
            APIConstants.contentTypeHeader: APIConstants.applicationJSONValue,
            APIConstants.acceptHeader: APIConstants.applicationJSONValue
        ]

        // Note: Token will be added when making requests to avoid actor isolation issues

        return headers
    }

    // MARK: - Configuration
    func configure() {
        // Any additional configuration can be done here
        print("🌐 NetworkManager configured with base URL: \(configuration.baseURL)")
    }

    // MARK: - Generic Request Method
    func request<T: Codable>(
        endpoint: APIEndpoint,
        responseType: T.Type
    ) async throws -> T {

        // Build URL
        let url = configuration.baseURL + endpoint.path

        // Prepare headers
        var headers = HTTPHeaders(defaultHeaders)
        if let endpointHeaders = endpoint.headers {
            for (key, value) in endpointHeaders {
                headers[key] = value
            }
        }

        // Add authorization header if token exists
        if let token = AuthenticationService.shared.accessToken {
            headers[APIConstants.authorizationHeader] = "\(APIConstants.bearerPrefix)\(token)"
        }

        // Convert HTTPMethod enum to Alamofire HTTPMethod
        let alamofireMethod = Alamofire.HTTPMethod(rawValue: endpoint.method.rawValue)

        // Prepare parameters
        var parameters: Parameters? = nil
        let encoding: ParameterEncoding = JSONEncoding.default

        if let body = endpoint.body {
            parameters = body
        }

        // Perform request using Alamofire
        return try await withCheckedThrowingContinuation { continuation in
            session.request(
                url,
                method: alamofireMethod,
                parameters: parameters,
                encoding: encoding,
                headers: headers
            )
            .validate(statusCode: 200..<300)
            .responseDecodable(of: T.self) { response in
                switch response.result {
                case .success(let value):
                    continuation.resume(returning: value)

                case .failure(let error):
                    let networkError = self.mapAlamofireError(error, response: response.response)
                    continuation.resume(throwing: networkError)
                }
            }
        }
    }

    // MARK: - Error Mapping
    private nonisolated func mapAlamofireError(_ error: AFError, response: HTTPURLResponse?) -> NetworkError {
        switch error {
        case .invalidURL:
            return .invalidURL

        case .responseValidationFailed(let reason):
            switch reason {
            case .unacceptableStatusCode(let code):
                switch code {
                case 401:
                    return .unauthorized
                case 400...499:
                    return .clientError(code)
                case 500...599:
                    return .serverError(code)
                default:
                    return .unknown
                }
            default:
                return .invalidResponse
            }

        case .responseSerializationFailed:
            return .decodingError

        case .sessionTaskFailed(let error):
            if let urlError = error as? URLError {
                switch urlError.code {
                case .notConnectedToInternet, .networkConnectionLost:
                    return .noInternetConnection
                case .timedOut:
                    return .timeout
                default:
                    return .networkError(urlError)
                }
            } else {
                return .unknown
            }

        default:
            return .unknown
        }
    }
}
