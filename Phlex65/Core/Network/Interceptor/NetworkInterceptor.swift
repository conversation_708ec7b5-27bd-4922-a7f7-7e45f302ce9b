//
//  NetworkInterceptor.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import UIKit
import Alamofire

// MARK: - Network Interceptor
final class NetworkInterceptor: RequestInterceptor {

    // MARK: - Request Adaptation
    func adapt(_ urlRequest: URLRequest, for session: Session, completion: @escaping (Result<URLRequest, Error>) -> Void) {
        var adaptedRequest = urlRequest

        // Add common headers
        adaptedRequest.setValue(APIConstants.applicationJSONValue, forHTTPHeaderField: APIConstants.contentTypeHeader)
        adaptedRequest.setValue(APIConstants.applicationJSONValue, forHTTPHeaderField: APIConstants.acceptHeader)

        // Add user agent
        let userAgent = "\(AppConstants.appName)/\(AppConstants.appVersion) iOS/\(UIDevice.current.systemVersion)"
        adaptedRequest.setValue(userAgent, forHTTPHeaderField: APIConstants.userAgentHeader)

        // Log request for debugging
        if AppConfiguration.shared.isLoggingEnabled {
            logRequest(adaptedRequest)
        }

        completion(.success(adaptedRequest))
    }

    // MARK: - Request Retry
    func retry(_ request: Request, for session: Session, dueTo error: Error, completion: @escaping (RetryResult) -> Void) {
        guard let response = request.task?.response as? HTTPURLResponse else {
            completion(.doNotRetry)
            return
        }

        // Retry logic for specific status codes
        switch response.statusCode {
        case 401:
            // Handle token refresh
            handleTokenRefresh(for: request, session: session, completion: completion)

        case 500...599:
            // Retry server errors with exponential backoff
            let retryCount = request.retryCount
            if retryCount < 3 {
                let delay = pow(2.0, Double(retryCount))
                completion(.retryWithDelay(delay))
            } else {
                completion(.doNotRetry)
            }

        default:
            completion(.doNotRetry)
        }
    }

    // MARK: - Token Refresh Handling
    private func handleTokenRefresh(for request: Request, session: Session, completion: @escaping (RetryResult) -> Void) {
        Task { @MainActor in
            do {
                // Attempt to refresh token
                if let refreshToken = AuthenticationService.shared.refreshToken {
                    let endpoint = AuthenticationEndpoint.refreshToken(refreshToken: refreshToken)
                    let response: AuthenticationResponse = try await NetworkManager.shared.request(
                        endpoint: endpoint,
                        responseType: AuthenticationResponse.self
                    )

                    // Save new tokens
                    await AuthenticationService.shared.handleSuccessfulAuthentication(response: response)

                    // Retry the original request
                    completion(.retry)
                } else {
                    // No refresh token available, logout user
                    await AuthenticationService.shared.logout()
                    completion(.doNotRetry)
                }
            } catch {
                // Token refresh failed, logout user
                await AuthenticationService.shared.logout()
                completion(.doNotRetry)
            }
        }
    }

    // MARK: - Request Logging
    private func logRequest(_ request: URLRequest) {
        print("🌐 Network Request:")
        print("   URL: \(request.url?.absoluteString ?? "Unknown")")
        print("   Method: \(request.httpMethod ?? "Unknown")")

        if let headers = request.allHTTPHeaderFields, !headers.isEmpty {
            print("   Headers:")
            for (key, value) in headers {
                // Don't log sensitive headers
                if key.lowercased().contains("authorization") {
                    print("     \(key): [REDACTED]")
                } else {
                    print("     \(key): \(value)")
                }
            }
        }

        if let body = request.httpBody,
           let bodyString = String(data: body, encoding: .utf8) {
            print("   Body: \(bodyString)")
        }
    }
}
