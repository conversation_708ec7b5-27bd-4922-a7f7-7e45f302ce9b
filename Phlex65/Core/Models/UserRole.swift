//
//  UserRole.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - User Role Model
struct UserRole: Identifiable, Equatable, CaseIterable {
    let id: String
    let title: String
    let description: String

    // MARK: - Static Role Definitions
    static let caregiver = UserRole(
        id: "caregiver",
        title: RoleSelectionConstants.Roles.Caregiver.title,
        description: "Provide care and support to those in need"
    )

    static let careReceiver = UserRole(
        id: "care_receiver",
        title: RoleSelectionConstants.Roles.CareReceiver.title,
        description: "Receive care and support services"
    )

    // MARK: - All Cases
    static var allCases: [UserRole] {
        return [caregiver, careReceiver]
    }
    
    // MARK: - Computed Properties
    var isCaregiver: Bool {
        return id == "caregiver"
    }

    var isCareReceiver: Bool {
        return id == "care_receiver"
    }
}

// MARK: - Role Selection State
enum RoleSelectionState: Equatable {
    case selecting
    case selected(UserRole)
    case completed(UserRole)
    
    var selectedRole: UserRole? {
        switch self {
        case .selected(let role), .completed(let role):
            return role
        case .selecting:
            return nil
        }
    }
    
    var isCompleted: Bool {
        switch self {
        case .completed:
            return true
        default:
            return false
        }
    }
}
