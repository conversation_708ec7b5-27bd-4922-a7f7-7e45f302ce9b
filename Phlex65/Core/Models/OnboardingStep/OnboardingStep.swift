//
//  OnboardingStep.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Onboarding Step Model
struct OnboardingStep: Identifiable, Equatable {
    let id: Int
    let title: String
    let subtitle: String
    let backgroundImageName: String
    
    // MARK: - Static Step Definitions
    static let step1 = OnboardingStep(
        id: 1,
        title: OnboardingConstants.Step1.Text.title,
        subtitle: OnboardingConstants.Step1.Text.subtitle,
        backgroundImageName: OnboardingConstants.Step1.Images.backgroundImage
    )
    
    static let step2 = OnboardingStep(
        id: 2,
        title: OnboardingConstants.Step2.Text.title,
        subtitle: OnboardingConstants.Step2.Text.subtitle,
        backgroundImageName: OnboardingConstants.Step2.Images.backgroundImage
    )
    
    static let step3 = OnboardingStep(
        id: 3,
        title: OnboardingConstants.Step3.Text.title,
        subtitle: OnboardingConstants.Step3.Text.subtitle,
        backgroundImageName: OnboardingConstants.Step3.Images.backgroundImage
    )
    
    // MARK: - All Steps Array
    static let allSteps: [OnboardingStep] = [step1, step2, step3]
    
    // MARK: - Computed Properties
    var isLastStep: Bool {
        return id == OnboardingConstants.Navigation.totalSteps
    }
    
    var progressValue: Double {
        return Double(id) / Double(OnboardingConstants.Navigation.totalSteps)
    }
}

// MARK: - Onboarding Flow State
enum OnboardingFlowState: Equatable {
    case welcome
    case step(Int)
    case roleSelection
    case completed

    var currentStepIndex: Int? {
        switch self {
        case .step(let index):
            return index
        default:
            return nil
        }
    }

    var isCompleted: Bool {
        switch self {
        case .completed:
            return true
        default:
            return false
        }
    }

    var isRoleSelection: Bool {
        switch self {
        case .roleSelection:
            return true
        default:
            return false
        }
    }
}
