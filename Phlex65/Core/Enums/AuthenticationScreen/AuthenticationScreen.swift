//
//  AuthenticationScreen.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Verification Context
enum VerificationContext: Equatable {
    case signup(email: String)           // After user signs up
    case forgotPassword(email: String)   // After forgot password request
    case unverifiedLogin(email: String)  // When unverified user tries to login
}

// MARK: - Authentication Screen
enum AuthenticationScreen: Equatable {
    case login
    case register
    case signupStep1
    case signupStep2(fullName: String, email: String, password: String)
    case forgotPassword
    case verifyCode(context: VerificationContext)
    case resetPassword(token: String)
}
