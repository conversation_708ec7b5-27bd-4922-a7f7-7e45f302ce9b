//
//  AppAnimation.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Animation
struct AppAnimation {
    static let fast = SwiftUI.Animation.easeInOut(duration: 0.2)
    static let medium = SwiftUI.Animation.easeInOut(duration: 0.3)
    static let slow = SwiftUI.Animation.easeInOut(duration: 0.5)
    static let spring = SwiftUI.Animation.spring(response: 0.5, dampingFraction: 0.8)
}
