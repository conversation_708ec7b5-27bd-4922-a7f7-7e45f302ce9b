//
//  AppShadow.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Shadow
struct AppShadow {
    static let small = (color: AppColors.shadow.opacity(0.1), radius: CGFloat(2), x: CGFloat(0), y: CGFloat(1))
    static let medium = (color: AppColors.shadow.opacity(0.15), radius: CGFloat(4), x: CGFloat(0), y: CGFloat(2))
    static let large = (color: AppColors.shadow.opacity(0.2), radius: CGFloat(8), x: CGFloat(0), y: CGFloat(4))
    static let extraLarge = (color: AppColors.shadow.opacity(0.25), radius: CGFloat(16), x: CGFloat(0), y: CGFloat(8))
}
