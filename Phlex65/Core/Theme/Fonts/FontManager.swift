//
//  FontManager.swift
//  Phlex65
//
//  Created by TKxel on 16/06/2025.
//

import SwiftUI
import UIKit

// MARK: - Font Manager
class FontManager {
    
    static let shared = FontManager()
    
    private init() {
        registerFonts()
    }
    
    // MARK: - Font Registration
    private func registerFonts() {
        let fontNames = [
            "Inter-Regular",
            "Inter-Medium", 
            "Inter-SemiBold",
            "Inter-Bold",
            "Inter-Light",
            "Inter-Thin",
            "Inter-ExtraLight",
            "Inter-ExtraBold",
            "Inter-Black"
        ]
        
        for fontName in fontNames {
            registerFont(fontName: fontName)
        }
    }
    
    private func registerFont(fontName: String) {
        guard let fontURL = Bundle.main.url(forResource: fontName, withExtension: "ttf"),
              let fontDataProvider = CGDataProvider(url: fontURL as CFURL),
              let font = CGFont(fontDataProvider) else {
            print("❌ Failed to load font: \(fontName)")
            return
        }
        
        var error: Unmanaged<CFError>?
        if !CTFontManagerRegisterGraphicsFont(font, &error) {
            print("❌ Failed to register font: \(fontName), error: \(String(describing: error))")
        } else {
            print("✅ Successfully registered font: \(fontName)")
        }
    }
    
    // MARK: - Font Helpers
    static func interFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        let fontName: String
        switch weight {
        case .thin:
            fontName = "Inter-Thin"
        case .ultraLight:
            fontName = "Inter-ExtraLight"
        case .light:
            fontName = "Inter-Light"
        case .regular:
            fontName = "Inter-Regular"
        case .medium:
            fontName = "Inter-Medium"
        case .semibold:
            fontName = "Inter-SemiBold"
        case .bold:
            fontName = "Inter-Bold"
        case .heavy:
            fontName = "Inter-ExtraBold"
        case .black:
            fontName = "Inter-Black"
        default:
            fontName = "Inter-Regular"
        }
        
        return .custom(fontName, size: size)
    }
}

// MARK: - Font Extension
extension Font {
    static func inter(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        return FontManager.interFont(size: size, weight: weight)
    }
}
