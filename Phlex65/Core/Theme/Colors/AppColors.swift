//
//  AppColors.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Colors
struct AppColors {

    // MARK: - Primary Colors
    static let primary = Color("AppPrimary")

    // MARK: - Brand Colors (Phlex65)
    static let phlexGreen = Color("PhlexGreen")
    static let phlexBlue = Color("PhlexBlue")
    static let primaryLight = Color("PrimaryLightColor")
    static let primaryDark = Color("PrimaryDarkColor")

    // MARK: - Secondary Colors
    static let secondary = Color("SecondaryColor")
    static let secondaryLight = Color("SecondaryLightColor")
    static let secondaryDark = Color("SecondaryDarkColor")

    // MARK: - Accent Colors
    static let accent = Color("AccentColor")
    static let accentLight = Color("AccentLightColor")
    static let accentDark = Color("AccentDarkColor")

    // MARK: - Background Colors
    static let background = Color("AppBackground")
    static let backgroundSecondary = Color("AppBackgroundSecondary")
    static let surface = Color("SurfaceColor")
    static let surfaceSecondary = Color("SurfaceSecondaryColor")

    // MARK: - Text Colors
    static let textPrimary = Color("AppTextPrimary")
    static let textSecondary = Color("AppTextSecondary")
    static let textTertiary = Color("TextTertiaryColor")
    static let textInverse = Color("AppTextInverse")

    // MARK: - Status Colors
    static let success = Color("SuccessColor")
    static let warning = Color("WarningColor")
    static let error = Color("AppError")
    static let info = Color("InfoColor")

    // MARK: - Border Colors
    static let border = Color("AppBorder")
    static let borderLight = Color("BorderLightColor")
    static let borderDark = Color("BorderDarkColor")

    // MARK: - Shadow Colors
    static let shadow = Color("AppShadow")
    static let shadowLight = Color("ShadowLightColor")

    // MARK: - System Colors (Fallbacks)
    static let systemBackground = Color(UIColor.systemBackground)
    static let systemSecondaryBackground = Color(UIColor.secondarySystemBackground)
    static let systemTertiaryBackground = Color(UIColor.tertiarySystemBackground)
    static let label = Color(UIColor.label)
    static let secondaryLabel = Color(UIColor.secondaryLabel)
    static let tertiaryLabel = Color(UIColor.tertiaryLabel)
}
