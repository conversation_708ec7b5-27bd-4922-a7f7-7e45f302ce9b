//
//  AppTheme.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - App Theme
struct AppTheme {

    // MARK: - Type Aliases for easier access
    typealias Colors = AppColors
    typealias Typography = AppTypography
    typealias Spacing = AppSpacing
    typealias CornerRadius = AppCornerRadius
    typealias Shadow = AppShadow
    typealias Animation = AppAnimation

    // MARK: - Configuration
    static func configure() {
        // Configure global appearance
        configureNavigationBar()
        configureTabBar()
        configureButtons()
    }

    private static func configureNavigationBar() {
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(AppColors.background)
        appearance.titleTextAttributes = [
            .foregroundColor: UIColor(AppColors.textPrimary),
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold)
        ]
        appearance.largeTitleTextAttributes = [
            .foregroundColor: UIColor(AppColors.textPrimary),
            .font: UIFont.systemFont(ofSize: 34, weight: .bold)
        ]

        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
    }

    private static func configureTabBar() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor(AppColors.background)

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }

    private static func configureButtons() {
        UIButton.appearance().tintColor = UIColor(AppColors.primary)
    }
}
