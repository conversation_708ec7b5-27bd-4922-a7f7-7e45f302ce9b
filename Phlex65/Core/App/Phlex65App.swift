//
//  Phlex65App.swift
//  Phlex65
//
//  Created by TK-LPT-1176 on 27/05/2025.
//

import SwiftUI

@main
struct Phlex65App: App {

    // MARK: - Properties
    @StateObject private var appCoordinator = AppCoordinator()

    var body: some Scene {
        WindowGroup {
            AppCoordinatorView()
                .environmentObject(appCoordinator)
                .onAppear {
                    print("🚀 Phlex65App: App appeared, setting up...")
                    setupApp()
                }
        }
    }

    // MARK: - Private Methods
    private func setupApp() {
        // Configure app-level settings
        configureNetworking()
        configureAppearance()

        // Print configuration in debug mode
        AppConfiguration.shared.printConfiguration()
    }

    private func configureNetworking() {
        // Initialize network configuration
        NetworkManager.shared.configure()
    }

    private func configureAppearance() {
        // Configure global app appearance
        AppTheme.configure()

        // Initialize font manager to register custom fonts
        _ = FontManager.shared
    }
}
