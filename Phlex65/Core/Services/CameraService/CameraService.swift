//
//  CameraService.swift
//  Phlex65
//
//  Created by Augment Agent on 18/06/2025.
//

import SwiftUI
import UIKit
import AVFoundation
import Photos

// MARK: - Camera Service Protocol
protocol CameraServiceProtocol {
    func checkCameraPermission() async -> Bool
    func checkPhotoLibraryPermission() async -> Bool
    func requestCameraPermission() async -> Bool
    func requestPhotoLibraryPermission() async -> Bool
}

// MARK: - Camera Service Implementation
class CameraService: CameraServiceProtocol {
    
    // MARK: - Singleton
    static let shared = CameraService()
    private init() {}
    
    // MARK: - Camera Permission Methods
    func checkCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }
    
    func requestCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch status {
        case .authorized:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                AVCaptureDevice.requestAccess(for: .video) { granted in
                    continuation.resume(returning: granted)
                }
            }
        case .denied, .restricted:
            return false
        @unknown default:
            return false
        }
    }
    
    // MARK: - Photo Library Permission Methods
    func checkPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        return status == .authorized || status == .limited
    }
    
    func requestPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                    let granted = newStatus == .authorized || newStatus == .limited
                    continuation.resume(returning: granted)
                }
            }
        case .denied, .restricted:
            return false
        @unknown default:
            return false
        }
    }
}

// MARK: - Image Picker Coordinator
class ImagePickerCoordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    
    let onImagePicked: (UIImage) -> Void
    let onCancel: () -> Void
    
    init(onImagePicked: @escaping (UIImage) -> Void, onCancel: @escaping () -> Void) {
        self.onImagePicked = onImagePicked
        self.onCancel = onCancel
    }
    
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if let image = info[.originalImage] as? UIImage {
            onImagePicked(image)
        }
        picker.dismiss(animated: true)
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        onCancel()
        picker.dismiss(animated: true)
    }
}

// MARK: - Image Picker Representable
struct ImagePicker: UIViewControllerRepresentable {
    
    let sourceType: UIImagePickerController.SourceType
    let onImagePicked: (UIImage) -> Void
    let onCancel: () -> Void
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> ImagePickerCoordinator {
        ImagePickerCoordinator(onImagePicked: onImagePicked, onCancel: onCancel)
    }
}
