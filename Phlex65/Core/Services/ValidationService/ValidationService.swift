//
//  ValidationService.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Validation Result
enum ValidationResult {
    case valid
    case invalid(String)
    
    var isValid: Bool {
        switch self {
        case .valid:
            return true
        case .invalid:
            return false
        }
    }
    
    var errorMessage: String? {
        switch self {
        case .valid:
            return nil
        case .invalid(let message):
            return message
        }
    }
}

// MARK: - Validation Service
class ValidationService {
    
    // MARK: - Singleton
    static let shared = ValidationService()
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Email Validation
    func validateEmail(_ email: String) -> ValidationResult {
        // Check if empty
        guard !email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return .invalid(emptyEmailMessage)
        }
        
        // Check email format
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", LoginConstants.Validation.emailRegex)
        guard emailPredicate.evaluate(with: email) else {
            return .invalid(invalidEmailMessage)
        }
        
        return .valid
    }
    
    // MARK: - Password Validation
    func validatePassword(_ password: String) -> ValidationResult {
        // Check if empty
        guard !password.isEmpty else {
            return .invalid(emptyPasswordMessage)
        }
        
        // Check minimum length
        guard password.count >= LoginConstants.Validation.minPasswordLength else {
            return .invalid(invalidPasswordMessage)
        }
        
        // Check maximum length
        guard password.count <= LoginConstants.Validation.maxPasswordLength else {
            return .invalid("Password must be less than \(LoginConstants.Validation.maxPasswordLength) characters long.")
        }
        
        return .valid
    }
    
    // MARK: - Password Confirmation Validation
    func validatePasswordConfirmation(_ password: String, confirmation: String) -> ValidationResult {
        // First validate the password itself
        let passwordValidation = validatePassword(password)
        guard passwordValidation.isValid else {
            return passwordValidation
        }
        
        // Check if confirmation matches
        guard password == confirmation else {
            return .invalid(passwordMismatchMessage)
        }
        
        return .valid
    }
    
    // MARK: - Name Validation
    func validateName(_ name: String, fieldName: String = "Name") -> ValidationResult {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Check if empty
        guard !trimmedName.isEmpty else {
            return .invalid("\(fieldName) is required.")
        }
        
        // Check minimum length
        guard trimmedName.count >= 2 else {
            return .invalid("\(fieldName) must be at least 2 characters long.")
        }
        
        // Check maximum length
        guard trimmedName.count <= 50 else {
            return .invalid("\(fieldName) must be less than 50 characters long.")
        }
        
        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        let nameRegex = "^[a-zA-Z\\s\\-']+$"
        let namePredicate = NSPredicate(format: "SELF MATCHES %@", nameRegex)
        guard namePredicate.evaluate(with: trimmedName) else {
            return .invalid("\(fieldName) can only contain letters, spaces, hyphens, and apostrophes.")
        }
        
        return .valid
    }
    
    // MARK: - Full Name Validation
    func validateFullName(_ fullName: String) -> ValidationResult {
        let trimmedName = fullName.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if empty
        guard !trimmedName.isEmpty else {
            return .invalid("Full name is required.")
        }

        // Check minimum length
        guard trimmedName.count >= 2 else {
            return .invalid("Full name must be at least 2 characters long.")
        }

        // Check maximum length
        guard trimmedName.count <= 100 else {
            return .invalid("Full name must be less than 100 characters long.")
        }

        // Check for valid characters (letters, spaces, hyphens, apostrophes)
        let nameRegex = "^[a-zA-Z\\s\\-']+$"
        let namePredicate = NSPredicate(format: "SELF MATCHES %@", nameRegex)
        guard namePredicate.evaluate(with: trimmedName) else {
            return .invalid("Full name can only contain letters, spaces, hyphens, and apostrophes.")
        }

        // Check that it contains at least first and last name (at least one space)
        let nameComponents = trimmedName.components(separatedBy: " ").filter { !$0.isEmpty }
        guard nameComponents.count >= 2 else {
            return .invalid("Please enter both first and last name.")
        }

        return .valid
    }

    // MARK: - Phone Number Validation
    func validatePhoneNumber(_ phoneNumber: String) -> ValidationResult {
        let trimmedPhone = phoneNumber.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if empty (phone is usually optional)
        guard !trimmedPhone.isEmpty else {
            return .invalid("Phone number is required.")
        }

        // Remove all non-digit characters for validation
        let digitsOnly = trimmedPhone.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()

        // Check length (10-15 digits is standard for international numbers)
        guard digitsOnly.count >= 10 && digitsOnly.count <= 15 else {
            return .invalid("Please enter a valid phone number.")
        }

        return .valid
    }
    
    // MARK: - Login Form Validation
    func validateLoginForm(email: String, password: String) -> (isValid: Bool, emailError: String?, passwordError: String?) {
        let emailValidation = validateEmail(email)
        let passwordValidation = validatePassword(password)
        
        return (
            isValid: emailValidation.isValid && passwordValidation.isValid,
            emailError: emailValidation.errorMessage,
            passwordError: passwordValidation.errorMessage
        )
    }
    
    // MARK: - Registration Form Validation
    func validateRegistrationForm(
        email: String,
        password: String,
        confirmPassword: String,
        firstName: String,
        lastName: String
    ) -> (isValid: Bool, errors: [String: String]) {
        
        var errors: [String: String] = [:]
        
        // Validate email
        let emailValidation = validateEmail(email)
        if !emailValidation.isValid {
            errors["email"] = emailValidation.errorMessage
        }
        
        // Validate password
        let passwordValidation = validatePassword(password)
        if !passwordValidation.isValid {
            errors["password"] = passwordValidation.errorMessage
        }
        
        // Validate password confirmation
        let confirmPasswordValidation = validatePasswordConfirmation(password, confirmation: confirmPassword)
        if !confirmPasswordValidation.isValid {
            errors["confirmPassword"] = confirmPasswordValidation.errorMessage
        }
        
        // Validate first name
        let firstNameValidation = validateName(firstName, fieldName: "First name")
        if !firstNameValidation.isValid {
            errors["firstName"] = firstNameValidation.errorMessage
        }
        
        // Validate last name
        let lastNameValidation = validateName(lastName, fieldName: "Last name")
        if !lastNameValidation.isValid {
            errors["lastName"] = lastNameValidation.errorMessage
        }
        
        return (isValid: errors.isEmpty, errors: errors)
    }
}
