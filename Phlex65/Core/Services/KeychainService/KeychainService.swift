//
//  KeychainService.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation
import Security

// MARK: - Keychain Service
class KeychainService {

    // MARK: - Singleton
    static let shared = KeychainService()

    // MARK: - Properties
    private let service = Bundle.main.bundleIdentifier ?? AppConstants.defaultBundleIdentifier

    // MARK: - Initialization
    private init() {}

    // MARK: - Token Management
    func saveAccessToken(_ token: String) {
        save(token, forKey: AppConstants.accessTokenKeychainKey)
    }

    func getAccessToken() -> String? {
        return get(forKey: AppConstants.accessTokenKeychainKey)
    }

    func deleteAccessToken() {
        delete(forKey: AppConstants.accessTokenKeychainKey)
    }

    func saveRefreshToken(_ token: String) {
        save(token, forKey: AppConstants.refreshTokenKeychainKey)
    }

    func getRefreshToken() -> String? {
        return get(forKey: AppConstants.refreshTokenKeychainKey)
    }

    func deleteRefreshToken() {
        delete(forKey: AppConstants.refreshTokenKeychainKey)
    }

    // MARK: - User Credentials
    func saveUserCredentials(email: String, password: String) {
        let credentials = "\(email)\(AppConstants.colonSeparator)\(password)"
        save(credentials, forKey: AppConstants.userCredentialsKeychainKey)
    }

    func getUserCredentials() -> (email: String, password: String)? {
        guard let credentials = get(forKey: AppConstants.userCredentialsKeychainKey),
              let data = credentials.data(using: .utf8),
              let credentialsString = String(data: data, encoding: .utf8) else {
            return nil
        }

        let components = credentialsString.components(separatedBy: AppConstants.colonSeparator)
        guard components.count == 2 else { return nil }

        return (email: components[0], password: components[1])
    }

    func deleteUserCredentials() {
        delete(forKey: AppConstants.userCredentialsKeychainKey)
    }

    // MARK: - Generic Keychain Operations
    private func save(_ value: String, forKey key: String) {
        guard let data = value.data(using: .utf8) else { return }

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]

        // Delete existing item first
        SecItemDelete(query as CFDictionary)

        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        if status != errSecSuccess {
            print("❌ Keychain save failed for key: \(key), status: \(status)")
        }
    }

    private func get(forKey key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }

        return string
    }

    private func delete(forKey key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]

        let status = SecItemDelete(query as CFDictionary)
        if status != errSecSuccess && status != errSecItemNotFound {
            print("❌ Keychain delete failed for key: \(key), status: \(status)")
        }
    }
}
