//
//  AuthenticationService.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - Authentication Service
@MainActor
class AuthenticationService: ObservableObject {

    // MARK: - Singleton
    static let shared = AuthenticationService()

    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var requiresVerification = false
    @Published var unverifiedUserEmail: String?
    @Published var resetToken: String?

    // MARK: - Private Properties
    private let keychainService = KeychainService.shared
    private let networkManager = NetworkManager.shared

    // MARK: - Initialization
    private init() {
        checkAuthenticationStatus()
    }

    // MARK: - Public Properties
    var accessToken: String? {
        return keychainService.getAccessToken()
    }

    var refreshToken: String? {
        return keychainService.getRefreshToken()
    }

    // MARK: - Authentication Methods
    func login(email: String, password: String) async throws {
        let endpoint = AuthenticationEndpoint.login(email: email, password: password)
        let response: AuthenticationResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: AuthenticationResponse.self
        )

        await handleSuccessfulAuthentication(response: response)
    }

    func register(email: String, password: String, firstName: String, lastName: String) async throws {
        let endpoint = AuthenticationEndpoint.register(
            email: email,
            password: password,
            firstName: firstName,
            lastName: lastName
        )
        let response: AuthenticationResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: AuthenticationResponse.self
        )

        await handleSuccessfulAuthentication(response: response)
    }

    func logout() async {
        // Call logout endpoint if needed
        do {
            let endpoint = AuthenticationEndpoint.logout
            let _: EmptyResponse = try await networkManager.request(
                endpoint: endpoint,
                responseType: EmptyResponse.self
            )
        } catch {
            print("⚠️ Logout API call failed: \(error)")
            // Continue with local logout even if API fails
        }

        await handleLogout()
    }

    func forgotPassword(email: String) async throws {
        let endpoint = AuthenticationEndpoint.forgotPassword(email: email)
        let _: EmptyResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: EmptyResponse.self
        )
    }

    // MARK: - Password Reset Alias
    func sendPasswordResetLink(email: String) async throws {
        try await forgotPassword(email: email)
    }

    // MARK: - Code Verification
    func verifyCode(email: String, code: String) async throws {
        let endpoint = AuthenticationEndpoint.verifyCode(email: email, code: code)
        let response: VerificationResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: VerificationResponse.self
        )

        // Handle successful verification
        await handleSuccessfulVerification(response: response)
    }

    private func handleSuccessfulVerification(response: VerificationResponse) async {
        if response.isAuthenticationResponse {
            // Handle authentication verification (signup/unverified login)
            if let authResponse = response.toAuthenticationResponse() {
                await handleSuccessfulAuthentication(response: authResponse)

                // Clear verification state
                requiresVerification = false
                unverifiedUserEmail = nil

                print("✅ Email verification successful for user: \(authResponse.user.email)")
            }
        } else if response.isResetTokenResponse {
            // Handle password reset verification
            resetToken = response.resetToken
            print("✅ Password reset verification successful, token received")
        }
    }

    func resendVerificationCode(email: String) async throws {
        let endpoint = AuthenticationEndpoint.resendCode(email: email)
        let _: EmptyResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: EmptyResponse.self
        )
    }

    func resetPassword(token: String, newPassword: String) async throws {
        let endpoint = AuthenticationEndpoint.resetPassword(token: token, newPassword: newPassword)
        let _: EmptyResponse = try await networkManager.request(
            endpoint: endpoint,
            responseType: EmptyResponse.self
        )

        // Clear reset token after successful reset
        resetToken = nil

        print("✅ Password reset successful")
    }

    // MARK: - Public Methods
    func checkAuthenticationStatus() {
        if let _ = accessToken {
            isAuthenticated = true
            // TODO: Validate token and fetch user profile
        } else {
            isAuthenticated = false
            currentUser = nil
        }
    }

    func handleSuccessfulAuthentication(response: AuthenticationResponse) async {
        // Check if user is verified
        if !response.user.isEmailVerified {
            // User is not verified, require verification
            requiresVerification = true
            unverifiedUserEmail = response.user.email
            isAuthenticated = false

            print("⚠️ User email not verified: \(response.user.email)")
            return
        }

        // Save tokens
        keychainService.saveAccessToken(response.accessToken)
        keychainService.saveRefreshToken(response.refreshToken)

        // Update state
        currentUser = response.user
        isAuthenticated = true
        requiresVerification = false
        unverifiedUserEmail = nil

        print("✅ Authentication successful for user: \(response.user.email)")
    }

    private func handleLogout() async {
        // Clear tokens
        keychainService.deleteAccessToken()
        keychainService.deleteRefreshToken()
        keychainService.deleteUserCredentials()

        // Update state
        currentUser = nil
        isAuthenticated = false

        print("✅ User logged out successfully")
    }
}
