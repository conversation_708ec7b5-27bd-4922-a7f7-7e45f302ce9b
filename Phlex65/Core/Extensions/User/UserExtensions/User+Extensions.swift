//
//  User+Extensions.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import Foundation

// MARK: - User Extensions
extension User {
    
    // MARK: - Validation
    var isValidForRegistration: Bool {
        return !firstName.isEmpty &&
               !lastName.isEmpty &&
               !email.isEmpty &&
               email.contains("@")
    }
    
    // MARK: - Display Helpers
    var displayName: String {
        if !fullName.trimmingCharacters(in: .whitespaces).isEmpty {
            return fullName
        }
        return email
    }
    
    var profileImageInitials: String {
        return initials.isEmpty ? "?" : initials
    }
    
    // MARK: - Age Calculation
    var age: Int? {
        guard let dateOfBirth = dateOfBirth else { return nil }
        let calendar = Calendar.current
        let now = Date()
        let ageComponents = calendar.dateComponents([.year], from: dateOfBirth, to: now)
        return ageComponents.year
    }
    
    // MARK: - Verification Status
    var verificationStatus: String {
        switch (isEmailVerified, isPhoneVerified) {
        case (true, true):
            return "Fully Verified"
        case (true, false):
            return "Email Verified"
        case (false, true):
            return "Phone Verified"
        case (false, false):
            return "Not Verified"
        }
    }
}
