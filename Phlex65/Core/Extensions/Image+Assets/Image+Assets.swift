//
//  Image+Assets.swift
//  Phlex65
//
//  Created by TKxel on 27/05/2025.
//

import SwiftUI

// MARK: - Image Assets Extension
extension Image {

    // MARK: - Welcome Screen Assets
    // Add welcome screen images here when ready

    // MARK: - Authentication Assets
    // Add authentication screen images here when ready

    // MARK: - System Icons (SF Symbols)
    struct SystemIcons {
        static let checkmark = Image(systemName: "checkmark")
        static let xmark = Image(systemName: "xmark")
        static let eye = Image(systemName: "eye")
        static let eyeSlash = Image(systemName: "eye.slash")
        static let person = Image(systemName: "person")
        static let envelope = Image(systemName: "envelope")
        static let lock = Image(systemName: "lock")
        static let phone = Image(systemName: "phone")
        static let calendar = Image(systemName: "calendar")
        static let location = Image(systemName: "location")
        static let heart = Image(systemName: "heart")
        static let heartFill = Image(systemName: "heart.fill")
        static let star = Image(systemName: "star")
        static let starFill = Image(systemName: "star.fill")
        static let chevronRight = Image(systemName: "chevron.right")
        static let chevronLeft = Image(systemName: "chevron.left")
        static let chevronDown = Image(systemName: "chevron.down")
        static let chevronUp = Image(systemName: "chevron.up")
        static let plus = Image(systemName: "plus")
        static let minus = Image(systemName: "minus")
        static let gear = Image(systemName: "gear")
        static let bell = Image(systemName: "bell")
        static let bellFill = Image(systemName: "bell.fill")
    }
}
